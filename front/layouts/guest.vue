<template>
    <div
        class="card card-primary"
        style="width: 400px; margin: 7% auto;"
    >
        <div class="card-header">
            <h1 class="card-title mb-0 text-center h1">
                {{ $route.meta.public ? $route.meta.title : 'Auth required' }}
            </h1>
        </div>
        <div class="card-body">
            <slot />
        </div>
        <div class="card-footer text-center">
            <template v-if="!$route.meta.public">
                <router-link to="/sign-up">
                    Create new account
                </router-link>
                <br>
                <router-link to="/password-recovery">
                    Forgot password?
                </router-link>
            </template>
            <template v-else>
                <router-link to="/">
                    Sign in with existing account
                </router-link>
            </template>
        </div>
    </div>
</template>
