<template>
    <div>
        <main-menu />

        <main
            role="main"
            class="py-3"
        >
            <slot />
        </main>
    </div>
</template>

<script lang="ts">
import { MainMenu } from './../widgets'
import { defineComponent } from 'vue'

export default defineComponent({
    components: {
        MainMenu,
    },
})
</script>

<script lang="ts" setup>
// Composition API logic can be added here if needed in the future
</script>
