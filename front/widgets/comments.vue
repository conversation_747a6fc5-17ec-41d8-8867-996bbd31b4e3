<template>
    <div>
        <RichTable
            v-bind="richTable"
            :showTitle="false"
            :showTotal="false"
            showTableHeader
            showPagination
            @reload="onReload"
        >
            <template #createComment="{ refreshCallback }">
                <div class="input-group">
                    <input
                        ref="commentInput"
                        v-model="newComment"
                        type="text"
                        class="form-control"
                        placeholder="Enter comment"
                        @keydown.enter="createComment(refreshCallback)"
                    >
                    <button
                        type="button"
                        class="btn btn-success btn-sm"
                        @click="createComment(refreshCallback)"
                    >
                        <Icona name="icn-plus" />
                    </button>
                </div>
            </template>
            <template #comment="{row, refreshCallback}: {row: CommentRow, refreshCallback: () => void}">
                <InplaceEdit
                    v-if="row.is_editable"
                    :key="row.id"
                    :value="row.comment"
                    size="sm"
                    type="textarea"
                    showDelete
                    @submit="updateComment(row.id, $event, refreshCallback)"
                    @delete="deleteComment(row.id, refreshCallback)"
                />
                <span v-else>{{ row.comment }}</span>
            </template>
            <template #updatedAt="{ row }: {row: CommentRow}">
                <small class="text-muted">{{ row.updated_at }}</small>
            </template>
            <template #updatedBy="{ row }: {row: CommentRow}">
                <small class="text-muted">{{ row.updated_by }}</small>
            </template>
        </RichTable>
    </div>
</template>

<script lang="ts" setup>
import { nextTick, onMounted, ref } from 'vue'
import { Icona, InplaceEdit, RichTable } from '@/components'
import { RichTableType, Values } from '@/types'
import { useFetch } from '@/utils/fetch'

interface CommentRow {
    id: number
    comment: string
    updated_at: string
    updated_by: string
    is_editable: boolean
}

const $props = defineProps<{
    siteUser: { siteId: number, userId: number }
}>()

const $fetch = useFetch()

const richTable = ref<RichTableType>({} as RichTableType)
const newComment = ref('')
const commentInput = ref<HTMLInputElement>()

async function onReload (params: Values) {
    richTable.value = await $fetch('/finance/users/get-withdraw-comments', params)
    await nextTick()
    commentInput.value?.focus()
}

function appendSiteUserParams (params: Values) {
    return Object.assign({}, params, $props.siteUser)
}

async function createComment (refreshCallback: () => void) {
    await $fetch('/finance/users/create-withdraw-comment', appendSiteUserParams({ comment: newComment.value }))
    refreshCallback()
    newComment.value = ''
}

async function updateComment (id: number, comment: string, refreshCallback?: () => void) {
    await $fetch('/finance/users/update-withdraw-comment', appendSiteUserParams({ comment, id }))
    refreshCallback?.()
}

async function deleteComment (id: number, refreshCallback?: () => void) {
    await $fetch('/finance/users/delete-withdraw-comment', appendSiteUserParams({ id }))
    refreshCallback?.()
}

onMounted(() => {
    onReload($props.siteUser)
})
</script>
