<template>
    <nav class="navbar navbar-expand-md">
        <div class="container">
            <router-link
                class="navbar-brand"
                to="/"
            >
                <img
                    src="../favicon.ico"
                    width="24"
                    height="24"
                    class="d-inline-block align-top"
                    alt="Analytics"
                >
            </router-link>

            <button
                class="navbar-toggler"
                type="button"
                aria-expanded="false"
                aria-label="Toggle navigation"
                @click="onBurgerClick"
            >
                <span class="navbar-toggler-icon" />
            </button>

            <div
                ref="page-top-navbar"
                class="collapse navbar-collapse"
            >
                <ul class="navbar-nav me-auto">
                    <li
                        v-for="(group, i) in loggedUser?.menu || []"
                        class="nav-item "
                        :class="{
                            'dropdown': 'items' in group,
                        }"
                    >
                        <router-link
                            v-if="group.url"
                            class="nav-link"
                            :to="group.url"
                        >
                            {{ group.label }}
                        </router-link>
                        <a
                            v-else
                            :id="`page-top-dropdown-${i}`"
                            class="nav-link dropdown-toggle"
                            href="javascript:void(0)"
                            aria-haspopup="true"
                            aria-expanded="false"
                        >
                            {{ group.label }}
                        </a>

                        <div
                            class="dropdown-menu"
                            :class="i > 4 ? 'dropdown-menu-end dropdown-menu-xl-start' : ''"
                            :aria-labelledby="`page-top-dropdown-${i}`"
                        >
                            <template v-if="group.isMega">
                                <div class="row flex-nowrap">
                                    <div
                                        v-for="item in group.items"
                                        class="col"
                                    >
                                        <h4 class="dropdown-header">
                                            {{ item.label }}
                                        </h4>
                                        <ul class="list-unstyled">
                                            <li v-for="subItem in item.items">
                                                <router-link
                                                    v-if="subItem.url"
                                                    :key="subItem.url"
                                                    class="dropdown-item"
                                                    :to="subItem.url"
                                                >
                                                    {{ subItem.label }}
                                                </router-link>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </template>
                            <template v-else>
                                <template v-for="item in group.items" :key="item.url">
                                    <router-link
                                        v-if="item.url"
                                        class="dropdown-item"
                                        :to="item.url"
                                    >
                                        {{ item.label }}
                                    </router-link>
                                </template>
                            </template>
                        </div>
                    </li>
                </ul>

                <ul class="navbar-nav navbar-right">
                    <li
                        v-if="loggedUser?.user.isPasswordExpiring"
                        class="nav-item"
                    >
                        <router-link
                            to="/preferences/security"
                            class="nav-link btn btn-sm btn-danger"
                        >
                            Password has expired!
                        </router-link>
                    </li>

                    <li class="nav-item">
                        <a
                            href="javascript:void(0)"
                            class="nav-link"
                            @click="onEmployeeRequestPopupOpen()"
                        >
                            <icona
                                title="Contact us (feedback)"
                                :name="loggedUser?.lastRequest.hasFeedback ? 'icn-message-exclamation' : 'icn-message'"
                                size="lg"
                            />
                        </a>
                    </li>

                    <popup
                        v-bind="employeeRequestPopup"
                        @close="onEmployeeRequestPopupClose"
                    >
                        <form-grid
                            v-bind="employeeRequestForm"
                            @change="employeeRequestForm.values = $event"
                            @submit="onEmployeeRequestCreateFormSubmit"
                        />
                        <div v-if="loggedUser?.lastRequest">
                            <hr>
                            <h5>Last request:</h5>
                            <p>{{ loggedUser.lastRequest.message }}</p>
                            <h5>Last feedback:</h5>
                            <p>{{ loggedUser.lastRequest.feedback }}</p>
                        </div>
                    </popup>

                    <li class="nav-item">
                        <a
                            href="javascript:void(0)"
                            class="nav-link"
                            @click="onSwitchTheme"
                        ><icona
                            :name="isDarkTheme ? 'icn-moon' : 'icn-sun'"
                            class="icn-lg"
                        /></a>
                    </li>

                    <li class="nav-item dropdown">
                        <a
                            href="javascript:void(0)"
                            class="nav-link"
                        >
                            <span class="hidden-xs">{{ loggedUser?.user.shortName }}</span>
                        </a>

                        <div class="dropdown-menu dropdown-menu-end">
                            <router-link
                                to="/preferences/security"
                                class="dropdown-item"
                            >
                                Change password
                            </router-link>
                            <router-link
                                to="/preferences/settings"
                                class="dropdown-item"
                            >
                                Settings
                            </router-link>
                            <button
                                type="button"
                                class="dropdown-item pointer"
                                @click="onLogOut"
                            >
                                Exit (logout)
                            </button>
                            <div class="dropdown-divider" />
                            <p class="ps-4 pe-4 mb-0">
                                <small> Node: {{ loggedUser?.nodeName }}</small>
                            </p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'
import { FormGridType, Values } from '@/types'
import { FormGrid, Popup } from '@/components'
import Icona from '@/components/icona.vue'
import { useAuthUser } from '@/utils/auth-user'
import { useTheme } from '@/utils/theme'
import { useFetch } from '@/utils/fetch'
import { useRoute } from 'vue-router'
import { useProcessFormResponse } from '@/utils/process-form-response'

const authUser = useAuthUser()
const theme = useTheme()
const $fetch = useFetch()
const route = useRoute()
const $processFormResponse = useProcessFormResponse()

const collapsed = ref(true)
const employeeRequestPopup = ref({
    opened: false,
    title: '',
})
const employeeRequestForm = ref<FormGridType>({} as FormGridType)
const pageTopNavbar = ref<Element>()

const loggedUser = computed(() => {
    return authUser.loggedUser.value
})

const isDarkTheme = computed(() => {
    return theme.isDarkTheme.value
})

function onLogOut() {
    authUser.logout()
}

function onSwitchTheme() {
    theme.toggleTheme()
}

function onBurgerClick() {
    const el = pageTopNavbar.value
    if (collapsed.value) {
        el?.classList.add('show')
    } else {
        el?.classList.remove('show')
    }
    collapsed.value = !collapsed.value
}

function onEmployeeRequestPopupClose() {
    authUser.resetEmployeeRequestHasFeedback()
    employeeRequestPopup.value = { title: '', opened: false }
}

async function onEmployeeRequestPopupOpen() {
    authUser.hasFeedbackFlagReset()
    employeeRequestPopup.value = { title: 'Send feedback', opened: true }
    employeeRequestForm.value = await $fetch('/back/employees-requests/add-form')
}

async function onEmployeeRequestCreateFormSubmit(params: Values) {
    params.url = route.fullPath
    await $processFormResponse($fetch('/back/employees-requests/add', params), employeeRequestForm.value)
    employeeRequestPopup.value = { title: '', opened: false }
}
</script>

<style lang="scss">
    @media (min-width: 868px) {
        .navbar-expand-md .dropdown:hover > .dropdown-menu {
            display: block;
        }

        .navbar-expand-md .dropdown > .dropdown-toggle:active {
            /*Without this, clicking will make it sticky*/
            pointer-events: none;
        }

        .dropdown-menu .col {
            min-width: auto;
        }
    }
</style>
