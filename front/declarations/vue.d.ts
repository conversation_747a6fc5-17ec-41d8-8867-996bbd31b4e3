// https://stackoverflow.com/questions/64155229/add-global-properties-to-vue-3-using-typescript
import { DecodeParamsType, EncodeParamsType, FetchType, HistoryChangeParamsType, NormalizeParamsObjType, NotifyType, ProcessFormResponseType, ProcessSetFormType } from '@/types.ts'
import { InjectionKey } from 'vue'
import { $processFormResponse, $processRichTableResponse, $processResponse, $setForm } from '@/utils/response-processor'

declare module 'vue' {
    interface ComponentCustomProperties {
        apiUrl: string
        $fetch: FetchType
        $notify: NotifyType
        $route: RouteLocation
        $router: Router

        $processResponse: typeof $processResponse
        $processFormResponse: typeof $processFormResponse
        $processRichTableResponse: typeof $processRichTableResponse
        $setForm: typeof $setForm

        $normalizeParamsObj: NormalizeParamsObjType
        $historyReplaceParams: HistoryChangeParamsType
        $historyPushParams: HistoryChangeParamsType
        $encodeParams: EncodeParamsType
        $decodeParams: DecodeParamsType
    }
    export declare function inject<T>(key: InjectionKey<T>): T; // Hack to disable IDE warning that injected component may be undefined
}

export {}
