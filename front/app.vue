<template>
    <component
        :is="layout"
        role="main"
    >
        <template v-if="authUser.isGuest.value && !$route.meta.public">
            <SignIn />
        </template>
        <template v-else>
            <RouterView />
        </template>
    </component>
</template>

<script lang="ts" setup>
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthUser } from '@/utils/auth-user'
import { setOn401Callback } from '@/utils/fetch'
import MainLayout from './layouts/main.vue'
import GuestLayout from './layouts/guest.vue'
import SignIn from '@/apps/auth/sign-in.vue'

const authUser = useAuthUser()
const $route = useRoute()

const layoutMap = {
    main: MainLayout,
    guest: GuestLayout,
}

setOn401Callback(authUser.needLogin)

watch(() => $route.path, async (_to, from) => {
    if (from === undefined && !authUser.isGuest.value) { // fresh page load
        await authUser.reloadConfig()
    }
}, { immediate: true })

const layout = computed(() => {
    let layout: keyof typeof layoutMap

    if (authUser.isGuest.value) {
        layout = 'guest'
    } else {
        layout = ($route.meta.layout as keyof typeof layoutMap) || 'main'
    }

    return layoutMap[layout]
})
</script>
