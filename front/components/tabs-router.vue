<template>
    <div class="card">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li
                    v-for="tab in tabsWithIdAndActive"
                    :key="tab.id"
                    class="nav-item"
                >
                    <component
                        :is="disabled || tab.active ? 'span' : 'router-link'"
                        :to="Object.assign({}, tab.route)"
                        :disabled="disabled"
                        :class="{
                            'active': tab.active,
                            'position-relative': showClose && tab.active
                        }"
                        class="nav-link"
                    >
<!-- no whitespaces
                        -->{{ tab.title }}<!--
                        --><template v-if="tab.badge">
                            &nbsp;
                            <span class="badge align-middle bg-primary">
                                {{ tab.badge }}
                            </span>
                        </template><!--
                        --><button
                        v-if="showClose && tab.active"
                        class="btn btn-sm btn-close ms-1"
                        title="Close tab"
                        @click="onClose(tab.id)"
                        /><!--
                    -->
                    </component>
                </li>
                <slot name="afterTabs" />
            </ul>
        </div>

        <div class="card-body">
            <slot :tabId="activeTabId" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'
import { Tab } from '@/types'

const $props = withDefaults(defineProps<{
    tabs?: Tab[]
    tabIdRouteParamName: string
    emptyTabId?: string
    showClose?: boolean
    disabled?: boolean
}>(), {
    tabs: undefined,
    emptyTabId: undefined,
})

const $emit = defineEmits<{
    close: [tabId: string]
    change: [tabId: string | undefined]
}>()

const route = useRoute()

const activeTabId = computed(() => {
    return route.params[$props.tabIdRouteParamName] as string || $props.emptyTabId
})

const tabsWithIdAndActive = computed(() => {
    return ($props.tabs || []).map((tab: Tab) => {
        const id = tab.route.params![$props.tabIdRouteParamName]

        return Object.assign({ id, active: id === activeTabId.value }, tab)
    })
})

function onClose(tabId: string) {
    $emit('close', tabId)
}

watch(() => route.path, (to, from) => {
    if (to !== from) {
        $emit('change', activeTabId.value)
    }
}, { immediate: true })
</script>
