<template>
    <ul :class="{tree: level === 0}">
        <li
            v-for="node in collapsedTree"
            :key="node[keyName] as string"
        >
            <slot
                :node="node"
                name="content"
            />
            <Tree
                v-if="node.children"
                :tree="node.children"
                :collapseLimit="collapseLimit"
                :collapseGap="collapseGap"
                :keyName="keyName"
                :level="level + 1"
            >
                <template #content="slotProps: object">
                    <slot
                        v-bind="slotProps"
                        name="content"
                    />
                </template>
            </Tree>
        </li>
        <li v-if="collapsed && !expanded">
            <span
                class="tree-expand-link"
                @click="expanded = true"
            >expand {{ collapsed }} more</span>
        </li>
    </ul>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue'

type TreeNode = {
    [key: string]: any
    children?: TreeNode[]
}

const $props = withDefaults(defineProps<{
    tree?: TreeNode[]
    collapseLimit?: number
    collapseGap?: number
    keyName: string
    level?: number
}>(), {
    tree: () => [],
    collapseLimit: 20,
    collapseGap: 10,
    level: 0,
})

const expanded = ref(false)

const collapsed = computed(() => {
    const len = $props.tree.reduce((len: number, node: { children?: unknown }) => {
        return node.children ? len : len + 1
    }, 0)
    return len > $props.collapseLimit ? len - ($props.collapseLimit - $props.collapseGap) : 0
})

const collapsedTree = computed(() => {
    if (collapsed.value && !expanded.value) {
        return $props.tree.slice(0, $props.collapseLimit - $props.collapseGap)
    }
    return $props.tree
})
</script>
<style lang="scss">
.tree {
    // https://iamkate.com/code/tree-views/
    --tree-vertical-spacing: 1.35rem;
    --tree-horizontal-spacing: 1rem;
    --tree-marker-radius: 3px;
    --tree-line-width: 2px;
    --tree-line-color: var(--bs-tertiary-bg);

    padding-left: 6px;

    ul {
        margin-left: calc(var(--tree-marker-radius) - var(--tree-horizontal-spacing));
        padding-left: 0;
    }

    li {
        display: block;
        position: relative;
        padding-left: calc(2 * var(--tree-horizontal-spacing) - var(--tree-marker-radius) - var(--tree-line-width));
        border-left: var(--tree-line-width) solid var(--tree-line-color);
    }

    li:last-child {
        border-color: transparent;
    }

    li::before {
        content: '';
        display: block;
        position: absolute;
        top: calc(var(--tree-vertical-spacing) / -2);
        left: calc(var(--tree-line-width) * -1);
        width: calc(var(--tree-horizontal-spacing) + var(--tree-line-width));
        height: calc(var(--tree-vertical-spacing) + 1px);
        border: solid var(--tree-line-color);
        border-width: 0 0 var(--tree-line-width) var(--tree-line-width);
    }

    li::after {
        content: '';
        display: block;
        position: absolute;
        top: calc(var(--tree-vertical-spacing) / 2 - var(--tree-marker-radius));
        left: calc(var(--tree-horizontal-spacing) - var(--tree-marker-radius) - 1px);
        width: calc(2 * var(--tree-marker-radius));
        height: calc(2 * var(--tree-marker-radius));
        border-radius: 50%;
        background: var(--tree-line-color);
    }
}

.tree-expand-link {
    cursor: pointer;
    border-bottom-width: 1px;
    border-bottom-style: dashed;
    margin-bottom: 0.125rem;
    color: var(--bs-primary);
    &:hover {
        border-bottom-style: solid;
    }
}
</style>
