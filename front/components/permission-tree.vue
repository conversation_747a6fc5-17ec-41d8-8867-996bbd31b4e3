<template>
    <div class="d-flex">
        <h3>Permissions:</h3>
        <label v-if="visiblePermissions.length" class="h3 ms-3">
            <input
                type="checkbox"
                v-bind="isPermission(visiblePermissions[0])"
                @change="onPermissionChecked(($event.target as HTMLInputElement).checked, visiblePermissions[0].permission)"
            >
            {{ visiblePermissions[0].name }} &darr;
        </label>
        <slot name="afterTitle" />

        <label class="form-check form-switch form-check-inline form-check-input-sm h4 ms-auto">
            <input
                class="form-check-input"
                type="checkbox"
                @change="displayOnlyChecked=!displayOnlyChecked"
            >
            Show only assigned permissions
        </label>
    </div>
    <div v-if="visiblePermissions.length" class="row">
        <template v-for="parentNode in visiblePermissions[0].children">
            <div class="col-2">
                <label class="h3">
                    <input
                        type="checkbox"
                        v-bind="isPermission(parentNode)"
                        @change="onPermissionChecked(($event.target as HTMLInputElement).checked, parentNode.permission)"
                    >
                    {{ parentNode.name }}
                </label>
                <tree
                    :tree="parentNode.children"
                    :collapseLimit="40"
                    keyName="name"
                >
                    <template #content="{node}">
                        <label :title="node.nameWithParents">
                            <input
                                type="checkbox"
                                v-bind="isPermission(node)"
                                @change="onPermissionChecked(($event.target as HTMLInputElement).checked, node.permission)"
                            >
                            {{ node.name }}
                        </label>
                    </template>
                </tree>
            </div>
        </template>
    </div>
</template>

<script lang="ts">
import { Tree } from '@/components'
import { defineComponent, PropType } from 'vue'

export interface PermissionNode {
    permission: string,
    nameWithParents: string,
    name: string,
    direct: boolean,
    indirect: boolean,
    childrenHasPermission: boolean,
    children?: PermissionNode[]
}

function visiblePermissionsRecursive(nodes: PermissionNode[]) {
    const result: PermissionNode[] = []

    nodes.forEach((vOrig: PermissionNode) => {
        const v = Object.assign({}, vOrig)
        if (v.children) {
            v.children = (v.direct || v.indirect) ? undefined : visiblePermissionsRecursive(v.children)
        }

        if ((v.direct || v.indirect) || v?.children?.length) {
            result.push(v)
        }
    })

    return result
}

export default defineComponent({
    components: {
        Tree,
    },
    props: {
        permissions: {
            type: Array as PropType<PermissionNode[]>,
            required: true,
        },
    },
    emits: ['onPermissionChecked'],
    data()  {
        return {
            displayOnlyChecked: false,
        }
    },

    computed: {
        visiblePermissions() {
            return this.displayOnlyChecked ? visiblePermissionsRecursive(this.permissions) : this.permissions
        },
    },

    methods: {
        isPermission ({ direct, indirect }: PermissionNode) {
            return {
                checked: indirect || direct,
                disabled: indirect && !direct,
            }
        },
        onPermissionChecked (checked: boolean, permission: string) {
            this.$emit('onPermissionChecked', checked, permission)
        },
    },
})
</script>
