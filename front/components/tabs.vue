<template>
    <div
        class="card"
        :class="tabsStyle"
    >
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs">
                <li
                    v-for="(tab, tabIndex) in tabs"
                    class="nav-item"
                >
                    <router-link
                        v-if="tab.route"
                        exact-active-class="active"
                        :to="tab.route"
                        class="nav-link"
                    >
                        {{ tab.title }}
                        <span
                            v-if="tab.badge"
                            class="badge bg-primary align-middle"
                        >{{ tab.badge }}</span>
                    </router-link>
                    <a
                        v-else
                        href="javascript:void(0);"
                        class="nav-link"
                        :class="tabIndex === activeTabIndex ? 'active' : ''"
                        @click.prevent="activateTab(tabIndex)"
                    >{{ tab.title }} <span
                        v-if="tab.badge"
                        class="badge bg-primary align-middle"
                    >{{ tab.badge }}</span></a>
                </li>
                <slot name="afterTabs" />
            </ul>
        </div>

        <div class="card-body">
            <slot :tab="activeTab" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { Tab } from '@/types'

const $props = withDefaults(defineProps<{
    tabs?: Tab[]
    tabsStyle?: string
    activeTabIndex?: number
}>(), {
    tabs: () => [],
    tabsStyle: 'tab-primary',
    activeTabIndex: 0,
})

const $emit = defineEmits<{
    'update:activeTabIndex': [index: number]
    onActivateTab: [index: number]
}>()

const activeTab = computed(() => {
    if ($props.activeTabIndex < $props.tabs.length) {
        return $props.tabs[$props.activeTabIndex]
    }
    return {}
})

function activateTab(index: number) {
    $emit('update:activeTabIndex', index)
    $emit('onActivateTab', index)
}
</script>
