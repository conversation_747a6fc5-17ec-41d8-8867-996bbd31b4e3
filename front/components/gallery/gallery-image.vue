<template>
    <div class="gallery-image-block position-relative d-inline-block">
        <a
            :href="doc.src"
            class="gallery-trigger"
        >
            <img
                class="orientation-from-image"
                :alt="title"
                :src="doc.src"
                :title="title"
            >
        </a>
        <template v-if="boxes && boxes.length && doc.width && doc.height">
            <div
                v-for="b in boxes"
                class="position-absolute border border-danger"
                :style="{
                    'pointer-events': 'none',
                    'left': (b[0] / doc.width * 100) + '%',
                    'top': (b[1] / doc.height * 100) + '%',
                    'width': ((b[2] - b[0]) / doc.width * 100) + '%',
                    'height': ((b[3] - b[1]) / doc.height * 100) + '%',
                }"
            />
        </template>
        <slot />
    </div>
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'

export interface ImageAttributes {
    src: string,
    width?: number,
    height?: number
}

export default defineComponent({
    props: {
        doc: {
            type: Object as PropType<ImageAttributes>,
            required: true,
        },
        boxes: {
            type: Array as PropType<number[][] | null>,
            default: () => null,
        },
        title: {
            type: String,
            default: '',
        },
    },
})
</script>
<style lang="scss">
.gallery-image-block {
    font-size: 0.9em;
    img {
        image-orientation: from-image;
        height: 300px;
    }
    &:hover *:not(:disabled) {
        opacity: 1;
    }
    *:hover {
        z-index: 1;
    }
    .btn:focus {
        box-shadow: none;
    }
}
</style>
