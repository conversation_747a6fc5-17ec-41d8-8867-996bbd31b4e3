<template>
    <dropdown
        ref="dropdown"
        :items="list"
        :value="value"
        :groups="groups"
        :placeholder="placeholder"
        :multiple="multiple"
        :size="size"
        :enabled="enabled"
        :isInvalid="isInvalid"
        :allowSearch="allowSearch"
        :allowToggleAll="allowToggleAll"
        :allowToggleNone="allowToggleNone"
        :allowLiveSearch="allowLiveSearch"
        :liveSearchMinLength="liveSearchMinLength"
        :liveSearchTimeout="liveSearchTimeout"
        :liveSearchNameAsId="liveSearchNameAsId"
        :liveSearchItems="liveSearchItems"
        @input="handleChange"
        @liveSearch="handleLiveSearch"
    />
</template>

<script>
import Dropdown from './../../dropdown.vue'
import { defineComponent } from 'vue'

export default defineComponent({

    components: {
        Dropdown,
    },
    inheritAttrs: false,

    props: {
        enabled: {
            type: Boolean,
            default: true,
        },
        list: {
            type: Array,
            default: () => [],
        },
        value: {
            type: [String, Number, Array, Boolean],
            default: null,
        },
        placeholder: {
            type: String,
            default: '...',
        },
        selectorNone: {
            type: Boolean,
            default: true,
        },
        groups: {
            type: Array,
            default: () => [],
        },
        menuRight: {
            type: Boolean,
            default: false,
        },
        multiple: {
            type: Boolean,
            default: true,
        },
        size: {
            type: String,
            default: 'md',
        },
        isInvalid: {
            type: Boolean,
            default: false,
        },
        allowLiveSearch: {
            type: Boolean,
            default: false,
        },
        liveSearchNameAsId: {
            type: Boolean,
            default: false,
        },
        liveSearchTimeout: {
            type: Number,
            default: 300,
        },
        liveSearchMinLength: {
            type: Number,
            default: 1,
        },
        liveSearchItems: {
            type: Array,
            default: () => ([]),
        },
        allowSearch: {
            type: Boolean,
            default: true,
        },
        allowToggleAll: {
            type: Boolean,
            default: true,
        },
        allowToggleNone: {
            type: Boolean,
            default: true,
        },
    },

    emits: ['change', 'liveSearch'],

    methods: {
        handleChange (change) {
            this.$emit('change', change)
        },
        handleLiveSearch (params) {
            this.$emit('liveSearch', params)
        },
        focus () {
            this.$nextTick(() => this.$refs.dropdown.openDropdown())
        },
    },
})
</script>
