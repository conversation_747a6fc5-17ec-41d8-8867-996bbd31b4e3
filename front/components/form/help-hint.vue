<template>
    <popover>
        <button
            v-if="showEdit"
            type="button"
            class="btn btn-link help-hint me-3"
            :class="{'help-hint-empty': (description || '').length === 0}"
        >
            {{ description || 'Empty' }}
        </button>

        <template #content>
            <div
                class="input-group"
                style="width: 250px;"
            >
                <input
                    v-model="editText"
                    class="form-control flex-grow-1"
                    type="text"
                >
                <button
                    type="button"
                    class="btn btn-primary"
                    @click="onSubmitEdit"
                >
                    <icona name="icn-save" />
                </button>
            </div>
        </template>
    </popover>
</template>

<script lang="ts" setup>
import Icona from '@/components/icona.vue'
import Popover from '@/components/popover.vue'
import { watch } from 'vue'

const $props = withDefaults(defineProps<{
    description?: string
    showEdit?: boolean
}>(), {
    description: undefined,
})

const $emit = defineEmits<{
    submit: [value: typeof $props.description]
}>()

const editText = defineModel<typeof $props.description>()

watch(() => $props.description, (description) => {
    editText.value = description
}, { immediate: true})

function onSubmitEdit () {
    $emit('submit', editText.value)
}
</script>

<style>
    .help-hint {
        border-bottom: dashed 1px var(--bs-primary);
    }

    .help-hint-empty {
        font-style: italic;
        color: var(--bs-danger);
        border-bottom-color: var(--bs-danger);
    }
</style>
