<template>
    <button
        ref="button"
        v-mousedown-outside="onMousedownOutside"
        type="button"
        class="btn form-control btn-outline-secondary dropdown-btn"
        :class="{'disabled': !enabled, 'is-invalid': isInvalid, [`btn-${size}`]: true}"
        :tabindex="tabindex"
        :disabled="!enabled"
        @click="onButtonClick"
        @keydown="onbButtonKeyDown"
    >
        <slot>
            <div class="d-flex justify-content-between">
                <span class="dropdown-text d-inline-block me-1">{{ displayValue }}</span>
                <i><icona name="icn-caret-down" /></i>
            </div>
        </slot>
    </button>

    <div
        ref="menu"
        class="dropdown-menu dropdown-menu-select"
        :style="{'max-height': `${menuMaxHeight}px`}"
        :class="{'dropdown-menu-end': menuRight, show: opened}"
        @keydown="onNavigate"
        @click="onMenuClick"
    >
        <input
            v-if="allowSearch"
            ref="input"
            v-model="searchValue"
            type="text"
            class="form-control form-control-sm m-1 d-flex w-auto"
        >

        <div
            v-if="(allowToggleAll || allowToggleNone) && multiple"
            class="btn-group btn-group-sm m-1"
            style="flex: none"
            tabindex="0"
        >
            <button
                v-if="allowToggleAll"
                type="button"
                class="btn btn-secondary w-50"
                @click="toggleAll"
            >
                All
            </button>
            <button
                v-if="allowToggleNone"
                type="button"
                class="btn btn-secondary w-50"
                @click="toggleNone"
            >
                None
            </button>
        </div>

        <div
            ref="itemsContainer"
            class="dropdown-menu-items-container"
            tabindex="0"
        >
            <template v-for="item in groupedItemList">
                <a
                    v-if="item.type === 'item'"
                    :key="item.id"
                    class="dropdown-item"
                    :class="itemClass(item)"
                    @click.prevent="onMenuItemClick(item)"
                >
                    <span class="text">
                        {{ item.name }}<template v-if="item.count"> ({{ item.count }})</template>
                    </span>

                    <span
                        v-if="isItemActive(item)"
                        class="check-mark ms-2"
                    />
                </a>

                <button
                    v-else-if="item.type === 'group'"
                    :key="item.name"
                    type="button"
                    class="my-1 mx-3"
                    :class="groupButtonClass(item)"
                    @click="!multiple || toggleGroup(item.name)"
                >
                    {{ item.name }}
                </button>

                <div
                    v-else-if="item.type === 'divider'"
                    class="dropdown-divider"
                />
            </template>

            <div
                v-if="groupedItemList.length === 0"
                class="m-1"
            >
                <div
                    v-if="allowLiveSearch"
                    class="text-center font-italic"
                >
                    Please use live search
                </div>
                <div
                    v-else
                    class="text-center font-italic"
                >
                    No items were found
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onBeforeUnmount, ref, watch } from 'vue'
import { createPopper } from '@popperjs/core'
import Icona from '@/components/icona.vue'

function getValuesWithToggleGroup (toggle: any[], current: any[]) {
    const containingItems = toggle.filter((toggleItem) => current.some((value) => isSame(toggleItem.id, value.id)))
    // if group has been toggled and all items from this group is selected then all of them must be deselected
    if (containingItems.length === toggle.length) {
        return current.filter((oldItem) => !toggle.some((value) => isSame(oldItem.id, value.id)))
    }
    // if group has been toggled and some or none items from this group is selected then every other must be put into new values
    const applyItems = toggle
        .filter((toggleItem) => !containingItems.some((value) => isSame(toggleItem.id, value.id)))
    const newItems = Array.from(current)
    newItems.push(...applyItems)

    return newItems
}

function getValuesWithToggle (toggle: any, current: any[]) {
    const oldValueIdx = current.findIndex((value) => isSame(toggle.id, value.id))
    const newValues = Array.from(current)

    if (oldValueIdx !== -1) {
        newValues.splice(oldValueIdx, 1)
    } else {
        newValues.unshift(toggle)
    }

    return newValues
}

function isSame (val1: any, val2: any) {
    val1 = val1 === undefined ? null : val1
    val2 = val2 === undefined ? null : val2

    val1 = typeof val1 === "boolean" ? Number(val1) : val1 // casting to int selected items when they are boolean (for false / true drop down lists)
    val2 = typeof val2 === "boolean" ? Number(val2) : val2

    return val1 === val2 ||
        (!(val1 === null || val2 === null) &&
            ('' + val1) === ('' + val2))
}

function normalizeValue (value: any) {
    if (value === undefined || value === null || value === '') {
        return []
    }

    if (!Array.isArray(value)) {
        return [value]
    }

    return value
}

defineOptions({
    inheritAttrs: false,
})
const $props = withDefaults(defineProps<{
    enabled?: boolean
    isInvalid?: boolean
    items?: any[]
    groups?: any[]
    menuRight?: boolean
    value?: any[] | string | number | boolean | null
    allowSearch?: boolean
    allowToggleAll?: boolean
    allowToggleNone?: boolean
    searchCallback?: (params: { searchValue: string, items: any[], currentValue: any[] }) => any[]
    multiple?: boolean
    size?: string
    tabindex?: number
    menuMaxHeight?: number
    menuMinHeight?: number
    allowSelectNone?: boolean
    placeholder?: string
    searchItems?: any[]
    allowLiveSearch?: boolean
    liveSearchTimeout?: number
    liveSearchMinLength?: number
    liveSearchNameAsId?: boolean
    placement?: string
}>(), {
    enabled: true,
    isInvalid: false,
    items: () => [],
    groups: () => [],
    menuRight: false,
    value: null,
    allowSearch: true,
    allowToggleAll: true,
    allowToggleNone: true,
    searchCallback: ({ searchValue, items }) => {
        searchValue = searchValue.toLowerCase()

        return items.filter(
            ({ name }) => name !== null &&
                name !== undefined &&
                (name + '').length !== 0 &&
                (name + '').toLowerCase().includes(searchValue),
        )
    },
    multiple: true,
    size: 'md',
    tabindex: 0,
    menuMaxHeight: 550,
    menuMinHeight: 300,
    allowSelectNone: true,
    placeholder: '...',
    searchItems: () => [],
    allowLiveSearch: false,
    liveSearchTimeout: 300,
    liveSearchMinLength: 1,
    liveSearchNameAsId: false,
    placement: 'bottom-start',
})

const $emit = defineEmits<{
    liveSearch: [params: any]
    input: [value: any, toggleValue?: any]
}>()

// Template refs
const button = ref<HTMLElement>()
const menu = ref<HTMLElement>()
const input = ref<HTMLInputElement>()
const itemsContainer = ref<HTMLElement>()

// Reactive data
const searchValue = ref('')
const lastSearchTime = ref(0)
const opened = ref(false)
const focusedItem = ref<any>(null)
const selectedItems = ref<any[]>([])
let popper: any = null
// Computed properties
const normalizedValue = computed(() => {
    return normalizeValue($props.value)
})

const plainItems = computed(() => {
    return JSON.parse(JSON.stringify($props.items))
})

const itemList = computed(() => {
    if ($props.allowLiveSearch) {
        return mergeItemsWithoutDuplications($props.searchItems, selectedItems.value)
    }
    // Hack to prevent VueJS reactivity for nested objects (speed up for data manipulations)
    return plainItems.value
})

const groupedItemList = computed(() => {
    const items = searchValue.value.length === 0
        ? itemList.value
        : $props.searchCallback.call(null, {
            items: itemList.value,
            searchValue: searchValue.value,
            currentValue: normalizedValue.value,
        })
    const mergedItems = mergeItemsWithoutDuplications(items, selectedItems.value)

    if ($props.groups.length === 0) {
        return mergedItems.map((item) => Object.assign({ type: 'item' }, item))
    }

    const groupsWithItems: any = {}
    for (const { name: groupName } of $props.groups) {
        groupsWithItems[groupName] = []
    }
    groupsWithItems.Other = []

    for (const item of mergedItems) {
        if (item.group in groupsWithItems) {
            groupsWithItems[item.group].push(item)
        } else {
            groupsWithItems.Other.push(item)
        }
    }

    const result: any[] = []
    const groupsWithOthers = $props.groups.find((g: any) => g.name === 'Other') ? $props.groups : [...$props.groups, { name: 'Other', style: 'secondary' }]
    for (const [i, group] of groupsWithOthers.entries()) {
        const groupItems = groupsWithItems[group.name]

        if (groupItems.length === 0) {
            continue
        }

        if (i > 0) {
            result.push({
                type: 'divider',
            })
        }

        result.push({
            type: 'group',
            name: group.name,
            style: group.style,
        })

        groupItems.forEach((item: any) =>
            result.push(Object.assign({ type: 'item' }, item)),
        )
    }

    return result
})

const displayValue = computed(() => {
    return selectedItems.value
        .map(({ name }) => name)
        .join(', ') || $props.placeholder
})
    watch: {
        searchValue (newVal) {
            if (this.allowLiveSearch && newVal.length >= this.liveSearchMinLength) {
                this.lastSearchTime = Date.now()

                setTimeout(() => {
                    if (Date.now() >= this.lastSearchTime + this.liveSearchTimeout) {
                        this.search({ q: newVal })
                    }
                }, this.liveSearchTimeout)
            }
        },
        value: {
            handler (newValue) {
                const normalized = normalizeValue(newValue)

                if (normalized.length > 0 && this.allowLiveSearch && this.searchItems.length === 0) {
                    if (this.liveSearchNameAsId) {
                        this.selectedItems = Object.values(normalized).map((id) => {
                            return { id, name: id }
                        }, {})
                    } else {
                        // reverse search for values by their ids
                        this.search({ ids: normalized })
                    }
                } else {
                    this.selectedItems = this.itemList
                        .filter(({ id }) => normalized.some((value) => isSame(id, value)))
                }
            },
            immediate: true,
        },
        items (items) {
            if (!this.allowLiveSearch) {
                const normalized = normalizeValue(this.value)
                this.selectedItems = items
                    .filter(({ id }) => normalized.some((value) => isSame(id, value)))
            }
        },
        searchItems (searchItems) {
            const items = this.mergeItemsWithoutDuplications(searchItems, this.selectedItems)

            this.selectedItems = items.filter(({ id }) => this.normalizedValue.some((value) => isSame(value, id)))
        },
    },
    beforeUnmount () {
        if (this.popper) {
            this.popper.destroy()
        }
    },
    methods: {
        toggle (toggle, group = false) {
            let newValue = []
            let selectedItems
            let toggleValue

            toggle = normalizeValue(toggle)
                .filter(({ disabled }) => !disabled)

            if (toggle.length === 0) {
                return
            }

            selectedItems = group
                ? getValuesWithToggleGroup(toggle, this.selectedItems)
                : getValuesWithToggle(toggle[0], this.selectedItems)

            if (!this.allowSelectNone && selectedItems.length === 0) {
                return
            }

            if (this.multiple) {
                newValue = selectedItems.map(({ id }) => id)
                toggleValue = toggle.map(({ id }) => id)
            } else {
                if (undefined === selectedItems[0]) {
                    newValue = null
                    selectedItems = []
                } else {
                    newValue = selectedItems[0].id
                    selectedItems = [selectedItems[0]]
                }
                toggleValue = toggle ? toggle.id : null
            }
            this.selectedItems = selectedItems
            this.$emit(
                'input',
                newValue,
                toggleValue,
            )
        },
        toggleGroup (toggleGroup) {
            this.toggle(
                this.itemList
                    .filter(({ group }) => (group || 'Other') === toggleGroup),
                true,
            )
        },
        toggleAll () {
            const value = this.itemList.map(({ id }) => id)
            const toggle = value.filter(
                (newVal) => !this.normalizedValue.some(
                    (curVal) => isSame(newVal, curVal),
                ),
            )

            this.focusInput()
            this.selectedItems = this.itemList

            this.$emit('input', value, toggle)
        },
        toggleNone () {
            this.focusInput()
            this.selectedItems = []
            this.$emit('input', [], this.normalizedValue)
        },
        itemClass (item) {
            const classObject = {}

            switch (item.type) {
                case 'group':
                    classObject['dropdown-header'] = true
                    break
                case 'divider':
                    classObject.divider = true
                    break
                case 'item':
                    classObject.selected = this.isItemActive(item)
                    classObject.disabled = item.disabled
                    classObject.focus = this.focusedItem && item.id === this.focusedItem.id
                    break
            }
            return classObject
        },
        groupButtonClass ({ style }) {
            const classList = ['btn', 'btn-xs']

            if (style) {
                classList.push(`btn-${style}`)
            }
            if (!this.multiple) {
                classList.push('disabled')
            }

            return classList
        },
        isItemActive ({ type, id }) {
            if (type !== 'item') {
                return false
            }
            id = id === undefined ? null : id

            return this.normalizedValue
                .some((value) => isSame(value, id))
        },
        focusInput (selectText) {
            selectText = selectText === undefined ? false : selectText

            const input = this.$refs.input

            if (input === undefined) {
                return false
            }

            input.focus()
            if (selectText && input.value) {
                input.select()
            }

            return true
        },
        mergeItemsWithoutDuplications (list1, list2) {
            return [
                ...list2
                    .filter(({ id }) => !list1.some(({ id: itemId }) => isSame(itemId, id))),
                ...list1,
            ]
        },
        focusMenuItem (item) {
            this.focusedItem = (!item || item.disabled) ? null : item
        },
        initPopper () {
            this.popper = createPopper(this.$refs.button, this.$refs.menu, {
                placement: this.placement,

                onFirstUpdate: () => {
                    if (!this.focusInput(true)) {
                        this.$refs.itemsContainer.focus()
                    }
                },

                modifiers: [
                    { name: 'flip', options: { allowedAutoPlacements: ['top-end', 'top-start', 'top', 'bottom', 'bottom-start', 'bottom-end'] } },
                    // keeps menu in same with as reference if reference is larger then menu
                    {
                        name: 'sameWidth',
                        enabled: true,
                        phase: 'beforeWrite',
                        requires: ['computeStyles'],
                        fn: ({ state }) => {
                            if (this.$refs.menu.getBoundingClientRect().width < state.rects.reference.width) {
                                state.styles.popper.width = `${state.rects.reference.width}px`
                            }
                        },
                    },
                ],
            },
            )
        },
        openDropdown (opened = true) {
            if (this.opened === opened) {
                return
            }

            if (opened) {
                // wait for menu mounted in dom for proper initial positioning
                this.$nextTick(() => {
                    this.initPopper()
                    this.focusMenuItem(this.groupedItemList.find(it => it.type === 'item'))
                })
            } else if (this.popper) {
                // searchValue reset disabled because of strange bug in /back/users-statuses-vip-thresholds Add new Popover when Country filtered by text input and then selected by mouse. Whole popover suddenly disappears
                // this.searchValue = ''
                this.popper.destroy()
                this.popper = null
            }

            this.opened = opened
        },
        search (params = {}) {
            this.$emit('liveSearch', params)
        },
        onButtonClick () {
            if (!this.enabled) {
                return
            }

            this.openDropdown(!this.opened)
        },
        onbButtonKeyDown (e) {
            if (['ArrowUp', 'ArrowDown'].includes(e.key)) {
                this.openDropdown()
            }
        },
        onMousedownOutside (e) {
            this.$nextTick(() => {
                if (this.$refs.button !== e.target &&
                    this.$refs.menu &&
                    this.$refs.menu !== e.target &&
                    !this.$refs.menu.contains(e.target)) {
                    this.openDropdown(false)
                }
            })
        },
        onMenuClick () {
            if (this.allowSearch) {
                this.focusInput()
            }
        },
        onMenuItemClick (item) {
            if (item.disabled) {
                return
            }
            this.toggle(item)
            this.focusMenuItem(item)
            if (!this.multiple) {
                this.openDropdown(false)
            }
        },
        onNavigate (event) {
            let offset

            if (event.key === 'ArrowDown') {
                offset = 1
            } else if (event.key === 'ArrowUp') {
                offset = -1
            } else if (event.key === 'Enter') {
                if (this.opened && this.itemList.length > 0) {
                    this.toggle(this.focusedItem)
                    if (!this.multiple) {
                        this.openDropdown(false)
                    }
                } else {
                    this.openDropdown(true)
                }
                return false
            } else if (event.key === 'Tab' || event.key === 'Escape') {
                this.openDropdown(false)
                return
            } else {
                return false
            }
            event.preventDefault()

            const items = this.groupedItemList.filter(it => it.type === 'item')

            if (items.length === 0) {
                this.focusMenuItem(null)
                return false
            }

            const idx = items.findIndex(it => this.focusedItem && it.id === this.focusedItem.id)

            if (idx + offset === items.length) {
                return false
            }

            if (idx === -1 || (idx === 0 && offset === -1) || items.length === 1) {
                this.focusMenuItem(items[0])
                return false
            }
            const newIdx = idx + offset
            this.focusMenuItem(items[newIdx])

            return false
        },
    },
})
</script>

<style lang="scss">
    .dropdown-menu-select {
        flex-direction: column;
        padding: 0;

        &.show {
            display: flex !important;
        }

        .focus {
            text-decoration: none;
            background-color: var(--bs-secondary-bg);
        }

        & > .dropdown-menu-items-container {
            flex: 1;
            overflow-y: auto;

            & > .dropdown-item {
                padding-right: 50px;
                padding-left: 2rem;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                & > .check-mark {
                    margin-right: -30px;
                    position: initial;
                    display: inline-block;

                    &::after {
                        content: '';
                        display: block;
                        width: 0.5em;
                        height: 1em;
                        border-style: solid;
                        border-width: 0 0.26rem 0.26rem 0;
                        transform: rotate(45deg);
                    }
                }

                &.disabled {
                    color: var(--bs-btn-disabled-color);
                    cursor: not-allowed;
                }
            }
        }
    }

    .dropdown-btn {
        width: 100%;
        overflow-x: hidden;

        .dropdown-text {
            overflow: hidden;
        }
    }

    .input-group .dropdown-btn {
        flex-grow: 1;
        width: 1%;
    }
</style>
