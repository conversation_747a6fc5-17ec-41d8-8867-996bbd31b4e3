<template>
    <button
        ref="button"
        v-mousedown-outside="onMousedownOutside"
        type="button"
        class="btn form-control btn-outline-secondary dropdown-btn"
        :class="{'disabled': !enabled, 'is-invalid': isInvalid, [`btn-${size}`]: true}"
        :tabindex="tabindex"
        :disabled="!enabled"
        @click="onButtonClick"
        @keydown="onbButtonKeyDown"
    >
        <slot>
            <div class="d-flex justify-content-between">
                <span class="dropdown-text d-inline-block me-1">{{ displayValue }}</span>
                <Icona name="icn-caret-down" />
            </div>
        </slot>
    </button>

    <div
        ref="menu"
        class="dropdown-menu dropdown-menu-select"
        :style="{'max-height': `${menuMaxHeight}px`}"
        :class="{show: opened}"
        @keydown="onNavigate"
        @click="onMenuClick"
    >
        <input
            v-if="allowSearch"
            ref="input"
            v-model="searchValue"
            type="text"
            class="form-control form-control-sm m-1 d-flex w-auto"
        >

        <div
            v-if="(allowToggleAll || allowToggleNone) && multiple"
            class="btn-group btn-group-sm m-1"
            style="flex: none"
            tabindex="0"
        >
            <button
                v-if="allowToggleAll"
                type="button"
                class="btn btn-secondary w-50"
                @click="toggleAll"
            >
                All
            </button>
            <button
                v-if="allowToggleNone"
                type="button"
                class="btn btn-secondary w-50"
                @click="toggleNone"
            >
                None
            </button>
        </div>

        <div
            ref="itemsContainer"
            class="dropdown-menu-items-container"
            tabindex="0"
        >
            <template v-for="item in groupedItemList">
                <a
                    v-if="item.type === 'item'"
                    :key="item.id"
                    class="dropdown-item"
                    :class="itemClass(item)"
                    @click.prevent="onMenuItemClick(item)"
                >
                    <span class="text">
                        {{ item.name }}<template v-if="item.count"> ({{ item.count }})</template>
                    </span>

                    <span
                        v-if="isItemActive(item)"
                        class="check-mark ms-2"
                    />
                </a>

                <button
                    v-else-if="item.type === 'group'"
                    :key="item.name"
                    type="button"
                    class="my-1 mx-3"
                    :class="groupButtonClass(item)"
                    @click="!multiple || toggleGroup(item.name)"
                >
                    {{ item.name }}
                </button>

                <div
                    v-else-if="item.type === 'divider'"
                    class="dropdown-divider"
                />
            </template>

            <div
                v-if="groupedItemList.length === 0"
                class="m-1"
            >
                <div
                    v-if="allowLiveSearch"
                    class="text-center font-italic"
                >
                    Please use live search
                </div>
                <div
                    v-else
                    class="text-center font-italic"
                >
                    No items were found
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, onBeforeUnmount, ref, watch } from 'vue'
import { createPopper, Instance as PopperInstance } from '@popperjs/core'
import Icona from '@/components/icona.vue'
import { BsSize, Item, ItemGroup } from '@/types'

type SingleValue = Item['id']
type AnyValue = SingleValue | SingleValue[] | null

interface TypedItem extends Item {
    type: 'item'
}

interface TypedGroup {
    type: 'group'
    name: string
    style?: string
}

interface TypedDivider {
    type: 'divider'
}

type DropdownItems = (TypedItem | TypedGroup | TypedDivider)[]

function getItemsWithToggleGroup (toggle: Item[], current: Item[]): Item[] {
    const containingItems = toggle.filter((toggleItem) => current.some((value) => isSame(toggleItem.id, value.id)))
    // if group has been toggled and all items from this group is selected then all of them must be deselected
    if (containingItems.length === toggle.length) {
        return current.filter((oldItem) => !toggle.some((value) => isSame(oldItem.id, value.id)))
    }
    // if group has been toggled and some or none items from this group is selected then every other must be put into new values
    const applyItems = toggle
        .filter((toggleItem) => !containingItems.some((value) => isSame(toggleItem.id, value.id)))
    const newItems = Array.from(current)
    newItems.push(...applyItems)

    return newItems
}

function getItemsWithToggle (toggle: Item, current: Item[]): Item[] {
    const oldValueIdx = current.findIndex((value) => isSame(toggle.id, value.id))
    const newValues = Array.from(current)

    if (oldValueIdx !== -1) {
        newValues.splice(oldValueIdx, 1)
    } else {
        newValues.unshift(toggle)
    }

    return newValues
}

function isSame (val1: unknown, val2: unknown): boolean {
    val1 = val1 === undefined ? null : val1
    val2 = val2 === undefined ? null : val2

    val1 = typeof val1 === "boolean" ? Number(val1) : val1 // casting to int selected items when they are boolean (for false / true drop down lists)
    val2 = typeof val2 === "boolean" ? Number(val2) : val2

    return val1 === val2 ||
        (!(val1 === null || val2 === null) &&
            ('' + val1) === ('' + val2))
}

function normalizeValue (value: AnyValue): SingleValue[] {
    if (value === undefined || value === null || value === '') {
        return []
    }

    if (!Array.isArray(value)) {
        return [value as SingleValue]
    }

    return value
}

defineOptions({
    inheritAttrs: false,
})
const $props = withDefaults(defineProps<{
    enabled?: boolean
    isInvalid?: boolean
    items?: Item[]
    groups?: ItemGroup[]
    value?: AnyValue
    allowSearch?: boolean
    allowToggleAll?: boolean
    allowToggleNone?: boolean
    multiple?: boolean
    size?: BsSize
    tabindex?: number
    menuMaxHeight?: number
    menuMinHeight?: number
    placeholder?: string
    allowLiveSearch?: boolean
    liveSearchItems?: Item[]
    liveSearchTimeout?: number
    liveSearchMinLength?: number
    liveSearchNameAsId?: boolean
    placement?: string
}>(), {
    enabled: true,
    isInvalid: false,
    items: () => [],
    groups: () => [],
    value: null,
    allowSearch: true,
    allowToggleAll: true,
    allowToggleNone: true,
    multiple: true,
    size: 'md',
    tabindex: 0,
    menuMaxHeight: 550,
    menuMinHeight: 300,
    placeholder: '...',
    allowLiveSearch: false,
    liveSearchItems: () => [],
    liveSearchTimeout: 300,
    liveSearchMinLength: 1,
    liveSearchNameAsId: false,
    placement: 'bottom-start',
})

const $emit = defineEmits<{
    liveSearch: [params: Record<string, unknown>]
    input: [value: AnyValue, toggleValue?: AnyValue]
}>()

// Template refs
const button = ref<HTMLElement>()
const menu = ref<HTMLElement>()
const input = ref<HTMLInputElement>()
const itemsContainer = ref<HTMLElement>()

// Reactive data
const searchValue = ref('')
const lastSearchTime = ref(0)
const opened = ref(false)
const focusedItem = ref<Item>()
const selectedItems = ref<Item[]>([])
let popper: PopperInstance|undefined

// Computed properties
const normalizedValue = computed((): SingleValue[] => {
    return normalizeValue($props.value)
})

const plainItems = computed((): Item[] => {
    return JSON.parse(JSON.stringify($props.items || []))
})

const itemList = computed((): Item[] => {
    if ($props.allowLiveSearch) {
        return mergeItemsWithoutDuplications($props.liveSearchItems || [], selectedItems.value)
    }
    // Hack to prevent VueJS reactivity for nested objects (speed up for data manipulations)
    return plainItems.value
})

const groupedItemList = computed((): DropdownItems => {
    const items = searchValue.value.length === 0
        ? itemList.value
        : filterItemsBySearchPhrase(searchValue.value, itemList.value)

    const mergedItems = mergeItemsWithoutDuplications(items, selectedItems.value)

    if ($props.groups.length === 0) {
        return mergedItems.map((item): TypedItem => Object.assign({ type: 'item' as const }, item))
    }

    const groupsWithItems: Record<string, Item[]> = {}
    for (const { name: groupName } of $props.groups) {
        groupsWithItems[groupName] = []
    }
    groupsWithItems.Other = []

    for (const item of mergedItems) {
        if (item.group && item.group in groupsWithItems) {
            groupsWithItems[item.group].push(item)
        } else {
            groupsWithItems.Other.push(item)
        }
    }

    const result: DropdownItems = []
    const groupsWithOthers = $props.groups.find((g) => g.name === 'Other') ? $props.groups : [...$props.groups, { name: 'Other', style: 'secondary' }]
    for (const [i, group] of groupsWithOthers.entries()) {
        const groupItems = groupsWithItems[group.name]

        if (groupItems.length === 0) {
            continue
        }

        if (i > 0) {
            result.push({
                type: 'divider',
            })
        }

        result.push({
            type: 'group',
            name: group.name,
            style: group.style,
        })

        groupItems.forEach((item) =>
            result.push(Object.assign({ type: 'item' as const }, item)),
        )
    }

    return result
})

const displayValue = computed((): string => {
    return selectedItems.value
        .map(({ name }) => name)
        .join(', ') || $props.placeholder
})
// Watchers
watch(searchValue, (newVal) => {
    if ($props.allowLiveSearch && newVal.length >= $props.liveSearchMinLength) {
        lastSearchTime.value = Date.now()

        setTimeout(() => {
            if (Date.now() >= lastSearchTime.value + $props.liveSearchTimeout) {
                search({ q: newVal })
            }
        }, $props.liveSearchTimeout)
    }
})

watch(() => $props.value, (newValue) => {
    const normalized = normalizeValue(newValue)

    if (normalized.length > 0 && $props.allowLiveSearch && $props.liveSearchItems.length === 0) {
        if ($props.liveSearchNameAsId) {
            selectedItems.value = Object.values(normalized).map((id) => {
                return { id, name: id }
            }, {})
        } else {
            // reverse search for values by their ids
            search({ ids: normalized })
        }
    } else {
        selectedItems.value = itemList.value
            .filter(({ id }) => normalized.some((value) => isSame(id, value)))
    }
}, { immediate: true })

watch(() => $props.items, (items) => {
    if (!$props.allowLiveSearch) {
        const normalized = normalizeValue($props.value)
        selectedItems.value = items
            .filter(({ id }) => normalized.some((value) => isSame(id, value)))
    }
})

watch(() => $props.liveSearchItems, (liveSearchItems) => {
    const items = mergeItemsWithoutDuplications(liveSearchItems, selectedItems.value)

    selectedItems.value = items.filter(({ id }) => normalizedValue.value.some((value) => isSame(value, id)))
})

// Lifecycle
onBeforeUnmount(() => {
    if (popper) {
        popper.destroy()
    }
})
// Methods
function toggle(targetItems: Item | Item[], group = false): void {
    if (!Array.isArray(targetItems)) {
        targetItems = [targetItems]
    }
    const toggleItems = targetItems.filter(({ disabled }) => !disabled)

    if (toggleItems.length === 0) {
        return
    }

    let newSelectedItems = group
        ? getItemsWithToggleGroup(toggleItems, selectedItems.value)
        : getItemsWithToggle(toggleItems[0], selectedItems.value)

    let newValue: AnyValue
    let toggleValue: AnyValue

    if ($props.multiple) {
        newValue = newSelectedItems.map(({ id }) => id)
        toggleValue = toggleItems.map(({ id }) => id)
    } else {
        if (newSelectedItems.length === 0) {
            newValue = null
            newSelectedItems = []
        } else {
            newValue = newSelectedItems[0].id
            newSelectedItems = [newSelectedItems[0]]
        }
        toggleValue = toggleItems.length > 0 ? toggleItems[0].id : null
    }
    selectedItems.value = newSelectedItems
    $emit('input', newValue, toggleValue)
}

function toggleGroup(toggleGroupName: string): void {
    const itemsInGroup = itemList.value
        .filter(({ group }) => (group || 'Other') === toggleGroupName)

    toggle(itemsInGroup, true)
}

function toggleAll(): void {
    const value = itemList.value.map(({ id }) => id)
    const toggleValue = value.filter(
        (newVal) => !normalizedValue.value.some(
            (curVal) => isSame(newVal, curVal),
        ),
    )

    focusInput()
    selectedItems.value = itemList.value

    $emit('input', value, toggleValue)
}

function toggleNone(): void {
    focusInput()
    selectedItems.value = []
    $emit('input', [], normalizedValue.value)
}

function filterItemsBySearchPhrase (searchValue: string, items: Item[]): Item[] {
    searchValue = searchValue.toLowerCase()

    return items.filter(({ name }) =>
        name !== null &&
        name !== undefined &&
        (name + '').length !== 0 &&
        (name + '').toLowerCase().includes(searchValue),
    )
}

function itemClass(item: TypedItem | TypedGroup | TypedDivider): Record<string, boolean> {
    const classObject: Record<string, boolean> = {}

    switch (item.type) {
        case 'group':
            classObject['dropdown-header'] = true
            break
        case 'divider':
            classObject.divider = true
            break
        case 'item':
            classObject.selected = isItemActive(item)
            classObject.disabled = Boolean(item.disabled)
            classObject.focus = Boolean(focusedItem.value && item.id === focusedItem.value.id)
            break
    }
    return classObject
}

function groupButtonClass({ style }: TypedGroup): string[] {
    const classList = ['btn', 'btn-xs']

    if (style) {
        classList.push(`btn-${style}`)
    }
    if (!$props.multiple) {
        classList.push('disabled')
    }

    return classList
}

function isItemActive(item: TypedItem | TypedGroup | TypedDivider): boolean {
    if (item.type !== 'item') {
        return false
    }
    const id = item.id === undefined ? null : item.id

    return normalizedValue.value
        .some((value) => isSame(value, id))
}

function focusInput(selectText = false): boolean {
    const inputEl = input.value

    if (!inputEl) {
        return false
    }

    inputEl.focus()
    if (selectText && inputEl.value) {
        inputEl.select()
    }

    return true
}

function mergeItemsWithoutDuplications(list1: Item[], list2: Item[]): Item[] {
    return [
        ...list2
            .filter(({ id }) => !list1.some(({ id: itemId }) => isSame(itemId, id))),
        ...list1,
    ]
}

function focusMenuItem(item: Item | undefined): void {
    focusedItem.value = (!item || item.disabled) ? undefined : item
}
function initPopper(): void {
    popper = createPopper(button.value!, menu.value!, {
        placement: $props.placement as 'bottom-start' | 'top-start' | 'bottom-end' | 'top-end',

        onFirstUpdate: () => {
            if (!focusInput(true)) {
                itemsContainer.value!.focus()
            }
        },

        modifiers: [
            { name: 'flip', options: { allowedAutoPlacements: ['top-end', 'top-start', 'top', 'bottom', 'bottom-start', 'bottom-end'] } },
            // keeps menu in same with as reference if reference is larger then menu
            {
                name: 'sameWidth',
                enabled: true,
                phase: 'beforeWrite',
                requires: ['computeStyles'],
                fn: ({ state }) => {
                    if (menu.value!.getBoundingClientRect().width < state.rects.reference.width) {
                        state.styles.popper.width = `${state.rects.reference.width}px`
                    }
                },
            },
        ],
    })
}

function openDropdown(openedParam = true): void {
    if (opened.value === openedParam) {
        return
    }

    if (openedParam) {
        // wait for menu mounted in dom for proper initial positioning
        nextTick(() => {
            initPopper()
            const firstItem = groupedItemList.value.find((it) => it.type === 'item') as TypedItem | undefined
            focusMenuItem(firstItem)
        })
    } else if (popper) {
        // searchValue reset disabled because of strange bug in /back/users-statuses-vip-thresholds Add new Popover when Country filtered by text input and then selected by mouse. Whole popover suddenly disappears
        // searchValue.value = ''
        popper.destroy()
        popper = undefined
    }

    opened.value = openedParam
}

function search(params: Record<string, unknown> = {}): void {
    $emit('liveSearch', params)
}
function onButtonClick(): void {
    if (!$props.enabled) {
        return
    }

    openDropdown(!opened.value)
}

function onbButtonKeyDown(e: KeyboardEvent): void {
    if (['ArrowUp', 'ArrowDown'].includes(e.key)) {
        openDropdown()
    }
}

function onMousedownOutside(e: Event): void {
    nextTick(() => {
        if (button.value !== e.target &&
            menu.value &&
            menu.value !== e.target &&
            !menu.value.contains(e.target as Node)) {
            openDropdown(false)
        }
    })
}

function onMenuClick(): void {
    if ($props.allowSearch) {
        focusInput()
    }
}

function onMenuItemClick(item: Item): void {
    if (item.disabled) {
        return
    }
    toggle(item)
    focusMenuItem(item)
    if (!$props.multiple) {
        openDropdown(false)
    }
}

function onNavigate(event: KeyboardEvent): boolean {
    let offset: number

    if (event.key === 'ArrowDown') {
        offset = 1
    } else if (event.key === 'ArrowUp') {
        offset = -1
    } else if (event.key === 'Enter') {
        if (opened.value && itemList.value.length > 0 && focusedItem.value) {
            toggle(focusedItem.value)
            if (!$props.multiple) {
                openDropdown(false)
            }
        } else {
            openDropdown(true)
        }
        return false
    } else if (event.key === 'Tab' || event.key === 'Escape') {
        openDropdown(false)
        return false
    } else {
        return false
    }
    event.preventDefault()

    const items = groupedItemList.value.filter((it): it is TypedItem => it.type === 'item')

    if (items.length === 0) {
        focusMenuItem(undefined)
        return false
    }

    const idx = items.findIndex((it) => focusedItem.value && it.id === focusedItem.value.id)

    if (idx + offset === items.length) {
        return false
    }

    if (idx === -1 || (idx === 0 && offset === -1) || items.length === 1) {
        focusMenuItem(items[0])
        return false
    }
    const newIdx = idx + offset
    focusMenuItem(items[newIdx])

    return false
}
</script>

<style lang="scss">
    .dropdown-menu-select {
        flex-direction: column;
        padding: 0;

        &.show {
            display: flex !important;
        }

        .focus {
            text-decoration: none;
            background-color: var(--bs-secondary-bg);
        }

        & > .dropdown-menu-items-container {
            flex: 1;
            overflow-y: auto;

            & > .dropdown-item {
                padding-right: 50px;
                padding-left: 2rem;
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                flex-wrap: wrap;

                & > .check-mark {
                    margin-right: -30px;
                    position: initial;
                    display: inline-block;

                    &::after {
                        content: '';
                        display: block;
                        width: 0.5em;
                        height: 1em;
                        border-style: solid;
                        border-width: 0 0.26rem 0.26rem 0;
                        transform: rotate(45deg);
                    }
                }

                &.disabled {
                    color: var(--bs-btn-disabled-color);
                    cursor: not-allowed;
                }
            }
        }
    }

    .dropdown-btn {
        width: 100%;
        overflow-x: hidden;

        .dropdown-text {
            overflow: hidden;
        }
    }

    .input-group .dropdown-btn {
        flex-grow: 1;
        width: 1%;
    }
</style>
