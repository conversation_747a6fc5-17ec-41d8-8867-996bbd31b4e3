import { _ } from '@/utils/index.ts'

export function nl2br(str: string): string {
    return str.replace(/([^>\r\n]?)(\r\n|\n\r|\r|\n)/g, '$1<br>$2');
}

export function ansiToHtml(input: string): string {
    const classMap: Record<number, string> = {
        34: 'text-blue',    // FG_BLUE
        36: 'text-teal',    // FG_CYAN
        32: 'text-green',   // FG_GREEN
        37: 'text-gray',    // FG_GREY
        35: 'text-purple',  // FG_PURPLE
        31: 'text-red',     // FG_RED
        33: 'text-yellow',  // FG_YELLOW

        44: 'bg-blue',      // BG_BLUE
        46: 'bg-teal',      // BG_CYAN
        42: 'bg-green',     // BG_GREEN
        47: 'bg-gray',      // BG_GREY
        45: 'bg-purple',    // BG_PURPLE
        41: 'bg-red',       // BG_RED
        43: 'bg-yellow',    // BG_YELLOW
    };

    let tags = 0;
    const RESET = '0';

    // eslint-disable-next-line no-control-regex
    let finalResult = nl2br(_.escape(input)).replace(/\u001b\[([\d;]+)m/g, (_, codes) => {
        const classes: string[] = [];
        let reset = false;

        codes.split(';').forEach((code: string) => {
            if (code === RESET) {
                classes.length = 0;
                reset = true;
            } else if (code in classMap) {
                classes.push(classMap[Number(code)]);
            }
        });

        let returnStr = '';
        while (reset && tags > 0) {
            returnStr += '</span>';
            tags--;
        }

        if (classes.length === 0) {
            return returnStr;
        }

        const classString = classes.join(' ');
        tags++;
        return `${returnStr}<span class="${classString}">`;
    });

    while (tags > 0) {
        finalResult += '</span>';
        tags--;
    }

    return finalResult;
}
