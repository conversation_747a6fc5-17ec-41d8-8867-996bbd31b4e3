<template>
    <card>
        <rich-table
            v-bind="richTable"
            @reload="reload"
        >
            <template #afterTitle="{refreshCallback}">
                <popover
                    title="Add country"
                    position="right"
                    @open="onCreateOpen"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <icona name="icn-plus" /> Add
                    </button>
                    <template #content>
                        <form-grid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onCreateSubmit($event, refreshCallback)"
                        />
                    </template>
                </popover>
            </template>
            <template #in_amount_initial="{row, refreshCallback}">
                <inplace-edit
                    :value="row.in_amount_initial"
                    type="input"
                    @submit="onUpdate({country: row.country_id, column: 'in_amount_initial', in_amount_initial: $event }, refreshCallback)"
                />
            </template>
            <template #in_amount_week="{row, refreshCallback}">
                <inplace-edit
                    :value="row.in_amount_week"
                    type="input"
                    @submit="onUpdate({country: row.country_id, column: 'in_amount_week', in_amount_week: $event }, refreshCallback)"
                />
            </template>
            <template #delete="{row, refreshCallback}">
                <button
                    class="btn btn-danger btn-xs"
                    :disabled="row.country === null"
                    @click="onDelete({country: row.country_id}, refreshCallback)"
                >
                    <icona name="icn-delete" /> Delete
                </button>
            </template>
        </rich-table>
    </card>
</template>

<script lang="ts">
import { RichTable, InplaceEdit, FormGrid, Popover, Icona } from '@/components'
import { Card } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, RichTableType, Values } from '@/types'

export default defineComponent({
    components: {
        Card,
        RichTable,
        InplaceEdit,
        FormGrid,
        Popover,
        Icona,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {} as RichTableType,
            createForm: {} as FormGridType,
        }
    },

    methods: {
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        async onCreateOpen () {
            this.createForm = await this.$fetch(this.$route.path + '/add-form')
        },
        onCreateSubmit (params: Values, refreshCallBack: () => void) {
            return this.$processFormResponse(this.$fetch(this.$route.path + '/add', params), this.createForm).then(() => refreshCallBack())
        },
        onUpdate (params: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update', params).then(refreshCallback)
        },
        onDelete (params: Values, refreshCallback: () => void) {
            if (confirm('Do you want to delete thresholds for ' + params.country + '?')) {
                this.$fetch(this.$route.path + '/delete', params).then(refreshCallback)
            }
        },
    },
})
</script>
