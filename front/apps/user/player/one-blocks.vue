<template>
    <div class="col-sm-9 player-blocks-container">
        <TabsRouter
            :tabs="blockTabs"
            tabIdRouteParamName="subTabId"
            :emptyTabId="DEFAULT_BLOCK"
            :disabled="playerBusy"
            @change="onTabChange"
        >
            <template #default>
                <component
                    :is="activeBlock.component || 'CommonTable'"
                    :key="siteUser + '-' + activeBlock.block"
                    v-bind="activeBlock.props"
                    :title="activeBlock.title"
                    :block="activeBlock.block"
                    :loadPromise="loadPromise"
                    :siteIdUserId="siteIdUserId"
                    :busy="finalBusy"
                    @reload="onBlockReload"
                />
            </template>
        </TabsRouter>
    </div>
</template>

<script lang="ts">
import { TabsRouter } from '@/components'
import { Bonuses, BonusesActivation, BonusOffers, Chats, Comments, CommonTable, Contacts, ContactsHistory, Customer, Transactions, Referrals, Kyc, PayInfo, Wallets, Tickets } from './blocks'
import { defineComponent, PropType } from 'vue'
import { Values } from '@/types'
import { useTitleBreadcrumbs } from '@/utils/title-breadcrumbs'
import { useRoute } from 'vue-router'

interface BlockType {
    block: string
    title: string
    component?: string
    props: {
        showTotal: boolean
        showPagination: boolean
    },
}

export default defineComponent({
    components: {
        TabsRouter,
        PayInfo,
        Contacts,
        Comments,
        Bonuses,
        BonusOffers,
        Transactions,
        ContactsHistory,
        Chats,
        Customer,
        BonusesActivation,
        CommonTable,
        Referrals,
        Kyc,
        Wallets,
        Tickets,
    },
    props: {
        siteUser: {
            type: String,
            required: true,
        },
        siteIdUserId: {
            type: Object as PropType<{ siteId: string, userId: string }>,
            required: true,
        },
        playerBusy: {
            type: Boolean,
            default: false,
        },
        allowedBlocks: {
            type: Array,
            required: true,
        },
    },
    emits: ['updateBlockTitle'],
    setup() {
        return {
            titleBreadcrumbs: useTitleBreadcrumbs(),
            route: useRoute(),
        }
    },
    data () {
        return {
            blocks: [
                {
                    block: 'pay-info',
                    title: 'Pay info',
                    component: 'PayInfo',
                },
                {
                    block: 'contacts',
                    title: 'Contacts',
                    component: 'Contacts',
                    props: {
                        showTotal: true,
                    },
                },
                {
                    block: 'transactions',
                    title: 'Transactions',
                    component: 'Transactions',
                    props: {
                        showPagination: true,
                    },
                },
                {
                    block: 'games',
                    title: 'Games',
                    props: {
                        showTotal: true,
                    },
                },
                {
                    block: 'games-hhs',
                    title: 'Games (HHS)',
                    props: {
                        showTotal: true,
                    },
                },
                {
                    block: 'logins',
                    title: 'Login places',
                },
                {
                    block: 'wallets',
                    title: 'Wallets',
                    component: 'Wallets',
                },
                {
                    block: 'requisites',
                    title: 'Requisites',
                    props: {
                        showTotal: true,
                    },
                },
                {
                    block: 'bonuses',
                    title: 'Bonuses',
                    component: 'Bonuses',
                    props: {
                        showTotal: true,
                        showPagination: true,
                    },
                },
                {
                    block: 'chats',
                    title: 'Chats',
                    component: 'Chats',
                },
                {
                    block: 'loyalty',
                    title: 'Loyalty',
                },
                {
                    block: 'customer',
                    title: 'Customer',
                    component: 'Customer',
                },
                {
                    block: 'wheel-fortune',
                    title: 'WoF info',
                },
                {
                    block: 'contacts-history',
                    title: 'Contact history',
                    component: 'ContactsHistory',
                },
                {
                    block: 'crm',
                    title: 'CRM',
                },
                {
                    block: 'bets',
                    title: 'Bets',
                    props: {
                        showTotal: true,
                        showPagination: true,
                    },
                },
                {
                    block: 'history',
                    title: 'Status history',
                    props: {
                        showTotal: true,
                        showPagination: true,
                    },
                },
                {
                    block: 'bonuses-activation',
                    title: 'Bonuses act.',
                    component: 'BonusesActivation',
                },
                {
                    block: 'bonus-offers',
                    title: 'Bonus offers',
                    component: 'BonusOffers',
                },
                {
                    block: 'lootboxes',
                    title: 'Lootboxes',
                },
                {
                    block: 'comments',
                    title: 'Comments',
                    component: 'Comments',
                },
                {
                    block: 'referrals',
                    title: 'Referrals',
                    component: 'Referrals',
                },
                {
                    block: 'kyc',
                    title: 'KYC',
                    component: 'Kyc',
                },
                {
                    block: 'tickets',
                    title: 'Tickets',
                    component: 'Tickets',
                },
            ] as BlockType[],
            busy: false,
            loadPromise: null as unknown,
            DEFAULT_BLOCK: 'pay-info',
        }
    },

    computed: {
        blockTabs () {
            return this.blocks
                .filter(block => this.allowedBlocks.includes(block.block)) // permissions check
                .map(block => {
                    return {
                        title: block.title,
                        route: {
                            name: 'player',
                            params: {
                                tabId: this.siteUser,
                                subTabId: block.block,
                            },
                        },
                    }
                })
        },

        activeBlock (): BlockType {
            const result = this.blocks.find(block => this.$route.params.subTabId === block.block)

            if (!result) {
                return this.blocks[0] // pay-info as default block
            }

            return result
        },
        finalBusy (): boolean {
            return this.playerBusy || this.busy
        },
    },

    methods: {
        onTabChange(newBlock: string) {
            this.setLoadPromise(this.$decodeParams(this.route.query), () => {
                this.$emit('updateBlockTitle', this.blocks.find(b => b.block === (newBlock || this.DEFAULT_BLOCK))?.title)
            })
        },
        onBlockReload (params: Values) {
            this.setLoadPromise(params, () => ({}))
            const paramsWithoutSiteIdAndUserId = Object.fromEntries(Object.entries(params).filter(([k]) => !['siteId', 'userId'].includes(k)))
            this.$historyReplaceParams(paramsWithoutSiteIdAndUserId, false)
        },
        setLoadPromise (params: Values, onFinally: () => void) {
            this.busy = true
            const fullParams = Object.assign({}, params, this.siteIdUserId)
            this.loadPromise = this.$fetch('/user/player/block?block=' + this.activeBlock.block, fullParams).finally(() => {
                this.busy = false
                onFinally()
            })
        },
    },
})
</script>

<style lang="scss">
.player-blocks-container .card-body {
    overflow-x: visible;
}
</style>
