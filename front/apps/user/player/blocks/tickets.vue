<template>
    <rich-table
        v-bind="richTableProps"
        @reload="onReload"
    >
        <template
            #afterTitle="{refreshCallback}"
        >
            <popover
                hideOnOutside
                title="New ticket"
                :isOpened="isOpened"
            >
                <button
                    class="btn btn-sm btn-success"
                    type="button"
                    @click="onShowAddForm"
                >
                    <icona name="icn-plus" /> Create ticket
                </button>
                <template #content>
                    <form-grid
                        v-bind="form"
                        enctype="formData"
                        @change="form.values = $event"
                        @submitFormData="onCreateSubmit($event, refreshCallback)"
                    />
                </template>
            </popover>
        </template>
        <template #ticketId="{ row }: { row: UserTicketRow }">
            <router-link :to="{ name: 'users-tickets', query: {ticketId: row.id}}">
                {{ row.id }}
            </router-link>
        </template>
        <template #jira="{ row }: { row: UserTicketRow }">
            <a
                v-if="row.jira_url"
                :href="row.jira_url"
                target="_blank"
            > {{ row.jira_key }}</a>
        </template>
        <template #approve="{ row, refreshCallback }: { row: UserTicketRow; refreshCallback: () => void }">
            <div v-if="row.allowApproveButtons" class="btn-group btn-group-sm">
                <button
                    class="btn btn-xs btn-success"
                    @click="onDecision({ 'ticketId' : row.id, 'approve' : true }, refreshCallback)"
                >
                    <icona name="icn-thumb-up" /> Approve
                </button>
                <button
                    class="btn btn-xs btn-warning"
                    @click="onDecision({ 'ticketId' : row.id, 'approve' : false }, refreshCallback)"
                >
                    <icona name="icn-thumb-down" /> Decline
                </button>
            </div>
        </template>
    </rich-table>
</template>

<script lang="ts">

import { FormGrid, Icona, Popover } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, TableRow } from '@/types'
import CommonTable from './common-table.vue'

interface UserTicketRow extends TableRow {
    id: number
    product_ticket_id?: string
    jira_key?: string
    jira_url?: string
    status: string
    comments?: []
    allowApproveButtons?: boolean
}

export default defineComponent({
    components: {
        Icona,
        Popover,
        FormGrid,
    },
    mixins: [
        CommonTable,
    ],
    data () {
        return {
            form: {} as FormGridType,
            isOpened: false,
        }
    },
    methods: {
        async onShowAddForm () {
            this.form = await this.$fetch('/user/player/tickets/add-form', this.siteIdUserId)
        },
        async onDecision(payload: { ticketId: number; approve: boolean }, refreshCallback: () => void) {
            this.richTable.disabled = true
            await this.$fetch('/user/tickets/update-status', payload)
                .then(() => refreshCallback())
                .finally(() => this.richTable.disabled = false)
        },
        onCreateSubmit (formData: FormData, refreshCallback: () => void) {
            formData.set('siteId', String(this.siteIdUserId.siteId))
            formData.set('userId', String(this.siteIdUserId.userId))
            formData.set('transId', this.form.values!.transId!.toString())
            formData.set('userAmount', this.form.values!.userAmount!.toString())
            this.$processFormResponse(this.$fetch('/user/player/tickets/add', formData, false), this.form).then(() => {
                this.isOpened = true
                this.$nextTick(() => {
                    this.isOpened = false
                })
                refreshCallback()
            })
        },
    },
})
</script>
