<template>
    <rich-table
        v-bind="richTable"
        showRefresh
        showPagination
        @reload="onReload"
    >
        <template #afterTitle="{refreshCallback}">
            <popover
                title="Add new mirror"
                position="right"
                @open="onCreateOpen"
            >
                <button
                    type="button"
                    class="btn btn-sm btn-success"
                >
                    <icona name="icn-plus" /> Add new mirror
                </button>
                <template #content>
                    <form-grid
                        v-bind="createMirrorForm"
                        @change="createMirrorForm.values = $event"
                        @submit="onCreateMirrorFormSubmit($event, refreshCallback)"
                    />
                </template>
            </popover>
        </template>
        <template #userCount="{row}">
            <router-link
                class="btn btn-sm btn-primary"
                type="button"
                :to="{ name: 'secret-mirrors-users', params: { mirrorId: row.id } }"
            >
                <icona name="icn-eye" /> {{ row.user_count }} Show
            </router-link>
        </template>
        <template #actions="{row, refreshCallback}">
            <div>
                <button
                    class="btn btn-sm btn-danger"
                    type="button"
                    @click="onDelete(row, refreshCallback)"
                >
                    <icona name="icn-delete" /> Delete
                </button>
            </div>
        </template>
    </rich-table>
</template>

<script lang="ts">
import { RichTable, FormGrid, Popover, Icona } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, TableRow, Values } from '@/types'

export default defineComponent({
    components: {
        Icona,
        RichTable,
        FormGrid,
        Popover,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    data: function () {
        return {
            richTable: {},
            createMirrorForm: {} as FormGridType,
        }
    },
    methods: {
        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },
        reload (params: Values) {
            return this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },
        async onCreateOpen () {
            this.createMirrorForm = await this.$fetch(this.$route.path + '/add-mirror-form')
        },
        onCreateMirrorFormSubmit (data: Values, refreshCallback: () => void) {
            this.$processFormResponse(this.$fetch(this.$route.path + '/add-mirror', data), this.createMirrorForm)
                .then(refreshCallback)
        },
        onDelete (row: TableRow, refreshCallback: () => void) {
            if (confirm(`Do you really want to delete mirror ${row.url}`)) {
                this.$fetch(this.$route.path + '/delete-mirror', { mirrorId: row.id })
                    .then(refreshCallback)
            }
        },
    },
})
</script>
