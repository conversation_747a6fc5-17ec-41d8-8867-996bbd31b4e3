<template>
    <Card width="wide">
        <template #afterHelp>
            <li>
                <button
                    class="btn btn-md btn-info ms-2"
                    type="button"
                    :disabled="table.data?.length === 0"
                    @click="onDownload"
                >
                    <Icona name="icn-download" />
                    CSV
                </button>
            </li>
        </template>
        <FormGrid
            v-bind="filterForm"
            @change="onChange"
            @submit="onSubmit"
        />
        <div ref="chart" />
        <EntityTable
            v-bind="table"
            enableInnerSort
            sticky
            @sortChanged="table.sort = $event"
        >
            <template #eventId="{row}: {row: SpinsTableRow}">
                {{ row.eventId }}
                <template v-if="row.eventId">
                    <a
                        :href="`https://admin.hiddenholysystem.com/hhs/replay/show/?eventIdFrom=${row.eventId}`"
                        target="_blank"
                    >
                        <Icona name="icn-play" />
                    </a>
                    <a
                        v-if="row.sessionId && row.userId"
                        :href="`https://admin.hiddenholysystem.com/hhs/user/gameeventsagregator/showlist/?page=1&system_name=${row.hhsProjectName}&agregator_user_id=${row.userId}&mode=0&event_id=${row.eventId}&data_user_user_id=${row.sessionId}`"
                        target="_blank"
                    >
                        <Icona name="icn-list" />
                    </a>
                </template>
            </template>

            <template #sessionId="{row}: {row: SpinsTableRow}">
                <template v-if="row.sessionId">
                    ...{{ row.sessionId.substring(row.sessionId.length - COUNT_LAST_SYMBOLS_VISIBLE) }}
                    <a
                        v-if="row.userId && filterForm?.values?.source === 'hhs'"
                        :href="`https://admin.hiddenholysystem.com/hhs/user/gameeventsagregator/showlist/?page=1&system_name=${row.hhsProjectName}&agregator_user_id=${row.userId}&mode=0&data_user_user_id=${row.sessionId}`"
                        target="_blank"
                    >
                        <Icona name="icn-list" />
                    </a>
                    <button
                        class="btn btn-secondary btn-xs"
                        @click="copyToClipboard(row.sessionId)"
                    >
                        <Icona name="icn-copy" />
                    </button>
                </template>
            </template>
        </EntityTable>
    </Card>
</template>

<script lang="ts">
import { Card } from '@/widgets'
import { EntityTable, FormGrid, HighchartsLoader, Icona } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, NextWithReload, TableColumn, TableRow, TableType, Values } from '@/types'
import { download } from '@/utils'
import { useFetch } from '@/utils/fetch.ts'
import { copyToClipboard } from '@/utils/clipboard.ts'
import { useProcessFormResponse } from '@/utils/response-processor.ts'
import { useHistoryReplaceParams } from '@/utils/url-params.ts'
import { useRouter } from 'vue-router'
import { SeriesLineOptions, StockChart, YAxisOptions, XAxisPlotLinesOptions, Tooltip, Point } from 'highcharts'

interface Balance {
    x: number
    y: number
    color: string
    roundType: string
    action: string
    bet: number
    win: number
    game: string
    balance: number
    bonusBalance: number
    realBalance: number
    bonusRefundSum: number
    bonusBetSum: number
    balanceType: string | null
    date: string
    symbols: string[][] | null
}

interface ChartData {
    series: SeriesLineOptions[],
    yAxis: YAxisOptions[],
    plotLinesValues: XAxisPlotLinesOptions[],
    columns: TableColumn[]
}

interface SpinsTableRow {
    eventId: string
    sessionId: string
    userId: number
    hhsProjectName?: string
}

let chartInstance: StockChart | null = null

function tooltipForMainSerie(point: Point): string {
    const { roundType, action, bet, win, game, balance, balanceType, symbols } = point as unknown as Balance
    const balanceTypeOrEmpty = balanceType || ''
    const roundTypeOrPaid = roundType || 'paid'
    let result = `${roundTypeOrPaid} <b>${action}</b> ${bet || win}<br>${game}<br>Balance: <b>${balance}</b> ${balanceTypeOrEmpty}`

    if (symbols) {
        const transpose = (m: string[][]) => m[0].map((_col: unknown, c: number) => m.map((_row: unknown, r: number) => m[r][c]))
        const symbolsT = transpose(symbols)
        const table = []
        table.push('<br><table class="spins-symbols-table">');
        (symbolsT as string[][]).forEach(function (i) {
            table.push('<tr>')
            i.forEach(function (j) {
                table.push('<td>', j, '</td>')
            })
            table.push('</tr>')
        })
        table.push('</table>')
        result += table.join('')
    }

    return result
}

export default defineComponent({
    components: {
        Icona,
        Card,
        FormGrid,
        EntityTable,
    },
    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },
    setup () {
        return {
            fetch: useFetch(),
            processFormResponse: useProcessFormResponse(),
            historyReplaceParams: useHistoryReplaceParams(useRouter()),
        }
    },
    data: function () {
        return {
            COUNT_LAST_SYMBOLS_VISIBLE: 10,
            filterForm: {} as FormGridType,
            table: {
                sort: 'date-',
                data: [] as TableRow[],
                columns: [],
            } as TableType,
        }
    },
    beforeUnmount () {
        this.destroyChart()
    },
    methods: {
        copyToClipboard,
        async reload (params: Values) {
            if (Object.keys(this.filterForm).length === 0) {
                this.filterForm = await this.$fetch(this.$route.path + '/form')
            }

            if (Object.keys(params).length > 0) {
                this.filterForm.values = params
                await this.onSubmit(params)
            }
        },
        onChange (params: Values) {
            this.filterForm.values = params
            this.historyReplaceParams(params)
        },
        async onSubmit (params: Values) {
            this.destroyChart()

            await this.processFormResponse<ChartData>(this.fetch(this.$route.path + '/data', params), this.filterForm).then(data => {
                if (data.series[0].data) {
                    this.table.data = data.series[0].data.slice() as unknown as TableRow[]
                    this.table.columns = data.columns
                }
                this.constructChart(data)
            })
        },
        destroyChart () {
            if (chartInstance) {
                chartInstance.destroy()
                chartInstance = null
                this.table.columns = []
                this.table.data = []
            }
        },
        constructChart (spinsData: ChartData) {
            HighchartsLoader().then(({ default: Highcharts }) => {
                chartInstance = Highcharts.stockChart(this.$refs.chart as HTMLElement, {
                    chart: {
                        panning: {
                            enabled: true,
                        },
                    },
                    rangeSelector: {
                        buttons: [
                            {
                                type: 'minute',
                                count: 15,
                                text: '15min',
                            },
                            {
                                type: 'day',
                                count: 1,
                                text: '1day',
                            },
                            {
                                type: 'all',
                                text: 'All',
                            },
                        ],
                        selected: 2,
                        inputEnabled: false,
                    },
                    series: spinsData.series,
                    yAxis: spinsData.yAxis,
                    xAxis: {
                        plotLines: spinsData.plotLinesValues,
                    },
                    tooltip: {
                        useHTML: true,
                        split: true,
                        formatter: function (tooltip: Tooltip) {
                            const result = tooltip.defaultFormatter.call(this, tooltip) as string[]

                            if (this.points) {
                                result[1] = tooltipForMainSerie(this.points[0]) // Override default tooltip for "Balance" main serie at index 1
                            }

                            return result;
                        },
                    },
                })
            })
        },
        onDownload () {
            this.fetch(this.$route.path + '/download', this.filterForm.values).then((csv: string) => {
                download(csv, 'spins.csv')
            })
        },
    },
})
</script>

<style lang="scss">
table.spins-symbols-table td {
    border: 1px solid var(--bs-body-color);
    padding: 3px 6px;
    text-align: center;
    font-family: monospace;
}
</style>
