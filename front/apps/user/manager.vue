<template>
    <div class="container">
        <Breadcrumbs />
        <TabsRouter
            :tabs="tabsWithRoutes"
            tabIdRouteParamName="action"
            @change="onTabChange"
        >
            <template #default="{tabId}">
                <FormGrid
                    v-bind="form"
                    @change="form.values = $event"
                    @submit="onFormSubmit(tabId, $event)"
                />
            </template>
        </TabsRouter>
    </div>
</template>

<script lang="ts">
import { TabsRouter, FormGrid } from '@/components'
import { Breadcrumbs } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, Values, Tab } from '@/types'

interface ManagerTab {
    title: string,
    action: string,
    formAction: string
}

export default defineComponent({
    components: {
        Breadcrumbs,
        TabsRouter,
        FormGrid,
    },
    data () {
        return {
            activeTab: null as ManagerTab | null,
            tabs: [] as ManagerTab[],
            form: {} as FormGridType,
        }
    },
    computed: {
        tabsWithRoutes (): Tab[] {
            return this.tabs.map(tab => {
                return {
                    title: tab.title,
                    route: {
                        name: 'user-manager',
                        params: { action: tab.action },
                    },
                }
            })
        },
    },
    methods: {
        async onTabChange (tabId: string | undefined) {
            if (Object.keys(this.tabs).length === 0) {
                this.tabs = await this.$fetch('/user/manager/tabs')
            }
            this.activeTab = null
            const activeTab = this.tabs.find(tab => {
                return tab.action === tabId
            })

            if (activeTab) {
                this.form = await this.$fetch('/user/manager/' + activeTab.formAction)
                this.activeTab = activeTab
            }
        },
        onFormSubmit (tabId: string | undefined, params: Values) {
            const activeTab = this.tabs.find(tab => {
                return tab.action === tabId
            })

            if (activeTab) {
                return this.$processFormResponse(this.$fetch('/user/manager/' + activeTab.action, params), this.form).then(data => {
                    if (data.blocks) {
                        this.form = data;
                    }
                })
            }
        },
    },
})
</script>
