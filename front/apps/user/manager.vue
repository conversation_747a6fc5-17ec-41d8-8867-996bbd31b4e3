<template>
    <div class="container">
        <Breadcrumbs />
        <TabsRouter
            :tabs="tabsWithRoutes"
            tabIdRouteParamName="action"
            @change="onTabChange"
        >
            <FormGrid
                v-bind="form"
                @change="form.values = $event"
                @submit="onFormSubmit($event)"
            />
        </TabsRouter>
    </div>
</template>

<script lang="ts">
import { TabsRouter, FormGrid } from '@/components'
import { Breadcrumbs } from '@/widgets'
import { defineComponent } from 'vue'
import { FormGridType, Values, Tab } from '@/types'

interface ManagerTab {
    title: string,
    action: string,
    formAction: string
}

export default defineComponent({
    components: {
        Breadcrumbs,
        TabsRouter,
        FormGrid,
    },
    data () {
        return {
            activeTabId: undefined as string | undefined,
            tabs: [] as ManagerTab[],
            form: {} as FormGridType,
        }
    },
    computed: {
        tabsWithRoutes (): Tab[] {
            return this.tabs.map(tab => {
                return {
                    title: tab.title,
                    route: {
                        name: 'user-manager',
                        params: { action: tab.action },
                    },
                }
            })
        },
        activeTab(): ManagerTab | undefined {
            return this.tabs.find(tab => {
                return tab.action === this.activeTabId
            })
        },
    },

    watch: {
        async activeTab (activeTab: ManagerTab | undefined) {
            if (activeTab) {
                this.form = await this.$fetch('/user/manager/' + activeTab.formAction)
            }
        },
    },
    async mounted () {
        if (Object.keys(this.tabs).length === 0) {
            this.tabs = await this.$fetch('/user/manager/tabs')
        }
    },
    methods: {
        async onTabChange (tabId: string | undefined) {
            this.activeTabId = tabId
        },
        async onFormSubmit (params: Values) {
            if (this.activeTab === undefined) {
                return
            }

            await this.$processFormResponse(this.$fetch('/user/manager/' + this.activeTab.action, params), this.form).then(data => {
                // For loading comments
                if (data.blocks) {
                    this.form = data;
                }
            })
        },
    },
})
</script>
