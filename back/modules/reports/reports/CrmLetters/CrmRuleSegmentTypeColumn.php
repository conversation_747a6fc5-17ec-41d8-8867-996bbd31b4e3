<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\CrmLetters;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\CrmRule;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class CrmRuleSegmentTypeColumn extends BaseColumn implements Filtered, Selected, Decorated
{
    use FilterAndSelectDefault;

    public string $column = 'segment_type';
    public string $title = 'Segment type';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(CrmRule::SEGMENT_TYPES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, CrmRule::SEGMENT_TYPES];
    }

    public function decorate($value, array $row)
    {
        return CrmRule::getSegmentTypeById($value);
    }
}
