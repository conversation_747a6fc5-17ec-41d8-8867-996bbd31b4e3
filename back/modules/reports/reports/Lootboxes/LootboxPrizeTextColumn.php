<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Lootboxes;

use app\back\components\helpers\Json;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class LootboxPrizeTextColumn extends BaseColumn implements Selected, Decorated
{
    public bool $isHtmlValue = true;
    public string $title = 'Size';
    public string $column = 'prize_text';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function decorate($value, array $row): string
    {
        if (!$value) {
            return '';
        }

        $result = implode("\n", Json::decode($value));

        if ($this->isHtmlVersion) {
            $result = nl2br($result);
        }

        return $result;
    }
}
