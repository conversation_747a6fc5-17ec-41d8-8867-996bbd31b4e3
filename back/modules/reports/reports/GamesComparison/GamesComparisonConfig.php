<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\GamesComparison;

use app\back\components\helpers\DateHelper;
use app\back\modules\reports\columns\DateRangeInputColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\HourColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Sites;

class GamesComparisonConfig extends BaseReportConfig
{
    public const string METRIC_BET_SUM = 'bet';
    public const string METRIC_PAYOUT = 'payout';
    public const string METRIC_USERS = 'users';

    public const string SUB_METRIC_BET_AMOUNT = 'bet_amount';
    public const string SUB_METRIC_WIN_AMOUNT = 'win_amount';
    public const string SUB_METRIC_USERS = self::METRIC_USERS;

    // Sort order (big to small)
    private const array ORDER_GROUPS = ['week', 'day', 'hour'];

    private array $outMetrics = [];
    private string $metric;
    private array $joinConditions = ['AND', 'p.site_id = h.site_id'];

    public function rules(): array
    {
        return [
            [['date_range', 'site_id'], 'required'],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date_range', DateHelper::range(DateHelper::weekAndDayAgo(), DateHelper::yesterday())],
            ['site_id', []],
            ['metrics', [self::METRIC_PAYOUT]],
            ['groups', ['day']],
        ];
    }

    public function filters(): array
    {
        return [
            'Filters' => [
                'date_range' => [DateRangeInputColumn::class, ['p:n', 'h:n']],
                'site_id' => [SiteIdColumn::class, ['p:n', 'h:n']],
            ]
        ];
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        return [
            'Mode' => $this->outMetrics ?: $this->viewMetrics()
        ];
    }

    private function viewMetrics(): array
    {
        return [
            self::METRIC_BET_SUM => [PercentColoredColumn::class, [
                'expr' => null,
                'p',
                'p:n' => self::SUB_METRIC_BET_AMOUNT,
                'h:n' => self::SUB_METRIC_BET_AMOUNT,
            ], 'title' => 'Bet sum', 'decimals' => 0],
            self::METRIC_PAYOUT => [PercentColoredColumn::class, [
                'expr' => null,
                'p',
                'p:n' => [self::SUB_METRIC_BET_AMOUNT, self::SUB_METRIC_WIN_AMOUNT],
                'h:n' => [self::SUB_METRIC_BET_AMOUNT, self::SUB_METRIC_WIN_AMOUNT],
            ], 'title' => 'Payout', 'decimals' => 0],
            self::METRIC_USERS => [PercentColoredColumn::class, [
                'expr' => null,
                'p',
                'p:n' => self::SUB_METRIC_USERS,
                'h:n' => self::SUB_METRIC_USERS,
            ], 'title' => 'Users', 'decimals' => 0],
        ];
    }

    public function groups(): array
    {
        return [
            'Periods' => [
                'week' => [WeekColumn::class, ['expr' => 'p.week', 'p:n', 'h:n']],
                'day' => [DayColumn::class, ['expr' => 'p.day', 'p:n', 'h:n']],
                'hour' => [HourColumn::class, ['expr' => 'p.hour', 'p:n', 'h:n']],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'p' => [GamesComparisonProductQueryConfig::class],
            'h' => [GamesComparisonHhsQueryConfig::class, $this->joinConditions, ['p'], 'FULL JOIN'],
        ];
    }

    protected function beforeQuery(): void
    {
        foreach (self::ORDER_GROUPS as $group) {
            if (!$this->request->isGroupedBy($group)) {
                continue;
            }

            $this->joinConditions[] = "p.$group = h.$group";
            $this->request->orders[$group] = SORT_DESC;
        }

        $this->metric = reset($this->request->metrics);
        $this->request->metrics = [];

        $this->fillOutMetrics();
    }

    private function fillOutMetrics(): void
    {
        $sitesRepo = $this->container->get(Sites::class);
        $metricColumn = $this->viewMetrics()[$this->metric];

        foreach ($this->request->getFilter('site_id') as $siteId) {
            $colKey = "m_$siteId";
            $filterWhere = sprintf("FILTER (WHERE p.site_id = %s)", $siteId);
            $bSP = "SUM(p.bet_amount) $filterWhere";
            $bSH = "SUM(h.bet_amount) $filterWhere";
            $wSP = "SUM(p.win_amount) $filterWhere";
            $wSH = "SUM(h.win_amount) $filterWhere";
            $uP = "SUM(p.users) $filterWhere";
            $uH = "SUM(h.users) $filterWhere";

            $metricColumn['title'] = $sitesRepo->getShortNameById($siteId);
            $metricColumn[1]['expr'] = match ($this->metric) {
                self::METRIC_BET_SUM => "(COALESCE($bSP, 0) - COALESCE($bSH, 0)) / GREATEST($bSP, $bSH, 1) * 100",
                self::METRIC_PAYOUT => "(COALESCE($wSP, 0) / COALESCE(NULLIF($bSP, 1), 1) - COALESCE($wSH, 0) / COALESCE(NULLIF($bSH, 0), 1)) * 100",
                self::METRIC_USERS => "(COALESCE($uP, 0) - COALESCE($uH, 0)) / GREATEST($uP, $uH, 1) * 100",
            };
            $this->outMetrics[$colKey] = $metricColumn;
            $this->request->metrics[] = $colKey;
        }
    }
}
