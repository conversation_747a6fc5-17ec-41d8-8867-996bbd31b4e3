<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\UsersInfo;

use app\back\entities\UserDocumentTextPrediction;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserDocumentTextManualLabels;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersInfoDocumentsFullNamesQueryConfig extends UsersInfoSiteUserBaseQueryConfig
{
    public function additionalSelects(): array
    {
        return [
            'verified_full_names' => [SimpleColumn::class, ['expr' => "STRING_AGG(DISTINCT f.full_name, ', ')", 'f']],
        ];
    }

    public function tableMap(): array
    {
        $usersTableMapAlias = array_key_first($this->usersTableMap());

        return [
            $usersTableMapAlias => [function () {
                return (new Query($this->db))
                    ->select([
                        'ud.site_id',
                        'ud.user_id',
                        'full_name' => "STRING_AGG(v.val, ' ' ORDER BY v.type, v.val)"
                    ])
                    ->from(['u' => UsersInfoConfig::FILTER_TABLE])
                    ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], "ud.site_id = u.site_id AND ud.user_id = u.user_id")
                    ->innerJoin(['udtml' => UserDocumentTextManualLabels::TABLE_NAME], 'udtml.user_document_id = ud.id')
                    ->innerJoin([new Expression('jsonb_array_elements(udtml.text_bounding_boxes) as l')], 'true')
                    ->innerJoin([new Expression('lateral (values ((l->>0)::int, l->>1)) AS v(type, val)')], 'true')
                    ->where([
                        'v.type' => [
                            UserDocumentTextPrediction::BOX_TYPE_SURNAME,
                            UserDocumentTextPrediction::BOX_TYPE_NAME,
                            UserDocumentTextPrediction::BOX_TYPE_PATRONYMIC
                        ],
                        'udtml.approved' => true
                    ])
                    ->groupBy([
                        'ud.site_id',
                        'ud.user_id',
                        'ud.id',
                    ]);
            }],
        ];
    }
}
