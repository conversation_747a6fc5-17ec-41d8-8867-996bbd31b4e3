<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\UsersInfo;

use app\back\components\BaseAuthAccess;
use app\back\entities\Rate;
use app\back\entities\UserBlock;
use app\back\entities\UserContact;
use app\back\entities\UserMetric;
use app\back\entities\UserWallet;
use app\back\modules\reports\columns\AffDataColumn;
use app\back\modules\reports\columns\AffParamsColumn;
use app\back\modules\reports\columns\AgeColumn;
use app\back\modules\reports\columns\BirthdayColumn;
use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\AutoWithdrawalsMetricColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\ChargebackProbabilityColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CommentColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountFilterColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\CurrencyFilterColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DateTimeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\EmailColumn;
use app\back\modules\reports\columns\FaceIdColumn;
use app\back\modules\reports\columns\GameColumn;
use app\back\modules\reports\columns\GenderColumn;
use app\back\modules\reports\columns\HourColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\IpColumn;
use app\back\modules\reports\columns\IsToxicColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\Minute10Column;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MoneyFilterColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\PercentColumn;
use app\back\modules\reports\columns\PeriodColumn;
use app\back\modules\reports\columns\PhoneColumn;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\RequisiteColumn;
use app\back\modules\reports\columns\RequisitesAllColumn;
use app\back\modules\reports\columns\RequisitesCardColumn;
use app\back\modules\reports\columns\RequisitesPhoneColumn;
use app\back\modules\reports\columns\RequisitesYmColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteHostIsAppColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\SocialNetColumn;
use app\back\modules\reports\columns\TaxNumberColumn;
use app\back\modules\reports\columns\TaxNumberTypeColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UserActiveStatusColumn;
use app\back\modules\reports\columns\UseragentAppColumn;
use app\back\modules\reports\columns\UseragentAppGroupColumn;
use app\back\modules\reports\columns\UseragentBrowserColumn;
use app\back\modules\reports\columns\UseragentColumn;
use app\back\modules\reports\columns\UseragentDeviceColumn;
use app\back\modules\reports\columns\UseragentPlatformColumn;
use app\back\modules\reports\columns\UseragentPlatformGroupColumn;
use app\back\modules\reports\columns\UseragentPlatformVersionColumn;
use app\back\modules\reports\columns\UseragentVariantColumn;
use app\back\modules\reports\columns\UseragentVariantVersionColumn;
use app\back\modules\reports\columns\UserBblStatusColumn;
use app\back\modules\reports\columns\UserBettingProfitSegmentColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\columns\UserCommentWithdrawColumn;
use app\back\modules\reports\columns\UserContactServiceProviderColumn;
use app\back\modules\reports\columns\UserContactStatusColumn;
use app\back\modules\reports\columns\UserCrmGroupColumn;
use app\back\modules\reports\columns\UserFirstDepDateColumn;
use app\back\modules\reports\columns\UserFullStatusColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UserIsBlockedColumn;
use app\back\modules\reports\columns\UserIsRmColumn;
use app\back\modules\reports\columns\UserKycAutoTypeColumn;
use app\back\modules\reports\columns\UserKycColumn;
use app\back\modules\reports\columns\UserKycSourceColumn;
use app\back\modules\reports\columns\UserLastDepDateColumn;
use app\back\modules\reports\columns\UserLastLoginDateColumn;
use app\back\modules\reports\columns\UserLastWdDateColumn;
use app\back\modules\reports\columns\UserLocaleColumn;
use app\back\modules\reports\columns\UserLoyaltyStatusColumn;
use app\back\modules\reports\columns\UserPersonalManagerColumn;
use app\back\modules\reports\columns\UserPriorityTypeOfGamblingColumn;
use app\back\modules\reports\columns\UserRegistrationMethodColumn;
use app\back\modules\reports\columns\UserSocialKeyColumn;
use app\back\modules\reports\columns\UserStatusColumn;
use app\back\modules\reports\columns\UserUuidColumn;
use app\back\modules\reports\columns\VipProbabilityColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\columns\WeekDayColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\components\QueryRequest;
use app\back\modules\reports\reports\UsersBlocks\UsersBlocksReasonColumn;
use app\back\repositories\AffData;
use app\back\repositories\AffParams;
use app\back\repositories\Cities;
use app\back\repositories\Hosts;
use app\back\repositories\LoyaltyStatuses;
use app\back\repositories\MarketingTids;
use app\back\repositories\Refcodes;
use app\back\repositories\Sites;
use app\back\repositories\SiteEmailDomains;
use app\back\repositories\UseragentApps;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\UserBettingProfitSegments;
use app\back\repositories\UserBlockComments;
use app\back\repositories\UserBlocks;
use app\back\repositories\UserContacts;
use app\back\repositories\views\UserGamePaymentActivities;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\UserKycs;
use app\back\repositories\UserLogin4plays;
use app\back\repositories\UserLoyalties;
use app\back\repositories\UserMetrics;
use app\back\repositories\UserMultiAccounts;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\WpWebmasters;
use Yiisoft\Db\Query\Query;

class UsersInfoConfig extends BaseReportConfig
{
    public const string M_KYC_DOC_SOURCE = 'um_kyc_source';
    public const string M_KYC_AUTO_TYPE = 'um_kyc_auto_type';
    public const string M_PAYOUT_LIMIT_DAILY = 'um_pld';
    public const string M_BET_LT_COUNT = 'um_bet_lt_count';
    public const string M_BET_LT_EUR = 'um_bet_lt_eur';
    public const string M_WIN_LT_EUR = 'um_win_lt_eur';
    public const string M_BET_LT_ORIG = 'um_bet_lt_orig';
    public const string M_WIN_LT_ORIG = 'um_win_lt_orig';
    public const string M_FAV_GAME_LT_1 = 'um_fg_lt';
    public const string M_FAV_GAME_7_1 = 'um_fg_d7';
    public const string M_FAV_GAME_30_1 = 'um_fg_d30';
    public const string M_AUTO_WITHDRAWALS = 'um_wdaa';
    public const string M_CHARGEBACK_PROBABILITY = 'um_cbp';
    public const string M_VIP_PROBABILITY = 'um_vip';

    public const string FILTER_TABLE = 'filtered';

    protected ?int $htmlVersionRowsLimit = 10000;

    private bool $canViewCustomer = true;
    private bool $canViewTaxNumber = false;
    private Sites $sitesRepo;

    public function init(): void
    {
        $authAccess = $this->container->get(BaseAuthAccess::class);
        $this->canViewCustomer = $authAccess->canViewCustomer();
        $this->canViewTaxNumber = $authAccess->canViewTaxNumber();
        $this->sitesRepo = $this->container->get(Sites::class);
    }

    protected function beforeQuery(): void
    {
        $this->secureSites();
    }

    private function secureSites(): void
    {
        $available = $this->allowedLists->sites();
        // If sites is not limited
        if (count($available) === count($this->sitesRepo->getNames())) {
            return;
        }

        // Only available sites can be selected. Securing doesn't required
        if (!empty($this->request->getFilter('site_id'))) {
            return;
        }

        // Set explicit list of only available sites to prevent leek
        $this->request->addFilter('site_id', array_keys($available));
    }

    public function rules(): array
    {
        $message = 'Please specify something';
        $otherFilterColumns = ['user_id', 'cid', 'site_user', 'email', 'phone', 'social_key', 'requisite', 'registered_at', 'country', 'uuid', 'ts', 'publisher', 'refcode', 'webmaster_id', 'status', 'active_status', 'is_rm', 'crm_group', 'personal_manager', 'face_id', 'birthday', 'age', 'dep_last_at', 'dep_first_at', 'dep_lt_usd', 'favorite_game', 'kyc', 'kyc_updated_at', 'is_toxic', 'chosen_games', 'bonus_black_list', 'is_blocked', 'is_ignore', 'locale', 'wd_last_at', 'dep_lt_count', 'wd_lt_count', 'location', 'email_confirmed_at', 'phone_confirmed_at'];

        if ($this->canViewTaxNumber) {
            $otherFilterColumns[] = 'tax_number';
            $otherFilterColumns[] = 'tax_number_type';
        }

        return [
            [$otherFilterColumns, 'required', 'when' => fn() => $this->request->allEmptyFilter(...$otherFilterColumns), 'message' => $message],
            ['site_id', 'required', 'when' => fn() => $this->request->allEmptyFilter('site_user', 'cid')],
            ['social_net', 'required', 'when' => fn() => $this->request->anyNotEmptyFilter('social_key')],
            ['favorite_game_period', 'required', 'when' => fn() => $this->request->anyNotEmptyFilter('favorite_game')],
            ['columns', fn() => count($this->request->columns) > 30 ? "You can choose no more than 30 columns simultaneously. Now selected: " . count($this->request->columns) : null],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['columns', ['site_id', 'user_id', 'registered_at']],
            ['metrics', ['count']],
        ];
    }

    public function filters(): array
    {
        $filtersGroups = [
            'User' => [
                'site_id' => [SiteIdColumn::class],
                'user_id' => [UserIdColumn::class],
                'site_user' => [SiteUserColumn::class],
                'cid' => [UserCidColumn::class],
                'ignore' => [IgnoreColumn::class],
                'is_toxic' => [IsToxicColumn::class],
                'is_ignore' => [BooleanColumn::class, 'title' => 'Is ignore (product)'],
                'is_blocked' => [UserIsBlockedColumn::class],
                'currency' => [CurrencyFilterColumn::class, 'currencies' => Rate::currencies(), 'allowMultiple' => true],
                'bonus_black_list' => [UserBblStatusColumn::class],
                'auto_withdrawals' => [AutoWithdrawalsMetricColumn::class, self::M_AUTO_WITHDRAWALS],
            ],
            'Contacts' => [
                'email' => [EmailColumn::class],
                'email_confirmed_at' => [DateColumn::class, 'title' => 'Email confirmed at'],
                'phone' => [PhoneColumn::class],
                'phone_confirmed_at' => [DateColumn::class, 'title' => 'Phone confirmed at'],
                'social_net' => [SocialNetColumn::class],
                'social_key' => [UserSocialKeyColumn::class],
                'requisite' => [RequisiteColumn::class],
            ],
            'Reg' => [
                'registered_at' => [DateColumn::class],
                'country' => [CountryColumn::class],
                'city' => [CityColumn::class],
                'uuid' => [UserUuidColumn::class],
                'locale' => [UserLocaleColumn::class],
                'brand_id' => [BrandColumn::class],
                'location' => [LocationColumn::class],
            ],
            'Traffic' => [
                'ts' => [TrafficSourceColumn::class],
                'publisher' => [MarketingTidPublisherColumn::class],
                'refcode' => [RefcodeColumn::class],
                'webmaster_id' => [WebmasterIdColumn::class],
                'aff_owner' => [WebmasterAffOwnerColumn::class],
                'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class],
            ],
            'Support' => [
                'status' => [UserStatusColumn::class, 'title' => 'Status'],
                'active_status' => [UserActiveStatusColumn::class],
                'is_rm' => [UserIsRmColumn::class],
                'crm_group' => [UserCrmGroupColumn::class],
                'personal_manager' => [UserPersonalManagerColumn::class],
                'segment_id' => [UserBettingProfitSegmentColumn::class],
            ],
            'Info' => [
                'face_id' => [FaceIdColumn::class],
                'birthday' => [BirthdayColumn::class],
                'age' => [AgeColumn::class],
                'kyc' => [UserKycColumn::class],
                'kyc_source' => [UserKycSourceColumn::class],
                'kyc_auto_type' => [UserKycAutoTypeColumn::class],
                'kyc_updated_at' => [DateColumn::class, 'title' => 'KYC updated at'],
            ],
            'Payments' => [
                'dep_first_at' => [UserFirstDepDateColumn::class],
                'dep_last_at' => [UserLastDepDateColumn::class],
                'wd_last_at' => [DateColumn::class, 'title' => 'Last wd date'],
                'balance_real_usd' => [MoneyFilterColumn::class, 'title' => 'Real Balance (USD)'],
                'dep_lt_usd' => [MoneyFilterColumn::class, 'title' => 'Deps LT (USD)'],
                'dep_lt_count' => [CountFilterColumn::class, 'title' => 'Deps LT count'],
                'wd_lt_count' => [CountFilterColumn::class, 'title' => 'Wd LT count'],
                'chargeback_probability' => [ChargebackProbabilityColumn::class, self::M_CHARGEBACK_PROBABILITY],
                'vip_probability' => [VipProbabilityColumn::class, self::M_VIP_PROBABILITY],
                'vip_classification_at' => [DateTimeColumn::class, [self::M_VIP_PROBABILITY => 'updated_at'], 'title' => 'Vip classification date'],
            ],
            'Games' => [
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class],
                'chosen_games' => [GameColumn::class, 'title' => 'Chosen games'],
                'bet_last_at' => [DateColumn::class, ['usi' => 'bet_last_at'], 'title' => 'Last bet date'],
                self::M_FAV_GAME_7_1 => [GameColumn::class, ['u' => self::M_FAV_GAME_7_1], 'title' => 'Favorite game (week)'],
                self::M_FAV_GAME_30_1 => [GameColumn::class, ['u' => self::M_FAV_GAME_30_1], 'title' => 'Favorite game (month)'],
                self::M_FAV_GAME_LT_1  => [GameColumn::class, ['u' => self::M_FAV_GAME_LT_1], 'title' => 'Favorite game (lifetime)'],
                self::M_BET_LT_COUNT  => [CountFilterColumn::class, self::M_BET_LT_COUNT, 'title' => 'Bet count'],
                self::M_BET_LT_EUR => [MoneyFilterColumn::class, self::M_BET_LT_EUR, 'title' => 'Bet sum (EUR)'],
                ...$this->favGames(),
            ],
        ];

        if ($this->canViewTaxNumber) {
            $filtersGroups['User']['tax_number'] = [TaxNumberColumn::class, 'usi'];
            $filtersGroups['User']['tax_number_type'] = [TaxNumberTypeColumn::class, 'usi'];
        }

        $result = [];

        foreach ($filtersGroups as $group => $filters) {
            $result[$group] = [];

            foreach ($filters as $filterName => $filterConfig) {
                $result[$group][$filterName] = array_merge([$filterConfig[0], 'f:n'], array_slice($filterConfig, 1));
            }
        }

        return $result;
    }

    public function columns(): array
    {
        $result = [
            'User' => [
                'site_id' => [SiteIdColumn::class, 'u'],
                'user_id' => [UserIdColumn::class, 'u'],
                'player_link' => [UserIdColumn::class, 'u', 'title' => 'Player link', 'isHtmlValue' => true, 'decorateWithPlayerLink' => true],
                'site_user' => [SiteUserColumn::class, 'u'],
                'is_blocked' => [UserIsBlockedColumn::class, 'u'],
                'is_ignore' => [BooleanColumn::class, ['u' => 'is_ignore'], 'title' => 'Is ignore (product)'],
                'is_ignore_final' => [BooleanColumn::class, ['expr' => '(ui.user_id IS NOT NULL)', 'ui'], 'title' => 'Is ignore (final)'],
                'is_toxic' => [IsToxicColumn::class, 'u'],
                'kyc' => [UserKycColumn::class, 'kyc'],
                'kyc_source' => [UserKycSourceColumn::class, self::M_KYC_DOC_SOURCE],
                'kyc_auto_type' => [UserKycAutoTypeColumn::class, self::M_KYC_AUTO_TYPE],
                'kyc_updated_at' => [DateColumn::class, ['kyc' => 'status_updated_at'], 'title' => 'KYC updated at'],
                'verified_full_names' => [SimpleColumn::class, ['expr' => "CASE u.locale WHEN 'ru' THEN COALESCE(udtml.verified_full_names, u.name) ELSE u.name END", 'u', 'udtml:n'], 'title' => 'Verified full names'],
                'bonus_black_list' => [UserBblStatusColumn::class, 'u'],
                'comment_withdraw' => [UserCommentWithdrawColumn::class, ['wc', 'wc:n' => 'comment']],
                'user_block_reason' => [UsersBlocksReasonColumn::class, 'ub'],
                'user_block_comment' => [CommentColumn::class, 'ubc'],
                'auto_withdrawals' => [AutoWithdrawalsMetricColumn::class, self::M_AUTO_WITHDRAWALS],
            ],
            'Multi-accounts' => [
                'cid' => [UserCidColumn::class, 'u'],
                'face_ids' => [SimpleColumn::class, ['faces' => 'face_ids', 'faces:n'], 'title' => 'Face ID'],
                'multi_account_parent_user_id' => [UserIdColumn::class, ['uma' => 'parent_user_id'], 'title' => 'Multi account parent user ID'],
                'multi_account_reason' => [SimpleColumn::class, ['uma' => 'reason'], 'title' => 'Multi account reason'],
                'uuid' => [UserUuidColumn::class, 'u'],
            ],
            'Profile' => [
                'name' => [SimpleColumn::class, ['u' => 'name'], 'title' => 'Name'],
                'login' => [SimpleColumn::class, ['u' => 'login'], 'title' => 'Login'],
                'phone' => [PhoneColumn::class, 'u'],
                'phone_confirm' => [BooleanColumn::class, ['u' => 'phone_confirm'], 'title' => 'Phone is confirmed'],
                'phone_confirmed_at' => [DateColumn::class, ['u' => 'phone_confirmed_at'], 'title' => 'Phone confirmed at'],
                'email' => [EmailColumn::class, 'u'],
                'email_confirm' => [BooleanColumn::class, ['u' => 'email_confirm'], 'title' => 'Email is confirmed'],
                'email_confirmed_at' => [DateColumn::class, ['u' => 'email_confirmed_at'], 'title' => 'Email confirmed at'],
                'email_domain_share' => [PercentColumn::class, ['expr' => '(sed.share * 100)', 'sed'], 'title' => 'Email domain share'],
                'alt_email' => [EmailColumn::class, ['u' => 'alt_email'], 'title' => 'Alternative email'],
                'alt_email_confirm' => [BooleanColumn::class, ['u' => 'alt_email_confirm'], 'title' => 'Alternative email confirm'],
                'unsubscribed' => [BooleanColumn::class, ['expr' => '(uc.unsubscribe_level IS NOT NULL)', 'uc'], 'title' => 'Unsubscribed'],
                'self_excluded' => [DateColumn::class, ['usi' => 'self_excluded_at'], 'title' => 'Self-Excluded at'],
                'is_ludoman' => [DateColumn::class, ['usi' => 'is_ludoman_at'], 'title' => 'Is ludoman at'],
                'contact_status' => [UserContactStatusColumn::class, 'uc', 'title' => 'Email Status'],
                'esp_id' => [UserContactServiceProviderColumn::class, 'uc', 'title' => 'Email Service Provider'],
                'birthday' => [BirthdayColumn::class, ['u', 'usi'], 'otherSources' => ['usi.birthday']],
                'age' => [AgeColumn::class, ['u', 'usi'], 'otherSources' => ['usi.birthday']],
                'gender' => [GenderColumn::class, 'u', 'title' => 'Gender'],
                'address' => [SimpleColumn::class, ['u' => 'address'], 'title' => 'Address'],
            ],
            'Reg info' => [
                'registered_at' => [DateColumn::class, 'u', 'title' => 'Reg date'],
                'useragent_id' => [UseragentColumn::class, 'uag'],
                'variant_id' => [UseragentVariantColumn::class, 'uag'],
                'variant_version' => [UseragentVariantVersionColumn::class, 'uag'],
                'app_id' => [UseragentAppColumn::class, 'uag'],
                'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
                'platform_id' => [UseragentPlatformColumn::class, 'uag'],
                'platform_version' => [UseragentPlatformVersionColumn::class, 'uag'],
                'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
                'browser_id' => [UseragentBrowserColumn::class, 'uag'],
                'device_id' => [UseragentDeviceColumn::class, 'uag'],
                'refcode' => [RefcodeColumn::class, 'r'],
                'traffic_source' => [TrafficSourceColumn::class, 'r'],
                'aff_data' => [AffDataColumn::class, 'ad'],
                'aff_params' => [AffParamsColumn::class, 'ap'],
                'country' => [CountryColumn::class, 'u'],
                'city' => [CityColumn::class, 'c'],
                'ip' => [IpColumn::class, 'u'],
                'locale' => [UserLocaleColumn::class, 'u'],
                'affiliate_id' => [WebmasterIdColumn::class, 'r'],
                'publisher' => [MarketingTidPublisherColumn::class, 'tid'],
                'registration_method' => [UserRegistrationMethodColumn::class, 'u'],
                'aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
                'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
                'brand_id' => [BrandColumn::class, 'u'],
                'host_is_app' => [SiteHostIsAppColumn::class, 'sh'],
                'location' => [LocationColumn::class, 'u'],
            ],
            'Payments' => [
                'dep_first_at' => [UserFirstDepDateColumn::class, 'usi'],
                'dep_first_month' => [MonthColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Month (FD)'],
                'dep_first_week' => [WeekColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Week (FD)'],
                'dep_first_day' => [DayColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Day (FD)'],
                'dep_last_at' => [UserLastDepDateColumn::class, 'usi'],
                'wd_last_at' => [UserLastWdDateColumn::class, 'usi'],
                'login_last_at' => [UserLastLoginDateColumn::class, 'usi'],
                'count_in' => [CountColumn::class, ['expr' => 'usi.dep_lt_count', 'usi'], 'title' => 'Dep count'],
                'sum_in_usd' => [MoneyColumn::class, ['usi' => 'dep_lt_usd'], 'title' => 'Dep sum (USD)'],
                'sum_in_eur' => [MoneyColumn::class, ['usi' => 'dep_lt_eur'], 'title' => 'Dep sum (EUR)'],
                'sum_in_orig' => [MoneyColumn::class, ['usi' => 'dep_lt_orig'], 'title' => 'Dep sum (ORIG)'],
                'sum_out_usd' => [MoneyColumn::class, ['usi' => 'wd_lt_usd'], 'title' => 'WD sum (USD)'],
                'sum_out_eur' => [MoneyColumn::class, ['usi' => 'wd_lt_eur'], 'title' => 'WD sum (EUR)'],
                'sum_out_orig' => [MoneyColumn::class, ['usi' => 'wd_lt_orig'], 'title' => 'WD sum (ORIG)'],
                'sum_cb_eur' => [MoneyColumn::class, ['sum_cb' => 'sum_cb_eur', 'sum_cb:n'], 'title' => 'Chargeback sum (EUR)'],
                'reg_to_first_dep_period' => [PeriodColumn::class, ['usi'], 'innerExpression' => 'usi.dep_first_at - u.date', 'title' => 'Reg to FD period'],
                'pay_bonus_ratio_lt' => [PercentColumn::class, ['expr' => '(usi.pay_bonus_ratio_lt * 100)', 'usi'], 'title' => 'Bonus/In (%)'],

                'requisites_all' => [RequisitesAllColumn::class, ['req', 'req:n' => 'requisites']],
                'requisites_cc' => [RequisitesCardColumn::class, ['req', 'req:n' => 'requisites']],
                'requisites_ym' => [RequisitesYmColumn::class, ['req', 'req:n' => 'requisites']],
                'requisites_ph' => [RequisitesPhoneColumn::class, ['req', 'req:n' => 'requisites']],

                'payout_limit_daily' => [MoneyColumn::class, [self::M_PAYOUT_LIMIT_DAILY => UserMetric::M_PAYOUT_LIMIT_DAILY[1]], 'title' => 'Payout limit (daily)'],
                'payout_limit_currency' => [CurrencyColumn::class, [self::M_PAYOUT_LIMIT_DAILY => UserMetric::COL_STRING], 'title' => 'Payout limit currency'],
                'chargeback_probability' => [ChargebackProbabilityColumn::class, self::M_CHARGEBACK_PROBABILITY],
                'vip_probability' => [VipProbabilityColumn::class, self::M_VIP_PROBABILITY],
                'vip_classification_at' => [DateTimeColumn::class, [self::M_VIP_PROBABILITY => 'updated_at'], 'title' => 'Vip classification date'],
            ],
            'Balances' => [
                'currency' => [CurrencyColumn::class, ['ubr' => 'currency', 'ubr:n' => 'currency']],

                'balance_real_usd' => [MoneyColumn::class, ['ubr' => 'balance_usd', 'ubr:n' => 'balance_usd'], 'title' => 'Real Balance (USD)'],
                'balance_real_eur' => [MoneyColumn::class, ['ubr' => 'balance_eur', 'ubr:n' => 'balance_eur'], 'title' => 'Real Balance (EUR)'],
                'balance_real_orig' => [MoneyColumn::class, ['ubr' => 'balance_orig', 'ubr:n' => 'balance_orig'], 'title' => 'Real Balance (orig)'],

                'balance_bonus_usd' => [MoneyColumn::class, ['ubb' => 'balance_usd', 'ubb:n' => 'balance_usd'], 'title' => 'Bonus Balance (USD)'],
                'balance_bonus_eur' => [MoneyColumn::class, ['ubb' => 'balance_eur', 'ubb:n' => 'balance_eur'], 'title' => 'Bonus Balance (EUR)'],
                'balance_bonus_orig' => [MoneyColumn::class, ['ubb' => 'balance_orig', 'ubb:n' => 'balance_orig'], 'title' => 'Bonus Balance (orig)'],
            ],
            'Game activity' => [
                'spin_last_at' => [DateColumn::class, ['usi' => 'spin_last_at'], 'title' => 'Last spin date'],
                'spin_first_at' => [DateColumn::class, ['usi' => 'spin_first_at'], 'title' => 'First spin date'],
                'bet_last_at' => [DateColumn::class, ['usi' => 'bet_last_at'], 'title' => 'Last bet date'],
                'bet_first_at' => [DateColumn::class, ['usi' => 'bet_first_at'], 'title' => 'First bet date'],
                self::M_BET_LT_COUNT => [SimpleColumn::class, [self::M_BET_LT_COUNT => UserMetric::M_BET_LT_COUNT[1]], 'title' => 'Bet count'],
                self::M_BET_LT_EUR => [MoneyColumn::class, [self::M_BET_LT_EUR => UserMetric::M_BET_LT_EUR[1]], 'title' => 'Bet sum (EUR)'],
                self::M_WIN_LT_EUR => [MoneyColumn::class, [self::M_WIN_LT_EUR => UserMetric::M_WIN_LT_EUR[1]], 'title' => 'Win sum (EUR)'],
                self::M_BET_LT_ORIG => [MoneyColumn::class, [self::M_BET_LT_ORIG => UserMetric::M_BET_LT_ORIG[1]], 'title' => 'Bet sum (orig)'],
                self::M_WIN_LT_ORIG => [MoneyColumn::class, [self::M_WIN_LT_ORIG => UserMetric::M_WIN_LT_ORIG[1]], 'title' => 'Win sum (orig)'],

                ...$this->favGames(),

                'chosen_games' => [SimpleColumn::class, ['gf' => 'chosen_games', 'gf:n'], 'title' => 'Chosen games'],
                'activity_score' => [SimpleColumn::class, ['ugpa' => 'score'], 'title' => 'Activity score'],
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
            ],
            'Support' => [
                'full_status' => [UserFullStatusColumn::class, 'u'],
                'status_updated_at' => [DateColumn::class, ['u' => 'status_updated_at'], 'title' => 'Status updated at'],
                'personal_manager' => [UserPersonalManagerColumn::class, 'u'],
                'crm_group' => [UserCrmGroupColumn::class, 'usi'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'no_call' => [BooleanColumn::class, ['usi' => 'no_call'], 'title' => 'No call flag'],
                'no_write' => [BooleanColumn::class, ['usi' => 'no_write'], 'title' => 'No write flag'],
                'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            ],
            'Social' => [
                'social_net' => [SocialNetColumn::class, 'u'],
                'social_key' => [UserSocialKeyColumn::class, 'u'],
                'soc_email' => [EmailColumn::class, 'ul4p', 'title' => 'Email (S)'],
                'soc_phone' => [PhoneColumn::class, 'ul4p', 'title' => 'Phone (S)'],
                'soc_first_name' => [SimpleColumn::class, ['ul4p' => 'first_name'], 'title' => 'First name (S)'],
                'soc_last_name' => [SimpleColumn::class, ['ul4p' => 'last_name'], 'title' => 'Last name (S)'],
                'soc_country' => [SimpleColumn::class, ['ul4p' => 'country'], 'title' => 'Country (S)'],
                'soc_city' => [SimpleColumn::class, ['ul4p' => 'city'], 'title' => 'City (S)'],
                'soc_gender' => [SimpleColumn::class, ['ul4p' => 'gender'], 'title' => 'Gender (S)'],
                'soc_birthday' => [SimpleColumn::class, ['ul4p' => 'birthday'], 'title' => 'Birthday (S)'],
                'soc_profile' => [SimpleColumn::class, ['ul4p' => 'profile'], 'title' => 'Profile (S)'],
            ],
        ];

        if ($this->canViewCustomer) {
            $result['Customer'] = [
                'customer_id' => [SimpleColumn::class, ['cust' => 'customer_id', 'cust:n'], 'title' => 'Customer ID'],
                'customer_names' => [SimpleColumn::class, ['cust' => 'customer_names', 'cust:n'], 'title' => 'Name'],
                'customer_jobs' => [SimpleColumn::class, ['cust' => 'customer_jobs', 'cust:n'], 'title' => 'Job'],
                'customer_social_links' => [SimpleColumn::class, ['cust' => 'customer_social_links', 'cust:n'], 'title' => 'Social link'],
                'customer_addresses' => [SimpleColumn::class, ['cust' => 'customer_addresses', 'cust:n'], 'title' => 'Address'],
                'customer_comments' => [SimpleColumn::class, ['cust' => 'customer_comments', 'cust:n'], 'title' => 'Comments'],
                'customer_descriptions' => [SimpleColumn::class, ['cust' => 'customer_descriptions', 'cust:n'], 'title' => 'Description'],
            ];
        }

        if ($this->canViewTaxNumber) {
            $result['User']['tax_number'] = [SimpleColumn::class, ['usi' => 'tax_number'], 'title' => 'Tax number'];
            $result['User']['tax_number_type'] = [SimpleColumn::class, ['usi' => 'tax_number_type'], 'title' => 'Tax number type'];
        }

        return $result;
    }

    public function metrics(): array
    {
        return [
            'Counts' => [
                'count' => [CountColumn::class, 'u'],
            ],
        ];
    }

    public function groups(): array
    {
        return [
            'User' => [
                'site_id' => [SiteIdColumn::class, 'u'],
                'is_blocked' => [UserIsBlockedColumn::class, 'u'],
                'is_ignore' => [BooleanColumn::class, ['u' => 'is_ignore'], 'title' => 'Is ignore (product)'],
                'is_ignore_final' => [BooleanColumn::class, ['expr' => '(ui.user_id IS NOT NULL)', 'ui'], 'title' => 'Is ignore (final)'],
                'is_toxic' => [IsToxicColumn::class, 'u'],
                'kyc' => [UserKycColumn::class, 'kyc'],
                'kyc_source' => [UserKycSourceColumn::class, self::M_KYC_DOC_SOURCE],
                'kyc_auto_type' => [UserKycAutoTypeColumn::class, self::M_KYC_AUTO_TYPE],
                'bonus_black_list' => [UserBblStatusColumn::class, 'u'],
            ],
            'Multi-accounts' => [
                'cid' => [UserCidColumn::class, 'u'],
                'uuid' => [UserUuidColumn::class, 'u'],
            ],
            'Profile' => [
                'phone_confirm' => [BooleanColumn::class, ['u' => 'phone_confirm'], 'title' => 'Phone confirm'],
                'email_confirm' => [BooleanColumn::class, ['u' => 'email_confirm'], 'title' => 'Email confirm'],
                'unsubscribed' => [BooleanColumn::class, ['expr' => '(uc.unsubscribe_level IS NOT NULL)', 'uc'], 'title' => 'Unsubscribed'],
                'self_excluded' => [DateColumn::class, ['usi' => 'self_excluded_at'], 'title' => 'Self-Excluded at'],
                'is_ludoman' => [DateColumn::class, ['usi' => 'is_ludoman_at'], 'title' => 'Is ludoman at'],
                'birthday' => [BirthdayColumn::class, ['u', 'usi'], 'otherSources' => ['usi.birthday']],
                'age' => [AgeColumn::class, ['u', 'usi'], 'otherSources' => ['usi.birthday']],
                'gender' => [GenderColumn::class, 'u', 'title' => 'Gender'],
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
            ],
            'Reg info' => [
                'reg_month' => [MonthColumn::class, 'u', 'title' => 'Reg month'],
                'reg_week' => [WeekColumn::class, 'u', 'title' => 'Reg week'],
                'reg_day' => [DayColumn::class, 'u', 'title' => 'Reg day'],
                'reg_hour' => [HourColumn::class, 'u', 'title' => 'Reg hour'],
                'reg_minute_10' => [Minute10Column::class, 'u', 'title' => 'Reg 10 minute'],
                'reg_weekday' => [WeekDayColumn::class, 'u', 'title' => 'Reg weekday'],

                'useragent_id' => [UseragentColumn::class, 'uag'],
                'variant_id' => [UseragentVariantColumn::class, 'uag'],
                'variant_version' => [UseragentVariantVersionColumn::class, 'uag'],
                'app_id' => [UseragentAppColumn::class, 'uag'],
                'app_group_id' => [UseragentAppGroupColumn::class, 'uaga'],
                'platform_id' => [UseragentPlatformColumn::class, 'uag'],
                'platform_version' => [UseragentPlatformVersionColumn::class, 'uag'],
                'platform_group' => [UseragentPlatformGroupColumn::class, 'uagp'],
                'browser_id' => [UseragentBrowserColumn::class, 'uag'],
                'device_id' => [UseragentDeviceColumn::class, 'uag'],
                'refcode' => [RefcodeColumn::class, 'r'],
                'traffic_source' => [TrafficSourceColumn::class, 'r'],
                'country' => [CountryColumn::class, 'u'],
                'city' => [CityColumn::class, 'c'],
                'ip' => [IpColumn::class, 'u'],
                'locale' => [UserLocaleColumn::class, 'u'],
                'affiliate_id' => [WebmasterIdColumn::class, 'r'],
                'publisher' => [MarketingTidPublisherColumn::class, 'tid'],
                'registration_method' => [UserRegistrationMethodColumn::class, 'u'],
                'aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
                'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
                'brand_id' => [BrandColumn::class, 'u'],
                'host_is_app' => [SiteHostIsAppColumn::class, 'sh'],
                'location' => [LocationColumn::class, 'u'],
            ],
            'Payments' => [
                'currency' => [CurrencyColumn::class, ['ubr' => 'currency', 'ubr:n']],

                'dep_first_month' => [MonthColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Month (FD)'],
                'dep_first_week' => [WeekColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Week (FD)'],
                'dep_first_day' => [DayColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Day (FD)'],
                'count_in' => [CountColumn::class, ['expr' => 'usi.dep_lt_count', 'usi'], 'title' => 'Dep count'],
                'sum_in_usd' => [MoneyColumn::class, ['usi' => 'dep_lt_usd'], 'title' => 'Dep sum (USD)'],
                'sum_out_usd' => [MoneyColumn::class, ['usi' => 'wd_lt_usd'], 'title' => 'WD sum (USD)'],
            ],
            'Game activity' => [
                ...$this->favGames(),
                'activity_score' => [SimpleColumn::class, ['ugpa' => 'score'], 'title' => 'Activity score'],
            ],
            'Support' => [
                'full_status' => [UserFullStatusColumn::class, 'u'],
                'personal_manager' => [UserPersonalManagerColumn::class, 'u'],
                'crm_group' => [UserCrmGroupColumn::class, 'usi'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'no_call' => [BooleanColumn::class, ['usi' => 'no_call'], 'title' => 'No call flag'],
                'no_write' => [BooleanColumn::class, ['usi' => 'no_write'], 'title' => 'No write flag'],
                'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            ],
            'Social' => [
                'social_net' => [SocialNetColumn::class, 'u'],
                'soc_country' => [SimpleColumn::class, ['ul4p' => 'country'], 'title' => 'Country (S)'],
                'soc_city' => [SimpleColumn::class, ['ul4p' => 'city'], 'title' => 'City (S)'],
                'soc_gender' => [SimpleColumn::class, ['ul4p' => 'gender'], 'title' => 'Gender (S)'],
                'soc_birthday' => [SimpleColumn::class, ['ul4p' => 'birthday'], 'title' => 'Birthday (S)'],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'f' => [function (Query $query, QueryRequest $nestedRequest) {
                $query->withQuery((new UsersInfoFilterQueryConfig($this->container))->query($this->db, $nestedRequest), self::FILTER_TABLE);
                return self::FILTER_TABLE;
            }],
            'u' => [Users::TABLE_NAME, 'u.site_id = f.site_id AND u.user_id = f.user_id'],
            'usi' => [UserSpecialInfos::TABLE_NAME, 'usi.site_id = f.site_id AND usi.user_id = f.user_id'],
            'req' => [UsersInfoRequisitesQueryConfig::class, 'req.site_id = f.site_id AND req.user_id = f.user_id'],
            'gf' => [UsersInfoGamesChosenQueryConfig::class, 'gf.site_id = f.site_id AND gf.user_id = f.user_id'],
            'ubr' => [
                function (Query $query, QueryRequest $nestedRequest) {
                    $nestedRequest->addFilter('type', UserWallet::TYPE_REAL);
                    return (new UsersInfoBalancesQueryConfig($this->container))->query($this->db, $nestedRequest);
                },
                'ubr.site_id = f.site_id AND ubr.user_id = f.user_id',
            ],
            'ubb' => [
                function (Query $query, QueryRequest $nestedRequest) {
                    $nestedRequest->addFilter('type', UserWallet::TYPE_BONUS);
                    return (new UsersInfoBalancesQueryConfig($this->container))->query($this->db, $nestedRequest);
                },
                'ubb.site_id = f.site_id AND ubb.user_id = f.user_id',
            ],
            'uc' => [UserContacts::TABLE_NAME, 'uc.site_id = u.site_id AND uc.user_id = u.user_id AND uc.value = u.email AND uc.type = ' . UserContact::TYPE_EMAIL, ['u']],
            'r' => [Refcodes::TABLE_NAME, 'r.id = u.refcode_id', ['u']],
            'ul4p' => [UserLogin4plays::TABLE_NAME, 'ul4p.social_id = u.social_id AND ul4p.social_key = u.social_key', ['u']],
            'ad' => [AffData::TABLE_NAME, 'ad.id = u.aff_data_id', ['u']],
            'ui' => [UserIgnoreIds::TABLE_NAME, 'ui.site_id = f.site_id AND ui.user_id = f.user_id'],
            'ul' => [UserLoyalties::TABLE_NAME, 'ul.site_id = f.site_id AND ul.user_id = f.user_id'],
            'ls' => [LoyaltyStatuses::TABLE_NAME, 'ls.site_id = u.site_id AND ls.id = COALESCE(ul.status_id, 1)', ['ul']],
            'tid' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r', 'tid'), ['r']],
            'uma' => [UserMultiAccounts::TABLE_NAME, 'uma.site_id = f.site_id and uma.user_id = f.user_id'],
            'uag' => [Useragents::TABLE_NAME, 'uag.id = u.useragent_id', ['u']],
            'uaga' => [UseragentApps::TABLE_NAME, 'uaga.id = uag.app_id', ['uag']],
            'uagp' => [UseragentPlatforms::TABLE_NAME, 'uagp.id = uag.platform_id', ['uag']],
            'faces' => [UsersInfoFacesQueryConfig::class, 'faces.site_id = f.site_id AND faces.user_id = f.user_id'],
            'udtml' => [UsersInfoDocumentsFullNamesQueryConfig::class, 'udtml.site_id = f.site_id AND udtml.user_id = f.user_id'],
            'sh' => [Hosts::TABLE_NAME, 'sh.id = u.host_id', ['u']],
            'cust' => [UsersInfoCustomerQueryConfig::class, 'cust.site_id = f.site_id AND cust.user_id = f.user_id'],
            'wp_w' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w', 'r'), ['r']],
            'ap' => [AffParams::paramsAsObjJoinQuery('u'), '', ['u'], 'CROSS JOIN LATERAL'],
            'sed' => [SiteEmailDomains::TABLE_NAME, 'sed.site_id = u.site_id AND sed.domain = ' . SiteEmailDomains::domainFromEmailExpression('u.email')],
            'ubps' => [UserBettingProfitSegments::TABLE_NAME, 'ubps.site_id = u.site_id AND ubps.user_id = u.user_id'],
            'ugpa' => [UserGamePaymentActivities::TABLE_NAME, 'ugpa.site_id = u.site_id AND ugpa.user_id = u.user_id'],
            'wc' => [UsersInfoCommentsQueryConfig::class, 'wc.site_id = u.site_id AND wc.user_id = u.user_id'],
            'ub' => [UserBlocks::TABLE_NAME, 'ub.site_id = u.site_id AND ub.user_id = u.user_id AND ub.type = ' . UserBlock::BLOCK_TYPE_LOGIN, ['u']],
            'ubc' => [UserBlockComments::TABLE_NAME, 'ubc.id = ub.comment_id', ['ub']],
            'sum_cb' => [UsersInfoChargebacksQueryConfig::class, 'sum_cb.site_id = f.site_id AND sum_cb.user_id = f.user_id'],
            'c' => [Cities::TABLE_NAME, 'c.id = u.city_id'],
            'kyc' => [UserKycs::TABLE_NAME, 'kyc.site_id = f.site_id AND kyc.user_id = f.user_id'],
            ...self::metricsTables('f'),
        ];
    }

    public static function metricsTables(string $toTableAlias): array
    {
        $m = [
            self::M_KYC_DOC_SOURCE => UserMetric::M_KYC_DOC_SOURCE,
            self::M_KYC_AUTO_TYPE => UserMetric::M_KYC_AUTO_TYPE,
            self::M_PAYOUT_LIMIT_DAILY => UserMetric::M_PAYOUT_LIMIT_DAILY,
            self::M_FAV_GAME_LT_1 => UserMetric::M_FAV_GAME_LT_1,
            self::M_FAV_GAME_7_1 => UserMetric::M_FAV_GAME_7_1,
            self::M_FAV_GAME_30_1 => UserMetric::M_FAV_GAME_30_1,
            self::M_BET_LT_COUNT => UserMetric::M_BET_LT_COUNT,
            self::M_BET_LT_EUR => UserMetric::M_BET_LT_EUR,
            self::M_WIN_LT_EUR => UserMetric::M_WIN_LT_EUR,
            self::M_BET_LT_ORIG => UserMetric::M_BET_LT_ORIG,
            self::M_WIN_LT_ORIG => UserMetric::M_WIN_LT_ORIG,
            self::M_AUTO_WITHDRAWALS => UserMetric::M_AUTO_WITHDRAWALS_DISABLE,
            self::M_CHARGEBACK_PROBABILITY => UserMetric::M_CHARGEBACK_PROBABILITY_PER_MILLE,
            self::M_VIP_PROBABILITY => UserMetric::M_VIP_PROBABILITY_PER_MILLE,
        ];

        $result = [];
        foreach ($m as $alias => $metric) {
            $result[$alias] = [UserMetrics::TABLE_NAME, UserMetrics::metricJoinCondition($alias, $toTableAlias, $metric[0])];
        }

        return $result;
    }

    private function favGames(): array
    {
        return [
            self::M_FAV_GAME_7_1 => [GameColumn::class, [self::M_FAV_GAME_7_1 => UserMetric::M_FAV_GAME_LT_1[1]], 'title' => 'Favorite game (week)'],
            self::M_FAV_GAME_30_1 => [GameColumn::class, [self::M_FAV_GAME_30_1 => UserMetric::M_FAV_GAME_30_1[1]], 'title' => 'Favorite game (month)'],
            self::M_FAV_GAME_LT_1  => [GameColumn::class, [self::M_FAV_GAME_LT_1 => UserMetric::M_FAV_GAME_LT_1[1]], 'title' => 'Favorite game (lifetime)'],
        ];
    }
}
