<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\UsersInfo;

use app\back\entities\Rate;
use app\back\entities\UserMetric;
use app\back\entities\UserWallet;
use app\back\modules\reports\columns\AgeColumn;
use app\back\modules\reports\columns\BirthdayColumn;
use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\AutoWithdrawalsMetricColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\ChargebackProbabilityColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CountFilterColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CurrencyFilterColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DateTimeColumn;
use app\back\modules\reports\columns\EmailColumn;
use app\back\modules\reports\columns\FaceIdColumn;
use app\back\modules\reports\columns\GameColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\IsToxicColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\MoneyFilterColumn;
use app\back\modules\reports\columns\PhoneColumn;
use app\back\modules\reports\columns\RefcodeColumn;
use app\back\modules\reports\columns\RequisiteColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\SocialNetColumn;
use app\back\modules\reports\columns\TaxNumberColumn;
use app\back\modules\reports\columns\TaxNumberTypeColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UserActiveStatusColumn;
use app\back\modules\reports\columns\UserBblStatusColumn;
use app\back\modules\reports\columns\UserBettingProfitSegmentColumn;
use app\back\modules\reports\columns\UserCidColumn;
use app\back\modules\reports\columns\UserCrmGroupColumn;
use app\back\modules\reports\columns\UserFirstDepDateColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UserIsBlockedColumn;
use app\back\modules\reports\columns\UserIsRmColumn;
use app\back\modules\reports\columns\UserKycAutoTypeColumn;
use app\back\modules\reports\columns\UserKycColumn;
use app\back\modules\reports\columns\UserKycSourceColumn;
use app\back\modules\reports\columns\UserLastDepDateColumn;
use app\back\modules\reports\columns\UserLocaleColumn;
use app\back\modules\reports\columns\UserPersonalManagerColumn;
use app\back\modules\reports\columns\UserPriorityTypeOfGamblingColumn;
use app\back\modules\reports\columns\UserSocialKeyColumn;
use app\back\modules\reports\columns\UserStatusColumn;
use app\back\modules\reports\columns\UserUuidColumn;
use app\back\modules\reports\columns\VipProbabilityColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\Cities;
use app\back\repositories\MarketingTids;
use app\back\repositories\Refcodes;
use app\back\repositories\Requisites;
use app\back\repositories\UserBettingProfitSegments;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserGameChosens;
use app\back\repositories\UserKycs;
use app\back\repositories\UserRequisites;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserWallets;
use app\back\repositories\WpWebmasters;
use Yiisoft\Db\Query\Query;

class UsersInfoFilterQueryConfig extends BaseQueryConfig
{
    protected function beforeQuery(): void
    {
        $this->request
            ->select('site_id', 'user_id')
            ->group('site_id', 'user_id');
    }

    public function selects(): array
    {
        return [
            'cid' => [UserCidColumn::class, 'u'],
            'site_id' => [SiteIdColumn::class, 'u'],
            'user_id' => [UserIdColumn::class, 'u'],
        ];
    }

    public function filters(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'u'],
            'user_id' => [UserIdColumn::class, 'u'],
            'site_user' => [SiteUserColumn::class, 'u'],
            'cid' => [UserCidColumn::class, 'u'],
            'ignore' => [IgnoreColumn::class, 'u'],
            'is_ignore' => [BooleanColumn::class, ['u' => 'is_ignore']],
            'is_blocked' => [UserIsBlockedColumn::class, 'u'],
            'is_toxic' => [IsToxicColumn::class, 'u'],
            'currency' => [CurrencyFilterColumn::class, 'uwb', 'currencies' => Rate::currencies(), 'allowMultiple' => true],
            'email' => [EmailColumn::class, 'u'],
            'phone' => [PhoneColumn::class, 'u'],
            'email_confirmed_at' => [DateColumn::class, ['u' => 'email_confirmed_at']],
            'phone_confirmed_at' => [DateColumn::class, ['u' => 'phone_confirmed_at']],
            'social_net' => [SocialNetColumn::class, 'u'],
            'social_key' => [UserSocialKeyColumn::class, 'u'],
            'requisite' => [RequisiteColumn::class, ['req' => 'requisite']],
            'registered_at' => [DateColumn::class, 'u'],
            'country' => [CountryColumn::class, 'u'],
            'city' => [CityColumn::class, 'c'],
            'locale' => [UserLocaleColumn::class, 'u'],
            'brand_id' => [BrandColumn::class, 'u'],
            'uuid' => [UserUuidColumn::class, 'u'],
            'ts' => [TrafficSourceColumn::class, 'r'],
            'publisher' => [MarketingTidPublisherColumn::class, 'tid'],
            'refcode' => [RefcodeColumn::class, 'r'],
            'webmaster_id' => [WebmasterIdColumn::class, 'r'],
            'status' => [UserStatusColumn::class, 'u'],
            'active_status' => [UserActiveStatusColumn::class, 'u'],
            'is_rm' => [UserIsRmColumn::class, 'u'],
            'crm_group' => [UserCrmGroupColumn::class, 'usi'],
            'personal_manager' => [UserPersonalManagerColumn::class, 'u'],
            'face_id' => [FaceIdColumn::class, 'udf'],
            'birthday' => [BirthdayColumn::class, ['u', 'usi'], 'otherSources' => ['usi.birthday']],
            'age' => [AgeColumn::class, ['u', 'usi'], 'otherSources' => ['usi.birthday']],
            'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
            UsersInfoConfig::M_FAV_GAME_7_1 => [GameColumn::class, [UsersInfoConfig::M_FAV_GAME_7_1 => UserMetric::M_FAV_GAME_7_1[1]]],
            UsersInfoConfig::M_FAV_GAME_30_1 => [GameColumn::class, [UsersInfoConfig::M_FAV_GAME_30_1 => UserMetric::M_FAV_GAME_30_1[1]]],
            UsersInfoConfig::M_FAV_GAME_LT_1  => [GameColumn::class, [UsersInfoConfig::M_FAV_GAME_LT_1 => UserMetric::M_FAV_GAME_LT_1[1]]],
            UsersInfoConfig::M_BET_LT_COUNT => [CountFilterColumn::class, UsersInfoConfig::M_BET_LT_COUNT, 'expression' => UsersInfoConfig::M_BET_LT_COUNT . '.' . UserMetric::M_BET_LT_COUNT[1]],
            UsersInfoConfig::M_BET_LT_EUR => [MoneyFilterColumn::class, UsersInfoConfig::M_BET_LT_EUR, 'expression' => UsersInfoConfig::M_BET_LT_EUR . '.' . UserMetric::M_BET_LT_EUR[1]],
            'bet_last_at' => [DateColumn::class, ['usi' => 'bet_last_at']],
            'dep_first_at' => [UserFirstDepDateColumn::class, 'usi'],
            'dep_last_at' => [UserLastDepDateColumn::class, 'usi'],
            'wd_last_at' => [DateColumn::class, ['usi' => 'wd_last_at']],
            'balance_real_usd' => [MoneyFilterColumn::class, 'uwb', 'expression' => 'SUM(uwb.balance_usd)', 'having' => true],
            'dep_lt_usd' => [MoneyFilterColumn::class, 'usi', 'expression' => 'usi.dep_lt_usd'],
            'dep_lt_count' => [CountFilterColumn::class, 'usi', 'expression' => 'usi.dep_lt_count'],
            'wd_lt_count' => [CountFilterColumn::class, 'usi', 'expression' => 'usi.wd_lt_count'],
            'kyc' => [UserKycColumn::class, 'kyc'],
            'kyc_source' => [UserKycSourceColumn::class, 'um_kyc_source'],
            'kyc_auto_type' => [UserKycAutoTypeColumn::class, 'um_kyc_auto_type'],
            'kyc_updated_at' => [DateColumn::class, ['kyc' => 'status_updated_at']],
            'aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
            'aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w', 'refcodeTableAlias' => 'r'],
            'chosen_games' => [GameColumn::class, ['ugf' => 'game_id']],
            'tax_number' => [TaxNumberColumn::class, 'usi'],
            'tax_number_type' => [TaxNumberTypeColumn::class, 'usi'],
            'bonus_black_list' => [UserBblStatusColumn::class, 'u'],
            'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            'location' => [LocationColumn::class, 'u'],
            'auto_withdrawals' => [AutoWithdrawalsMetricColumn::class, UsersInfoConfig::M_AUTO_WITHDRAWALS],
            'chargeback_probability' => [ChargebackProbabilityColumn::class, UsersInfoConfig::M_CHARGEBACK_PROBABILITY],
            'vip_probability' => [VipProbabilityColumn::class, UsersInfoConfig::M_VIP_PROBABILITY],
            'vip_classification_at' => [DateTimeColumn::class, [UsersInfoConfig::M_VIP_PROBABILITY => 'updated_at'], 'title' => 'Vip classification date'],
        ];
    }

    public function groups(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'u'],
            'user_id' => [UserIdColumn::class, 'u'],
        ];
    }

    public function tableMap(): array
    {
        return [
            'u' => [Users::TABLE_NAME],
            'r' => [Refcodes::TABLE_NAME, 'r.id = u.refcode_id'],
            'ur' => [UserRequisites::TABLE_NAME, 'ur.site_id = u.site_id AND ur.user_id = u.user_id'],
            'req' => [Requisites::TABLE_NAME, 'req.id = ur.requisite_id', ['ur']],
            'usi' => [UserSpecialInfos::TABLE_NAME, 'usi.site_id = u.site_id AND usi.user_id = u.user_id'],
            'tid' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r', 'tid'), ['r']],
            'ud' => [UserDocumentsActive::TABLE_NAME, 'ud.site_id = u.site_id AND ud.user_id = u.user_id'],
            'udf' => [UserDocumentFaces::TABLE_NAME, 'udf.user_document_id = ud.id', ['ud']],
            'wp_w' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w', 'r'), ['r']],
            'ugf' => [UserGameChosens::TABLE_NAME, 'ugf.site_id = u.site_id AND ugf.user_id = u.user_id AND ugf.is_deleted = false'],
            'ubps' => [UserBettingProfitSegments::TABLE_NAME, 'ubps.site_id = u.site_id AND ubps.user_id = u.user_id'],
            'c' => [Cities::TABLE_NAME, 'c.id = u.city_id'],
            'uwb' => [
                fn() => $this->userWalletsQuery(),
                'u.site_id = uwb.site_id AND u.user_id = uwb.user_id',
                ['u']
            ],
            'kyc' => [UserKycs::TABLE_NAME, 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id'],
            ...UsersInfoConfig::metricsTables('u')
        ];
    }

    private function userWalletsQuery(): Query
    {
        return (new Query($this->db))
            ->select([
                'site_id' => 'uw.site_id',
                'user_id' => 'uw.user_id',
                'balance_usd' => 'SUM(uw.balance_usd)',
                'currency' => 'ANY_VALUE(uw.currency)'
            ])
            ->from(['uw' => UserWallets::TABLE_NAME])
            ->where(['AND',
                ['uw.type' => UserWallet::TYPE_REAL],
                UserWallets::getActiveCondition('uw'),
            ])
            ->groupBy(['uw.site_id', 'uw.user_id']);
    }
}
