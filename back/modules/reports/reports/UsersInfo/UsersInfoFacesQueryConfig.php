<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\UsersInfo;

use app\back\modules\reports\columns\SimpleColumn;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\views\UserDocumentsActive;

class UsersInfoFacesQueryConfig extends UsersInfoSiteUserBaseQueryConfig
{
    protected function beforeQuery(): void
    {
        parent::beforeQuery();

        $this->query
            ->where(['IS NOT', 'udf.similar_id', null]);
    }

    public function additionalSelects(): array
    {
        return [
            'face_ids' => [SimpleColumn::class, ['expr' => "ARRAY_TO_STRING(ARRAY_AGG(DISTINCT udf.similar_id ORDER BY udf.similar_id), ', ')", 'udf']],
        ];
    }

    public function tableMap(): array
    {
        return array_merge($this->usersTableMap(), [
            'ud' => [UserDocumentsActive::TABLE_NAME, 'ud.site_id = f.site_id AND ud.user_id = f.user_id'],
            'udf' => [UserDocumentFaces::TABLE_NAME, 'udf.user_document_id = ud.id', ['ud']],
        ]);
    }
}
