<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\WithdrawalsEfficiency;

use app\back\components\helpers\DateHelper;
use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\CommentColumn;
use app\back\modules\reports\columns\CountColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\JsonFieldColumn;
use app\back\modules\reports\columns\ModeColumn;
use app\back\modules\reports\columns\MoneyColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\SiteUserColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UsersStatsStatusColumn;
use app\back\modules\reports\columns\UsersTransactionsTransactionIdColumn;
use app\back\modules\reports\columns\UserStatusColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\Employees;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\back\repositories\Withdrawals;
use Yiisoft\Db\Query\Query;

class WithdrawalsEfficiencyConfig extends BaseReportConfig
{
    private const string DATE_TYPE_CREATED = 'created_at';
    private const string DATE_TYPE_UPDATED = 'updated_at';
    private const string DATE_TYPE_PROCESSED = 'processed_at';

    private const array DATE_TYPES = [
        self::DATE_TYPE_CREATED => 'Created',
        self::DATE_TYPE_UPDATED => 'Modified',
        self::DATE_TYPE_PROCESSED => 'Processed',
    ];

    private const array DATE_TABLE_COLUMN = [
        self::DATE_TYPE_CREATED => ['ut' => 'created_at'],
        self::DATE_TYPE_UPDATED => ['ut' => 'updated_at'],
        self::DATE_TYPE_PROCESSED => ['w' => 'created_at'],
    ];

    private array $dateColumnConfig = ['ut' => 'created_at'];

    public function beforeQuery(): void
    {
        $this->dateColumnConfig = self::DATE_TABLE_COLUMN[$this->request->getFilter('date_type')] ?? $this->dateColumnConfig;

        if ($this->request->isGroupedBy('upd_day')) {
            $this->request->orders['upd_day'] = SORT_ASC;
        }

        parent::beforeQuery();
    }

    public function rules(): array
    {
        return [
            [['date', 'date_type'], 'required'],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), '>='],
            ['date', DateHelper::yesterday(), '<='],
            ['date_type', self::DATE_TYPE_UPDATED],
            ['columns', ['site_id', 'user_id', 'site_user_id', 'transaction_id', 'status', 'created_at', 'updated_at', 'sum_orig', 'currency', 'user_status', 'decision', 'reason', 'decision_made_by']],
            ['metrics', ['count']],
            ['groups', ['decision_made_by']],
        ];
    }

    public function filters(): array
    {
        return [
            'General' => [
                'site_id' => [SiteIdColumn::class, 'ut'],
                'date' => [DateColumn::class, $this->dateColumnConfig],
                'date_type' => [ModeColumn::class, 'title' => 'Date type', 'modes' => self::DATE_TYPES],
                'decision' => [WithdrawalDecisionColumn::class, 'w'],
                'reason' => [WithdrawalReasonColumn::class, 'w'],
                'user_id' => [UserIdColumn::class, 'ut'],
                'decision_made_by' => [WithdrawalOperatorColumn::class, ['e', 'ut'], 'operatorTableAlias' => 'e', 'usersStatsTableAlias' => 'ut'],
                'site_user_id' => [SiteUserColumn::class, 'ut'],
                'rule_id' => [JsonFieldColumn::class, ['w' => 'extra'], 'key' => 'rule_id', 'title' => 'Rule Id'],
                'country' => [CountryColumn::class, 'u'],
            ]
        ];
    }

    public function columns(): array
    {
        return [
            'General' => [
                'site_id' => [SiteIdColumn::class, 'ut'],
                'user_id' => [UserIdColumn::class, 'ut'],
                'site_user_id' => [SiteUserColumn::class, 'ut'],
                'transaction_id' => [UsersTransactionsTransactionIdColumn::class, 'ut'],
                'status' => [UsersStatsStatusColumn::class, 'ut'],
                'created_at' => [DateColumn::class, ['ut' => 'created_at'], 'title' => 'Date created'],
                'updated_at' => [DateColumn::class, ['ut' => 'updated_at'], 'title' => 'Date modified'],
                'sum_orig' => [MoneyColumn::class, ['ut' => 'amount_orig'], 'title' => 'Sum Orig'],
                'currency' => [CurrencyColumn::class, 'ut'],
                'user_status' => [UserStatusColumn::class, 'u'],
                'decision' => [WithdrawalDecisionColumn::class, 'w'],
                'reason' => [WithdrawalReasonColumn::class, 'w'],
                'decision_made_by' => [WithdrawalOperatorColumn::class, ['e', 'ut'], 'operatorTableAlias' => 'e', 'usersStatsTableAlias' => 'ut'],
                'rule_id' => [JsonFieldColumn::class, ['w' => 'extra'], 'key' => 'rule_id', 'title' => 'Rule Id'],
                'processed_at' => [DateColumn::class, ['w' => 'created_at'], 'title' => 'Decision made at'],
                'admin_comment' => [CommentColumn::class, ['w' => 'admin_comment'], 'title' => 'Admin comment'],
                'country' => [CountryColumn::class, 'u'],
            ]
        ];
    }

    public function metrics(): array
    {
        return [
            'General' => [
                'count' => [CountColumn::class, ['expr' => "COUNT(DISTINCT (ut.site_id, ut.transaction_id))"], 'title' => 'Count'],
            ]
        ];
    }

    public function groups(): array
    {
        return [
            'General' => [
                'site_id' => [SiteIdColumn::class, 'ut'],
                'decision_made_by' => [WithdrawalOperatorColumn::class, ['e', 'ut'], 'operatorTableAlias' => 'e', 'usersStatsTableAlias' => 'ut'],
                'decision' => [WithdrawalDecisionColumn::class, 'w'],
                'upd_month' => [MonthColumn::class, ['ut' => 'updated_at'], 'title' => 'Month (modified)'],
                'upd_day' => [DayColumn::class, ['ut' => 'updated_at'], 'title' => 'Day (modified)'],
                'user_status' => [UserStatusColumn::class, 'u'],
                'rule_id' => [JsonFieldColumn::class, ['w' => 'extra'], 'key' => 'rule_id', 'title' => 'Rule Id'],
                'country' => [CountryColumn::class, 'u'],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'ut' => [function (Query $query) {
                $query->andWhere([
                    'AND',
                    ['=', 'ut.op_id', UserTransaction::OP_OUT],
                    ['IN', 'ut.status', [UserTransaction::STATUS_SUCCESS, UserTransaction::STATUS_FAIL]]
                ]);
                return UserTransactions::TABLE_NAME;
            }],
            'w' => [Withdrawals::TABLE_NAME, 'w.site_id = ut.site_id AND w.transaction_id = ut.transaction_id'],
            'e' => [Employees::TABLE_NAME, 'e.employee_id = w.operator_id', ['w']],
            'u' => [Users::TABLE_NAME, 'u.site_id = ut.site_id AND u.user_id = ut.user_id']
        ];
    }
}
