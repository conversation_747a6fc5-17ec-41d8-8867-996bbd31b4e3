<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Verifications;

use app\back\components\helpers\Arr;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\UserDocumentProgress;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\FilterAndSelectDefault;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Selected;

class UserDocumentProgressSourceColumn extends BaseColumn implements Selected, Decorated, Filtered
{
    use FilterAndSelectDefault;

    public string $title = 'Source';
    public string $column = 'source';

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'list' => Arr::assocToIdName(UserDocumentProgress::SOURCES),
        ];
    }

    public function rule(): array
    {
        return [IntArrayValidator::class, UserDocumentProgress::SOURCES];
    }

    public function decorate($value, array $row)
    {
        return UserDocumentProgress::SOURCES[$value] ?? $value;
    }
}
