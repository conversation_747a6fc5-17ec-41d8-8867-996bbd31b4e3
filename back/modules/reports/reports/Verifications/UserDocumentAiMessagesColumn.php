<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Verifications;

use app\back\components\helpers\Db;
use app\back\components\helpers\Json;
use app\back\entities\UserDocument;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserDocumentAiMessagesColumn extends BaseColumn implements Selected, Decorated
{
    public string $title = 'AI messages';
    public bool $isHtmlValue = true;

    public static function innerColumnSelectPart(ConnectionInterface $db): array
    {
        $messagesSubQuery = (new Query($db))
            ->select([
                'docs_ai_messages' => "JSONB_ARRAY_ELEMENTS_TEXT(ud.ai_validation->'messages')"
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->where('ud.id = ANY(doc_ids)');


        $query = (new Query($db))
            ->select(new Expression("STRING_AGG(docs_ai_messages, ', ')"))
            ->from($messagesSubQuery);

        return ['docs_ai_messages' => $query];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        $t = $this->tableAlias;

        $expr = Db::aggStatsFromJsonSelectQuery($db, "$t.docs_ai_messages")->createCommand()->getRawSql();

        return "($expr)";
    }

    public function decorate($value, array $row)
    {
        if (empty($value)) {
            return '';
        }

        $result = [];
        $stats = Json::decode($value);

        foreach ($stats as $messageId => $count) {
            $message = UserDocument::AI_VALIDATIONS[$messageId] ?? $messageId;
            $result[] = "$message: $count";
        }

        $result = implode("\n", $result);
        if ($this->isHtmlVersion) {
            $result = "<pre>$result</pre>";
        }

        return $result;
    }
}
