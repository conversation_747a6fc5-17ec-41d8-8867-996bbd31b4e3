<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Verifications;

use app\back\components\helpers\Db;
use app\back\components\helpers\Json;
use app\back\entities\UserDocument;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserDocumentAiResultColumn extends BaseColumn implements Selected, Decorated
{
    public string $title = 'AI result';
    public bool $isHtmlValue = true;

    public static function innerColumnSelectPart(ConnectionInterface $db): array
    {
        $key = UserDocument::AI_V_KEY_VALID;

        $query = (new Query($db))
            ->select(new Expression("STRING_AGG((ud.ai_validation->'$key')::boolean::int::text, ', ')"))
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->where('ud.id = ANY(doc_ids)');

        return ['docs_ai_valid' => $query];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        $t = $this->tableAlias;

        $expr = Db::aggStatsFromJsonSelectQuery($db, "$t.docs_ai_valid")->createCommand()->getRawSql();

        return "($expr)";
    }

    public function decorate($value, array $row)
    {
        if (empty($value)) {
            return '';
        }

        $result = [];
        $stats = Json::decode($value);

        foreach ($stats as $isValid => $count) {
            $validMessage = $isValid ? 'Valid' : 'Invalid';
            $result[] = "$validMessage: $count";
        }

        $result = implode("\n", $result);
        if ($this->isHtmlVersion) {
            $result = "<pre>$result</pre>";
        }

        return $result;
    }
}
