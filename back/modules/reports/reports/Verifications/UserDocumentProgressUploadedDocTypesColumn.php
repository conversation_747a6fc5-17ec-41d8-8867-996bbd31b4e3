<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Verifications;

use app\back\components\helpers\Db;
use app\back\components\helpers\Json;
use app\back\entities\UserDocumentProgress;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\Decorated;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserDocumentProgressUploadedDocTypesColumn extends BaseColumn implements Selected, Decorated
{
    public string $title = 'Uploaded document types';
    public bool $isHtmlValue = true;

    public static function innerColumnSelectPart(ConnectionInterface $db): array
    {
        $query = (new Query($db))
            ->select(new Expression("STRING_AGG(ud.doc_type::text, ', ')"))
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->where('ud.id = ANY(doc_ids)');

        return ['upload_doc_types' => $query];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        $t = $this->tableAlias;

        $expr = Db::aggStatsFromJsonSelectQuery($db, "$t.upload_doc_types")->createCommand()->getRawSql();

        return "($expr)";
    }

    public function decorate($value, array $row)
    {
        if (empty($value)) {
            return '';
        }

        $result = [];
        $stats = Json::decode($value);

        foreach ($stats as $type => $count) {
            $typeReason = UserDocumentProgress::TYPES[$type];
            $result[] = "$typeReason: $count";
        }

        $result = implode("\n", $result);
        if ($this->isHtmlVersion) {
            $result = "<pre>$result</pre>";
        }

        return $result;
    }
}
