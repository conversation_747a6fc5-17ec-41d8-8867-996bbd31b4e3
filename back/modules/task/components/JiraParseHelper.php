<?php

declare(strict_types=1);

namespace app\back\modules\task\components;

use app\back\components\helpers\DateHelper;
use app\back\entities\UserTicket;

readonly class JiraParseHelper
{
    public const int JIRA_TYPE_LOST_DEPOSIT_ID = 13400;

    public const array JIRA_ANALYTICS_TYPES = [
        JiraParseHelper::JIRA_TYPE_LOST_DEPOSIT_ID => UserTicket::TYPE_LOST_DEPOSIT,
    ];

    public const array JIRA_ANALYTICS_STATUSES = [
        'Need Approve' => UserTicket::STATUS_NEED_APPROVE,
        'Open' => UserTicket::STATUS_OPEN,
        'Declined' => UserTicket::STATUS_DECLINED,
        'Description needed' => UserTicket::STATUS_DESCRIPTION_NEEDED,
        'In Progress' => UserTicket::STATUS_IN_PROGRESS,
        'Waiting for Client' => UserTicket::STATUS_WAITING_CLIENT,
        'Not correct request' => UserTicket::STATUS_INCORRECT_REQUEST,
        'Provider Checking' => UserTicket::STATUS_PROVIDER_CHECKING,
        'Resolved' => UserTicket::STATUS_RESOLVED,
        'Closed' => UserTicket::STATUS_CLOSED,
        'Reopened' => UserTicket::STATUS_REOPENED,
        'Payment received' => UserTicket::STATUS_PAYMENT_RECEIVED,
        'Lost payment' => UserTicket::STATUS_PAYMENT_LOST,
        'Failed payment' => UserTicket::STATUS_PAYMENT_FAILED,
    ];

    private const string JIRA_TIMEZONE = 'Europe/Kyiv'; // jira application setting
    private const string JIRA_DATETIME_FORMAT = 'Y-m-d\TH:i:s.vO'; // 2025-03-12T13:48:00.221+0200

    public const int JIRA_STATUS_DECLINE = 91;
    public const int JIRA_STATUS_OPEN = 101;

    public const array TRANSITIONS_MAP = [
        UserTicket::STATUS_OPEN => JiraParseHelper::JIRA_STATUS_OPEN,
        UserTicket::STATUS_DECLINED => JiraParseHelper::JIRA_STATUS_DECLINE,
    ];

    public static function parseCustomField(string $name, array $fields): mixed
    {
        $value = $fields[$name] ?? null;
        return match (true) {
            empty($value) => null,
            is_float($value) => (string) $value,
            is_numeric($value) => (int) $value,
            is_array($value) => array_is_list($fields[$name]) ? $fields[$name][0]['value'] : $fields[$name]['value'],
            default => $value,
        };
    }

    public static function parseEmail(array $value): string
    {
        return $value['emailAddress'];
    }

    public static function parseDateFromJira(string $dateJira): string
    {
        $date = \DateTimeImmutable::createFromFormat(self::JIRA_DATETIME_FORMAT, $dateJira);
        $date = $date->setTimezone(new \DateTimeZone('UTC'));
        return $date->format(DateHelper::DATETIME_FORMAT_PHP);
    }

    public static function parseDateToJira(string $dateUtc): string
    {
        $jiraDate = new \DateTimeImmutable($dateUtc, new \DateTimeZone('UTC'));
        $jiraDate = $jiraDate->setTimezone(new \DateTimeZone(self::JIRA_TIMEZONE));
        return $jiraDate->format(DateHelper::DATETIME_WITHOUT_SECONDS);
    }

    public static function firstLogStatusRecord(array $row): array
    {
        $record = [
            'created_at' => $row['created_at'],
            'created_by' => $row['creator'],
            'source' => UserTicket::SOURCE_JIRA,
            'status' => $row['status'],
        ];

        foreach ($row['history'] as $items) {
            foreach ($items['items'] as $item) {
                if ($item['field'] === 'status') {
                    $record['status'] = self::JIRA_ANALYTICS_STATUSES[$item['fromString']];
                    break 2;
                }
            }
        }

        return $record;
    }

    public static function parseHistory(array $row): array
    {
        $changelog = [];
        $changelog[] = self::firstLogStatusRecord($row); // first status is absent in jira changelog so must be constructed
        foreach ($row['history'] as $item) {
            foreach ($item['items'] as $record) {
                if ($record['field'] !== 'status' || !array_key_exists($record['toString'], self::JIRA_ANALYTICS_STATUSES)) {
                    continue;
                }

                $changelog[] = [
                    'created_at' => self::parseDateFromJira($item['created']),
                    'created_by' => self::parseEmail($item['author']),
                    'source' => UserTicket::SOURCE_JIRA,
                    'status' => self::JIRA_ANALYTICS_STATUSES[$record['toString']],
                ];
            }
        }

        return $changelog;
    }
}
