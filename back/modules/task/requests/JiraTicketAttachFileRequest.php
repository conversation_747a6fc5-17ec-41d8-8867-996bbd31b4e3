<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\parsers\JsonParser;
use app\back\components\services\FileStorage;
use app\back\modules\task\actions\import\UsersTicketsFilesBaseTask;
use app\back\repositories\UserTicketFiles;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\Multipart\FormDataPart;
use Yiisoft\Db\Expression\Expression;

class JiraTicketAttachFileRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;

    public string $authKey;
    public array $files;
    public ?string $jiraKey = null;

    public function __construct(
        private readonly UserTicketFiles $userTicketFilesRepo,
        private readonly FileStorage $storage,
    ) {
    }

    protected function fetchData(): iterable
    {
        $fileParts = [];
        $mimeTypes = array_flip(UsersTicketsFilesBaseTask::MIME_TYPES_ALLOWED);
        foreach ($this->files as $file) {
            $path = $this->userTicketFilesRepo->getStoragePath($file['id'], $file['extension']);
            $fileParts[] = ['file' => new DataPart($this->storage->getContents($path), $file['original_name'], $mimeTypes[$file['extension']])];
        }

        $formData = new FormDataPart($fileParts);

        $response = $this->createHttpClient()->post(
            $this->buildUrl([':jiraKey' => $this->jiraKey]),
            $formData->bodyToString(),
            [
                'auth_basic' => $this->authKey,
                'headers' => array_merge(
                    ['X-Atlassian-Token' => 'no-check'],
                    $formData->getPreparedHeaders()->toArray()
                ),
            ]
        );

        $toUpdate = [];
        foreach ($this->responseToData($response) as $jiraFile) {
            foreach ($this->files as $file) {
                if ($file['original_name'] === $jiraFile['filename']) {
                    $toUpdate[] = ['id' => $file['id'], 'jira_file_id' => (int) $jiraFile['jira_file_id'], 'upserted_at' => new Expression('NOW()')];
                    break;
                }
            }
        }
        return $toUpdate;
    }

    protected function parserConfig(): string|array
    {
        return JsonParser::class;
    }
}
