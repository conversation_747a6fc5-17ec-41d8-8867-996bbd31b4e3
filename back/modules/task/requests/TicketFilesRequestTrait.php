<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\UserTicketFile;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Contracts\HttpClient\ResponseInterface;

trait TicketFilesRequestTrait
{
    protected function responseToData(ResponseInterface $response, ?string $path = null): iterable
    {
        $result = array_merge($this->remoteFileId(), [
            'fileId' => $this->fileId,
            'fileContent' => null,
        ]);

        if ($response->getStatusCode() === Response::HTTP_NOT_FOUND) {
            $this->log->debug("Not found on remote server file with id {$result['fileId']}");
            $result['sync_status'] = UserTicketFile::SYNC_NOT_FOUND;
            return $result;
        }

        try {
            $this->handleStatusCode($response);
            $result['fileContent'] = $response->getContent(false);
            $result['sync_status'] = UserTicketFile::SYNC_SUCCESS;
        } catch (\Throwable $e) {
            $this->log->error("Failed to download file with id {$result['fileId']}: " . $e->getMessage());
            $result['sync_status'] = UserTicketFile::SYNC_FAILED;
        }

        return $result;
    }
}
