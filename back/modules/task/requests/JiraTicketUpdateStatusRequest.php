<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\modules\task\components\JiraParseHelper;

class JiraTicketUpdateStatusRequest extends JiraPostRequest
{
    public string $jiraKey;
    public int $analyticStatus;

    protected function buildUrl(array $params = []): string
    {
        return parent::buildUrl([':jiraKey' => $this->jiraKey]);
    }

    protected function requestParams(): array
    {
        return ['transition' => ['id' => JiraParseHelper::TRANSITIONS_MAP[$this->analyticStatus]]];
    }
}
