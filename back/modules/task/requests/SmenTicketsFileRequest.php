<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

class SmenTicketsFileRequest extends SmenRequest
{
    use TicketFilesRequestTrait;

    public int $fileId;
    public int $productTicketId;
    public string $productFileKey;

    protected function buildUrl(array $params = []): string
    {
        return parent::buildUrl([':taskId' => $this->productTicketId, ':fileName' => $this->productFileKey]);
    }

    protected function remoteFileId(): array
    {
        return  ['product_file_key' => $this->productFileKey];
    }
}
