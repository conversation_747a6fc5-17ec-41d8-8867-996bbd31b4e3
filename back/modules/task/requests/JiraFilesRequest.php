<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

class JiraFilesRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use TicketFilesRequestTrait;

    public string $authKey;

    public int $jiraFileId;
    public string $jiraFileName;
    public int $fileId;

    protected function fetchData(): iterable
    {
        $url = $this->buildUrl([':jiraFileId' => $this->jiraFileId, ':jiraFileName' => $this->jiraFileName]);

        $response = $this->createHttpClient()->get($url, [
            'auth_basic' => $this->authKey,
        ]);

        return $this->responseToData($response);
    }

    protected function remoteFileId(): array
    {
        return  ['jira_file_id' => $this->jiraFileId];
    }
}
