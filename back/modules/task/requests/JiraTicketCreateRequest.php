<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\entities\User;
use app\back\entities\UserTicket;
use app\back\modules\task\components\JiraParseHelper;
use app\back\repositories\PaySystems;
use app\back\repositories\Sites;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class JiraTicketCreateRequest extends JiraPostRequest
{
    private const string JIRA_PROJECT = 'CCPAYM';

    private const array JIRA_VIP_STATUSES = [
        User::STATUS_ASP => '34385',
        User::STATUS_ULTRA => '25157',
        User::STATUS_VIP => '25155',
    ];

    public UserTicket $ticket;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Sites $sitesRepo,
    ) {
    }

    protected function requestParams(): array
    {
        $transInfo = (new Query($this->db))
            ->select([
                'user_status' => 'u.status',
                'us.amount_orig',
                's2p_id' => 'us.remote_id',
                'pay_sys_name' => 'ps.name',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->leftJoin(
                ['us' => UserTransactions::TABLE_NAME],
                'us.site_id = u.site_id AND us.user_id = u.user_id AND us.transaction_id = :transId',
                ['transId' => $this->ticket->invoice_id],
            )
            ->leftJoin(['ps' => PaySystems::TABLE_NAME], 'ps.id = us.pay_sys_id')
            ->where([
                'u.site_id' => $this->ticket->site_id,
                'u.user_id' => $this->ticket->user_id,
            ])
            ->one();

        $fields = [
            'project' => ['key' => self::JIRA_PROJECT],
            'issuetype' =>  ['id' => JiraParseHelper::JIRA_TYPE_LOST_DEPOSIT_ID],
            'summary' => APP_ENV_DEV ? 'Test ticket - ignore it' : 'Lost deposit',
            'customfield_17200' => ['value' => $this->sitesRepo->getShortNameById($this->ticket->site_id)],
            'customfield_17201' => (string) $this->ticket->user_id,
            'customfield_12902' => (string) $this->ticket->invoice_id,
            'customfield_12903' => $transInfo['s2p_id'] ?? null,
            'customfield_26107' => [['id' => $this->getJiraPaysSysId($transInfo['pay_sys_name'])]],
            'customfield_11704' => $transInfo['amount_orig'] ? (float) $transInfo['amount_orig'] : null,
        ];

        if (array_key_exists($transInfo['user_status'], self::JIRA_VIP_STATUSES)) {
            $fields['customfield_18524'] = ['id' => self::JIRA_VIP_STATUSES[$transInfo['user_status']]];
        }

        return ['fields' => array_filter($fields)];
    }

    private function getJiraPaysSysId(?string $paysys): string
    {
        return match ($paysys) {
            'bank_cards_sber', 'bank_cards_hpp_visa', 'bank_cards_hpp_mc', 'bank_cards_visa', 'bank_cards_mc', 'bank_cards_mir_card', 'bank_cards' => '34136', // Bank Cards
            'bank_cards_sbp_phone', 'bank_cards_sbp', 'bank_cards_sbp_phone_unique', 'sber_p2p_qr', 'pay_p2p_tb', 'pay_p2p_sb', 'sqp_phone_p2p_cis', 'sqp_phone_p2p_uniq_ammount', 'sqp_h2h_p2p' => '34135', // SPB
            'cards_h2h_p2p', 'bank_cards_h2h_p2p', 'pay_p2p_vb', 'pay_p2p_ab', 'cards_p2p', 'p2p_bank_3498', 'card_transfer_p2p_1', 'qiwi_p2p_card', 'yoomoney_p2p_card', 'bank_transfers_ru', 'mr_cards_acq', 'visa_cards_acq', 'mc_cards_acq', 'bank_cards_sbp_p2p', 'bank_cards_sbp_p2p_spay' => '34134', // P2P
            'cry_other', 'usdc_trc20', 'tron_trx', 'dogecoin', 'usdc_erc20', 'bnb_bep20', 'ton_ton', 'litecoin_no_amount', 'bitcoin_no_amount', 'ethereum_no_amount', 'usdt_erc20_no_amount', 'usdt_trc20_no_amount', 'binance_pay', 'litecoin', 'ethereum', 'usdt_trc20', 'usdt_erc20', 'bitcoin' => '34139', // Crypto
            'piastrix' => '34138', // piastrix
            'mobile_commerce_kcell', 'mobile_commerce_activ', 'mobile_commerce_tele2_kz', 'mobile_commerce_beeline_kz', 'mobile_commerce_altel' => '34137', // mobile commerce
            default => '34142', //'Другое'
        };
    }
}
