<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\buf;

use app\back\repositories\BaseRepository;
use app\back\repositories\Fingerprints;

class BufFingerprintTask extends BufBaseTask
{
    public function __construct(private readonly Fingerprints $fingerprintsRepo)
    {
    }

    protected function beforeFind(array &$row): bool
    {
        $result = parent::beforeFind($row);
        unset($row['project']);

        return $result;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpsert($rows, [], '(site_id, user_id, created_at, fingerprint, ip)');
    }

    protected function repository(): BaseRepository
    {
        return $this->fingerprintsRepo;
    }
}
