<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\ads;

use app\back\entities\AdStat;
use app\back\modules\task\requests\AdsStatsUnityRequest;

/** @property AdsStatsUnityRequest $request */
class AdsStatsUnityTask extends AdsStatsBaseTask
{
    protected function adsPlatform(): int
    {
        return AdStat::PLATFORM_UNITY;
    }

    protected function getData(): iterable
    {
        return $this->createRequest([
            'adCredentialsRepo' => $this->adCredentialsRepo,
            'credentials' => $this->credentials(),
            'from' => $this->from,
            'to' => $this->to,
        ])->finalData();
    }

    protected function beforeFind(array &$row): bool
    {
        if (empty($row['creative_id'])) {
            return false;
        }

        $this->setBaseFields($row);

        return true;
    }
}
