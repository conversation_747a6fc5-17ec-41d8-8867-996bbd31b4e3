<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\Arr;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\Users;

class VipaffUsersTask extends ImportTask
{
    use TaskWithFromToRequest;

    private array $updatedUsers = [];

    public function __construct(
        private readonly Users $usersRepo
    ) {
    }

    protected function beforeFind(array &$row): bool
    {
        if (!ctype_digit($row['user_id'])) {
            $this->log->notice("Skip Not integer user: {$row['user_id']}");
            return false;
        }
        return true;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['site_id', 'user_id']);

        return $repository->batchUpsert($rows);
    }

    protected function repository(): BaseRepository
    {
        return $this->usersRepo;
    }
}
