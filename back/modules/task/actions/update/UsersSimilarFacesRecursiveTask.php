<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\Console;
use app\back\components\ConsoleTable;
use app\back\components\helpers\Json;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\UserDocumentFaceSimilarQueues;
use app\back\repositories\UserDocumentFacesSecondary;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersSimilarFacesRecursiveTask extends BaseTask
{
    private const int BATCH_SIZE = 1000;

    private bool $noSleep = false;
    private bool $isDebugCte = false;
    private bool $ignoreOptimisticLock = false;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Users $usersRepo,
    ) {
    }

    public function process(): void
    {
        foreach ($this->mode ? explode(',', $this->mode) : [] as $mode) {
            match ($mode) {
                'noSleep' => $this->noSleep = true,
                'debugCte' => $this->isDebugCte = true,
                // use only in tests
                'ignoreOptimisticLock' => $this->ignoreOptimisticLock = true,
            };
        }

        $this->log->debug('Batch update faces by similarity');
        while (true) {
            $time = microtime(true);
            $res = $this->recursiveUpdateQuery()->one();
            $this->totalRows += $res['from_faces'];
            $this->affectedRows += $res['updated_sims'];

            $stat = $this->logBatchStat($res, microtime(true) - $time);
            $this->logCteTables($res);

            if ($stat['from_faces'] === self::BATCH_SIZE) {
                continue;
            }
            if (max($stat['queued_sims'], $stat['updated_sims']) > 0) {
                if (!$this->noSleep) {
                    sleep(2);
                }
                continue;
            }
            break;
        }

        $this->log->debug('Deleted outdated similar_id from queue: ' . $this->cleanOutdatedSimilarIdFromQueue());
    }

    private function logBatchStat(array &$res, float $time): array
    {
        $res = array_filter($res, static function ($val, $key) use (&$resScalars) {
            if (is_string($val) || $val === null) {
                return true;
            }
            $resScalars[$key] = $val;
            return false;
        }, ARRAY_FILTER_USE_BOTH);

        $scalarStat = implode(', ', array_map(static fn($v, $k) => "$k: $v", $resScalars, array_keys($resScalars)));
        $this->log->info("Batch processed by " . number_format($time, 3) . "s. $scalarStat");

        return $resScalars;
    }

    private function logCteTables(array $res): void
    {
        $res = array_map(Json::decode(...), $res);
        foreach ($res as $k => $v) {
            if (is_array($v) && array_is_list($v)) {
                array_walk($v, static fn(&$row) => array_walk($row, static function (&$ceil) {
                    if (is_array($ceil)) {
                        $ceil = Json::encode($ceil);
                    }
                    if (is_string($ceil) && strlen($ceil) > 100) {
                        $ceil = substr($ceil, 0, 100) . '...';
                    }
                }));
                Console::withWriter(fn(string $msg) => $this->log->debug("CTE $k:\n" . rtrim($msg)), static fn() => ConsoleTable::dump($v));
            } elseif (is_array($v)) {
                foreach ($v as $n => $a) {
                    $this->log->debug("CTE $n: $a");
                }
            } else {
                $this->log->debug("CTE $k: " . ($v ?? '-'));
            }
        }
    }

    // when face change similar_id, we need to check old similar_id "cloud"
    // but when it was the last one, it will stay in queue forever
    private function cleanOutdatedSimilarIdFromQueue(): int
    {
        return $this->db->createCommand()->delete(UserDocumentFaceSimilarQueues::TABLE_NAME, [
            'similar_id' => (new Query($this->db))
                ->distinct()
                ->select('q.similar_id')
                ->from(['q' => UserDocumentFaceSimilarQueues::TABLE_NAME])
                ->leftJoin(['f' => UserDocumentFaces::TABLE_NAME], 'f.similar_id = q.similar_id')
                ->where([
                    'AND',
                    ['f.similar_id' => null],
                    "q.upserted_at < (now() - '1 second'::interval)",
                ])
        ])->execute();
    }

    private function recursiveUpdateQuery(): Query
    {
        $docFacesTable = UserDocumentFaces::TABLE_NAME;
        $docFacesTableSecondary = UserDocumentFacesSecondary::TABLE_NAME;

        $simQueueTable = UserDocumentFaceSimilarQueues::TABLE_NAME;
        if ($this->ignoreOptimisticLock) {
            $optimisticLockCondition = '';
        } else {
            $optimisticLockCondition = "AND q.upserted_at < (now() - '1 second'::interval)";
        }

        $query = (new Query($this->db))
            ->withQuery((new Query($this->db))
                ->select([
                    'face_id' => 'COALESCE(MIN(f.id) FILTER (WHERE COALESCE(f.is_valid, TRUE)), MIN(f.id))',
                    'sim_id_old' => 'q.similar_id',
                ])
                ->from(['f' => $docFacesTable])
                ->innerJoin(['q' => $simQueueTable], ['AND', "f.similar_id = q.similar_id $optimisticLockCondition"])
                ->groupBy('q.similar_id')
                ->orderBy(['q.upserted_at' => SORT_ASC])
                ->limit(self::BATCH_SIZE), 'queued', true)
            ->withQuery((new Query($this->db))->from('queued')->union((new Query($this->db))
                ->select([
                    'face_id' => 'id',
                    'sim_id_old' => '(NULL)',
                ])
                ->from($docFacesTable)
                ->where('similar_id IS NULL AND COALESCE(is_valid, TRUE)')
                ->orderBy(['id' => SORT_ASC])
                ->limit(new Expression('(SELECT ' . self::BATCH_SIZE .  ' - COUNT(*) FROM queued)'))), 'faces_from')
            ->withQuery((new Query($this->db))
                ->select([
                    'sim_id_tmp' => 'ff.face_id',
                    'ff.face_id',
                ])
                ->from(['ff' => 'faces_from'])
                ->union((new Query($this->db))
                    ->distinct()
                    ->select([
                        'sfr.sim_id_tmp',
                        'face_id' => '(CASE WHEN s.face_a_id = sfr.face_id THEN s.face_b_id ELSE s.face_a_id END)',
                    ])
                    ->from(['sfr' => 'sim_faces_rec'])
                    ->innerJoin(['s' => UserDocumentFaceSimilarities::TABLE_NAME], 's.approved AND (s.face_a_id = sfr.face_id OR s.face_b_id = sfr.face_id)')
                    ->innerJoin(['f' => $docFacesTable], 'COALESCE(f.is_valid, TRUE) AND f.id = (CASE WHEN s.face_a_id = sfr.face_id THEN s.face_b_id ELSE s.face_a_id END)')), 'sim_faces_rec')
            ->withQuery((new Query($this->db))
                ->select(['sim_id_tmp', 'sim_id_new' => 'MIN(face_id)'])
                ->from('sim_faces_rec')
                ->groupBy('sim_id_tmp'), 'tmp_sim_to_final')
            ->withQuery((new Query($this->db))
                ->select(['sfr.face_id', 'sim_id_new' => 'MIN(tsf.sim_id_new)', 'sim_id_old' => 'ANY_VALUE(f.similar_id)'])
                ->from(['sfr' => 'sim_faces_rec'])
                ->innerJoin(['tsf' => 'tmp_sim_to_final'], 'tsf.sim_id_tmp = sfr.sim_id_tmp')
                ->innerJoin(['f' => $docFacesTable], 'f.id = sfr.face_id')
                ->groupBy('sfr.face_id'), 'face_sim_final')
            ->withQuery((new Query($this->db))
                ->from('face_sim_final')
                ->where('sim_id_old != sim_id_new OR sim_id_old IS NULL'), 'new_cloud_diff')
            ->withQuery((new Query($this->db))
                ->select([
                    'face_id' => 'f.id',
                    'sim_id_new' => '(NULL::int)',
                    'sim_id_old' => 'f.similar_id',
                ])
                ->from(['f' => (new Query($this->db))
                    ->from($docFacesTable)
                    ->where(new Expression('similar_id IN (SELECT DISTINCT sim_id_old FROM face_sim_final WHERE sim_id_old IS NOT NULL)'))])
                ->leftJoin(['fsf' => 'face_sim_final'], 'fsf.face_id = f.id AND fsf.sim_id_old = f.similar_id')
                ->where(['fsf.face_id' => null]), 'old_cloud_diff')
            ->withQuery("
                UPDATE $docFacesTable f
                   SET similar_id = cld.sim_id_new
                FROM ((SELECT * FROM new_cloud_diff) UNION ALL (SELECT * FROM old_cloud_diff)) cld
                WHERE f.id = cld.face_id
                RETURNING cld.*, f.user_document_id", 'updated_similar')
            ->withQuery("
                UPDATE $docFacesTableSecondary f
                   SET similar_id = cld.sim_id_new,
                       inherited_similarity = true
                FROM ((SELECT * FROM new_cloud_diff) UNION ALL (SELECT * FROM old_cloud_diff)) cld
                WHERE f.id = cld.face_id
                RETURNING cld.*, f.user_document_id", 'updated_similar_secondary')
            ->withQuery((new Query($this->db))
                ->distinct()
                ->select(['d.site_id', 'd.user_id'])
                ->from(['d' => UserDocumentsActive::TABLE_NAME])
                ->innerJoin(['ups' => 'updated_similar'], 'ups.user_document_id = d.id'), 'source_resets_prepared')
            ->withQuery("
                {$this->usersRepo->resetCloudSourcesCommand('source_resets_prepared')->getRawSql()}
                RETURNING site_id, user_id", 'source_resets_affected')
            ->withQuery((new Query($this->db))
                ->select('sim_id_old')
                ->from('queued')
                ->union((new Query($this->db))
                    ->select('sim_id_old')
                    ->from('updated_similar')
                    ->where('sim_id_old IS NOT NULL')), 'sims_to_del_queued')
            ->withQuery((new Query($this->db))
                ->distinct()
                ->select(['sim_id' => 'UNNEST(CASE WHEN sim_id_old IS NULL THEN ARRAY[sim_id_new] ELSE ARRAY[sim_id_new, sim_id_old] END)'])
                ->from('updated_similar')
                ->where(['IS NOT', 'sim_id_new', null]), 'sims_to_queue')
            ->withQuery("
                DELETE FROM $simQueueTable q
                USING sims_to_del_queued AS d
                WHERE q.similar_id = d.sim_id_old AND q.similar_id NOT IN (SELECT sim_id FROM sims_to_queue) $optimisticLockCondition
                RETURNING q.*", 'sims_queued_deleted')
            ->withQuery("
                INSERT INTO $simQueueTable (similar_id, upserted_at)
                SELECT sim_id, NOW()
                FROM sims_to_queue
                ON CONFLICT (similar_id) DO UPDATE
                SET upserted_at = GREATEST(NOW(), excluded.upserted_at)
                RETURNING *", 'sims_queued_upserted')
            ->select([
                'from_faces' => '(SELECT COUNT(*) FROM faces_from)',
                'queue_deletes' => '(SELECT COUNT(*) FROM sims_queued_deleted)',
                'updated_sims' => '(SELECT COUNT(*) FROM updated_similar)',
                'queued_sims' => '(SELECT COUNT(*) FROM sims_queued_upserted)',
                'source_resets' => '(SELECT COUNT(*) FROM source_resets_affected)',
                'recursive_rows' => '(SELECT COUNT(*) FROM sim_faces_rec)',
                'top_cloud_size' => 'COALESCE((SELECT COUNT(*) FROM face_sim_final GROUP BY sim_id_new ORDER BY COUNT(*) DESC LIMIT 1), 0)',
            ]);

        if ($this->isDebugCte) {
            foreach ($query->getWithQueries() as ['alias' => $alias]) {
                $query->addSelect([
                    $alias => (new Query($this->db))
                        ->select('JSON_AGG(ROW_TO_JSON(r))')
                        ->from(['r' => $alias])
                ]);
            }
        }

        return $query;
    }
}
