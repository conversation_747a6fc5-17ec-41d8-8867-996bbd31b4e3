<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\modules\task\BaseTask;
use app\back\repositories\views\UserGameTokenAggs;

class UsersGamesAggTask extends BaseTask
{
    public function __construct(private readonly UserGameTokenAggs $userGamesTokensAggsRepo)
    {
    }

    public function process(): void
    {
        $this->userGamesTokensAggsRepo->refresh();
    }
}
