<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\Console;
use app\back\components\helpers\Db;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\Users;
use Yiisoft\Db\Command\CommandInterface;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Data\DataReader;
use Yiisoft\Db\Query\Query;

class UsersSimilarFacesTask extends BaseTask
{
    private const string TMP_FACE_TABLE = 'cid_tmp_users_documents_faces';
    private const string TMP_FACE_SIMILARITY_TABLE = 'cid_tmp_users_documents_faces_similarity';


    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Users $usersRepo,
    ) {
    }


    public function process(): void
    {
        $this->creatTempTableFaces();
        $this->creatTempTableFacesSimilarly();
        $this->groupingSimilarTmpFaces();
        $facesReader = $this->findUpdatedSimilarGroupFaces();
        $this->totalRows = $facesReader->count();

        if ($this->totalRows === 0) {
            return;
        }

        $this->log->notice(Console::format('Update similar faces...', Console::BOLD));
        foreach ($facesReader as $i => $face) {
            $this->db->transaction(function () use ($face) {
                $this->affectedRows += $this->db->createCommand()
                    ->update(UserDocumentFaces::TABLE_NAME, ['similar_id' => $face['similar_id']], ['id' => $face['id']])
                    ->execute();
                $this->usersRepo->resetCloudSources($face['site_id'], $face['user_id']);
            });
            ++$i % 1000 === 0 && $this->log->notice("Updated {$this->affectedRows}/{$this->totalRows}");
        }
    }

    protected function groupingSimilarTmpFaces(): void
    {
        $this->log->notice(Console::format('Recursive grouping similar faces in tmp table...', Console::BOLD));
        $continue = false;
        $newUpdates = $curGroupId = $globalUpdates = $batchUpdates = $batchQueries = 0;
        $facesCount = (new Query($this->db))->from(self::TMP_FACE_TABLE)->count();
        $groupingQuery = $this->getFacesGroupingCommand($curGroupId, $continue);
        $logAndClearBatch = function () use (&$batchUpdates, &$batchQueries) {
            $this->log->notice("Updated {$batchUpdates} faces by {$batchQueries} queries");
            $batchUpdates = $batchQueries = 0;
        };

        do {
            $curGroupId += (int)!$continue;
            $prevUpdates = $newUpdates;
            ['updated' => $newUpdates, 'continue_group' => $continue] = $groupingQuery->queryOne();
            $batchQueries++;
            $globalUpdates += $newUpdates;
            $batchUpdates += $newUpdates;
            $batchUpdates >= 10000 && $logAndClearBatch();

            if ($globalUpdates > $facesCount) {
                throw new \ErrorException("To match updates by recursive query. prev + {$newUpdates} = {$globalUpdates}");
            }
        } while ($newUpdates > 0 || $prevUpdates > 0);

        $logAndClearBatch();

        $facesStat = (new Query($this->db))
            ->select([
                'site_users' => new Expression('COUNT(DISTINCT array[ud.site_id, ud.user_id])'),
                'face_groups_count' => 'COUNT(DISTINCT face.similar_id)',
            ])
            ->from(['face' => self::TMP_FACE_TABLE])
            ->leftJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'face.id = udf.id')
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'udf.user_document_id = ud.id')
            ->one();

        $this->log->notice("Processed {$globalUpdates}} faces. With {$facesStat['site_users']} site_users and {$facesStat['face_groups_count']} similar faces groups");
    }

    private function getFacesGroupingCommand(int &$curGroupId, bool &$continue): CommandInterface
    {
        return (new Query($this->db))
            ->withQuery($this->fromTmpFacesQuery(), 'faces_init')
            ->withQuery($this->joinSimilarFacesQuery('faces_init', 'rec_faces'), 'rec_faces', true)
            ->withQuery($this->aggregateFaceIdsQuery('rec_faces'), 'to_update')
            ->withQuery('
                    UPDATE ' . self::TMP_FACE_TABLE . ' face
                    SET similar_id = :group_id
                    FROM to_update
                    WHERE (face.similar_id IS NULL OR face.similar_id != :group_id) AND face.id = ANY (to_update.ids::integer[])
                    RETURNING TRUE', 'updates')
            ->select([
                'updated' => (new Query($this->db))->select('COUNT(*)')->from('updates'),
                'continue_group' => new Expression('to_update.max_iteration >= :iteration_limit OR to_update.max_rows_counter >= :aggregation_limit')
            ])
            ->from('to_update')
            ->createCommand()
            ->bindValue(':iteration_limit', 10000)
            ->bindValue(':aggregation_limit', 10000)
            ->bindParam(':continue', $continue)
            ->bindParam(':group_id', $curGroupId);
    }

    private function fromTmpFacesQuery(): Query
    {
        return (new Query($this->db))
            ->select(['face.id'])
            ->from(['face' => (new Query($this->db))
                ->select(['face.id'])
                ->from(['face' => self::TMP_FACE_TABLE])
                ->where('NOT :continue AND similar_id IS NULL')
                ->limit(1)])
            ->union((new Query($this->db))
                ->select(['face.id'])
                ->from(['face' => self::TMP_FACE_TABLE])
                ->where(":continue AND similar_id = :group_id"), true);
    }

    private function joinSimilarFacesQuery(string $fromTable, string $similarFacesTable): Query
    {
        return (new Query($this->db))
            ->select([
                'id' => 'face.id',
                'iteration' => new Expression('0'),
                'rows_counter' => new Expression('0::bigint'),
                'prevs_ids' => 'array_agg(face.id) OVER()',
            ])
            ->from(['face' => $fromTable])
            ->union((new Query($this->db))
                ->select([
                    'id' => 'next_face.id',
                    'iteration' => new Expression('iteration + 1'),
                    'rows_counter' => new Expression('rows_counter + count(*) OVER ()'),
                    'prevs_ids' => new Expression('next_face.id || rec_faces.prevs_ids'),
                ], 'DISTINCT ON (next_face.id)')
                ->from($similarFacesTable)
                ->innerJoin(['udfs' => self::TMP_FACE_SIMILARITY_TABLE], 'udfs.face_a_id = rec_faces.id OR udfs.face_b_id = rec_faces.id')
                ->innerJoin([new Expression('LATERAL(VALUES (CASE WHEN udfs.face_a_id = rec_faces.id THEN udfs.face_b_id else udfs.face_a_id END)) as next_face(id)')], 'TRUE')
                ->where('rec_faces.iteration < :iteration_limit AND rec_faces.rows_counter < :aggregation_limit AND NOT (next_face.id = ANY (rec_faces.prevs_ids))'));
    }

    private function aggregateFaceIdsQuery(string $facesTable): Query
    {
        return (new Query($this->db))
            ->select([
                'ids' => 'array_agg(DISTINCT f.id)',
                'max_iteration' => 'MAX(iteration)',
                'max_rows_counter' => 'MAX(rows_counter)',
            ])
            ->from(['f' => $facesTable]);
    }

    private function findUpdatedSimilarGroupFaces(): DataReader
    {
        $this->log->notice(Console::format('Filter faces requiring updates...', Console::BOLD));
        $reader = (new Query($this->db))
            ->withQuery($this->similarFacesAggregatedQuery(), 'new_faces')
            ->withQuery($this->oldInnerSimilarsGroups(), 'old_similar_groups_info')
            ->withQuery($this->findClosestOldSimGroupQuery('new_faces'), 'old_most_intersected_similar_group')
            ->withQuery($this->pickupSimilarIdQuery(
                'new_faces',
                'old_most_intersected_similar_group',
                'old_similar_groups_info'
            ), 'to_update')
            ->select(['face.id', 'to_update.similar_id', 'ud.site_id', 'ud.user_id'])
            ->from('to_update')
            ->innerJoin([new Expression('UNNEST(to_update.ids) as face(id)')], 'TRUE')
            ->innerJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'face.id = udf.id')
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.id = udf.user_document_id')
            ->where('udf.similar_id IS NULL OR udf.similar_id != to_update.similar_id')
            ->createCommand()
            ->query();

        $this->log->notice("Found {$reader->count()} faces for update");
        return $reader;
    }

    private function similarFacesAggregatedQuery(): Query
    {
        return (new Query($this->db))
            ->select(['similar_id', 'ids' => 'array_agg(id)'])
            ->from(self::TMP_FACE_TABLE)
            ->groupBy('similar_id');
    }

    public function oldInnerSimilarsGroups(): Query
    {
        return (new Query($this->db))
            ->select(['similar_id', 'count' => 'COUNT(*)'])
            ->from(UserDocumentFaces::TABLE_NAME)
            ->where('similar_id IS NOT NULL')
            ->groupBy('similar_id');
    }

    public function findClosestOldSimGroupQuery(string $fromNewGroupTable): Query
    {
        return (new Query($this->db))
            ->select(['similar_id', 'old_similar_id', 'intersected_faces_count'])
            ->from((new Query($this->db))
                ->select(['*', 'intersected_faces_count_sames' => new Expression('COUNT(*) OVER (PARTITION BY similar_id)')])
                ->from((new Query($this->db))
                    ->select('*')
                    ->from((new Query($this->db))
                        ->select(['*', 'intersected_faces_count_max' => new Expression('MAX(intersected_faces_count) OVER(PARTITION BY similar_id)')])
                        ->from((new Query($this->db))
                            ->select(['new_sim.similar_id', 'udf.similar_id old_similar_id', 'intersected_faces_count' => 'COUNT(udf.id)'])
                            ->from(['new_sim' => $fromNewGroupTable])
                            ->leftJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'udf.similar_id IS NOT NULL AND udf.id = ANY (new_sim.ids)')
                            ->groupBy(['new_sim.similar_id', 'udf.similar_id'])))
                    ->where('intersected_faces_count = intersected_faces_count_max')))
            ->where(['intersected_faces_count_sames' => 1]);
    }

    private function pickupSimilarIdQuery(string $fromNewSimTable, string $oldProximateSimTable, string $oldSimilarsTable): Query
    {
        return (new Query($this->db))
            ->select([
                'new_sim.ids',
                'similar_id' => new Expression('
                    CASE WHEN old_similar_id IS NULL OR old_proxi_sim.intersected_faces_count <= COALESCE(old_sims.count, 0) / 2
                    THEN COALESCE((SELECT MAX(similar_id) FROM users_documents_faces), 0) + row_number() OVER(PARTITION BY old_proxi_sim.intersected_faces_count <= COALESCE(old_sims.count, 0) / 2)
                    ELSE old_similar_id END')
            ])
            ->from(['new_sim' => $fromNewSimTable])
            ->leftJoin(['old_proxi_sim' => $oldProximateSimTable], 'old_proxi_sim.similar_id = new_sim.similar_id')
            ->leftJoin(['old_sims' => $oldSimilarsTable], 'old_sims.similar_id = old_proxi_sim.old_similar_id');
    }

    protected function creatTempTableFaces(): void
    {
        $this->log->notice(Console::format('Creating temp table faces...', Console::BOLD));
        Db::createTempTable(
            $this->db,
            self::TMP_FACE_TABLE,
            (new Query($this->db))
                ->select(['udf.id', 'udf.is_valid', 'similar_id' => new Expression('null::int')])
                ->from(['udf' => UserDocumentFaces::TABLE_NAME]),
            ['id'],
            [['similar_id']]
        );
        $tmpFacesCount = (new Query($this->db))->from(self::TMP_FACE_TABLE)->count();
        $tmpFacesCount && $this->log->notice("Imported: {$tmpFacesCount} tmp rows");
    }

    protected function creatTempTableFacesSimilarly(): void
    {
        $this->log->notice(Console::format('Creating temp table faces similarly...', Console::BOLD));
        Db::createTempTable(
            $this->db,
            self::TMP_FACE_SIMILARITY_TABLE,
            (new Query($this->db))
                ->select(['udfs.face_a_id', 'udfs.face_b_id'])
                ->from(['udfs' => UserDocumentFaceSimilarities::TABLE_NAME])
                ->leftJoin(['udfa' => self::TMP_FACE_TABLE], 'udfs.face_a_id = udfa.id')
                ->leftJoin(['udfb' => self::TMP_FACE_TABLE], 'udfs.face_b_id = udfb.id')
                ->where([
                    'AND',
                    ['udfs.approved' => true],
                    ['IS NOT', 'udfa.is_valid', new Expression('FALSE')],
                    ['IS NOT', 'udfb.is_valid', new Expression('FALSE')],
                ]),
            ['face_a_id', 'face_b_id'],
            [['face_a_id'], ['face_b_id']]
        );
        $tmpFaceSimsCount = (new Query($this->db))->from(self::TMP_FACE_SIMILARITY_TABLE)->count();
        $tmpFaceSimsCount && $this->log->notice("Imported: {$tmpFaceSimsCount} tmp rows");
    }
}
