<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Json;
use app\back\components\HttpClient;
use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentTextPrediction;
use app\back\modules\task\BaseTask;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserDocumentTextPredictionModels;
use app\back\repositories\UserDocumentTextPredictions;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersDocumentsTextRecognizeTask extends BaseTask
{
    private readonly HttpClient $client;

    public function __construct(
        private readonly FileStorage $storage,
        private readonly UserDocumentsActive $docsActiveView,
        private readonly UserDocumentTextPredictions $userDocumentTextPredictionsRepo,
        private readonly UserDocumentTextPredictionModels $userDocumentTextPredictionModelsRepo,
    ) {
        $this->client = new HttpClient([
            'base_uri' => $_ENV['PAPER_NET_URL'],
            'auth_basic' => [$_ENV['PAPER_NET_USER'], $_ENV['PAPER_NET_PASS']],
        ]);
    }

    public function process(): void
    {
        $query = $this->documentsQuery();
        $this->totalRows = (int)$query->count();

        foreach ($this->docsActiveView->findByQuery($query) as $recordIndex => $documentRecord) {
            /** @var UserDocument $documentRecord */
            try {
                $requestStartTime = microtime(true);
                $fileUrl = $this->storage->getPublicUrlByKey($documentRecord->storagePath());
                $response = $this->client->get('?' . http_build_query(['url' => $fileUrl]));
                [$predictionBuild, $textBoundBoxes] = $response->toArray();
                $importedText = $this->importRow($documentRecord, $predictionBuild, $textBoundBoxes);

                $progressInfo = str_pad(++$recordIndex . "/{$this->totalRows}/{$this->affectedRows}", 20);
                $recognitionTime = number_format(microtime(true) - $requestStartTime, 3);
                $requestTimeInfo = str_pad($recognitionTime, 7, ' ', STR_PAD_LEFT) . ' sec';
                $importedTextInfo = Json::encode($documentRecord->tags->toArray()) . ' ' . str_replace("\n", ' ', $importedText);
                $this->log->debug("{$progressInfo} {$requestTimeInfo}\n{$importedTextInfo}\n{$fileUrl}\n");
            } catch (\Throwable $e) {
                $this->log->error((string)$e);
            }
        }
    }

    private function documentsQuery(): Query
    {
        return (new Query($this->docsActiveView->db))
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->leftJoin(['udtp' => UserDocumentTextPredictions::TABLE_NAME], 'udtp.user_document_id = ud.id')
            ->andWhere([
                'ud.country' => 'RU',
                'udtp.user_document_id' => null,
            ])
            ->andWhere(new Expression('ud.tags::text[] && ARRAY[ :passport, :id_card, :driving_license ]', [
                'passport' => UserDocument::TAG_PASSPORT,
                'id_card' => UserDocument::TAG_ID_CARD,
                'driving_license' => UserDocument::TAG_DIVING_LICENSE,
            ]));
    }

    private function importRow(UserDocument $documentRecord, string $predictionBuild, array $textBoundBoxes): string
    {
        $textBoundBoxes = array_map(static function ($textBoundBox) {
            $type = (int)array_shift($textBoundBox) ?: null;
            $text = array_shift($textBoundBox);
            [$ax, $ay, $bx, $by, $cx, $cy, $dx, $dy] = array_map(static function ($dot) {
                return (int)$dot;
            }, $textBoundBox);
            return [$type, $text, $ax, $ay, $bx, $by, $cx, $cy, $dx, $dy];
        }, $textBoundBoxes);

        $textPredictionRecord = new UserDocumentTextPrediction();
        $textPredictionRecord->user_document_id = $documentRecord->id;
        $textPredictionRecord->prediction_model_id = $this->userDocumentTextPredictionModelsRepo->getIdByName($predictionBuild);
        $textPredictionRecord->text_full = implode("\n", array_map(static function ($boundBox) {
            return $boundBox[1];
        }, $textBoundBoxes));
        $textPredictionRecord->text_bounding_boxes = $textBoundBoxes;
        $this->userDocumentTextPredictionsRepo->insert($textPredictionRecord);
        $this->affectedRows++;

        return $textPredictionRecord->text_full;
    }
}
