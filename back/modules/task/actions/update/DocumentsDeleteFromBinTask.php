<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskWithModes;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocuments;
use app\back\repositories\Users;

class DocumentsDeleteFromBinTask extends BaseTask
{
    use TaskWithModes;

    public bool $modeLegacyFix = false;

    public function __construct(
        private readonly Users $usersRepo,
        private readonly UserDocuments $docsRepo,
        private readonly UserDocumentFaces $facesRepo,
        private readonly FileStorage $storage,
    ) {
    }

    public function process(): void
    {
        /** @var UserDocument[] $foundDocuments */
        $foundDocuments = Arr::fromIterable($this->docsRepo->findEach([
            'AND',
            ['>=', 'deleted_at', $this->from],
            ['<', 'deleted_at', $this->to],
        ]));
        $this->totalRows = count($foundDocuments);

        if ($this->modeLegacyFix) {
            $this->log->notice('Legacy fix');
            foreach ($foundDocuments as $foundDocument) {
                if (!$foundDocument->face_processed) {
                    continue;
                }

                $foundDocument->face_processed = false;
                $foundDocument->pushChangedFields('face_processed');
                $this->docsRepo->updateOrThrowModified($foundDocument);

                $this->facesRepo->deleteByDocumentId($foundDocument->id);
                $this->usersRepo->resetCloudSources($foundDocument->site_id, $foundDocument->user_id);
                $this->affectedRows++;
            }
            return;
        }

        foreach ($foundDocuments as $foundDocument) {
            if (!$this->storage->delete($foundDocument->storagePath())) {
                $this->log->debug("Cant remove document file: {$foundDocument->storagePath()}");
            } else {
                $this->affectedRows++;
            }
        }
    }
}
