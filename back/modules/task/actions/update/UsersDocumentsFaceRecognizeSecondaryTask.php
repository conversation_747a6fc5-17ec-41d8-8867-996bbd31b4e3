<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Db;
use app\back\components\helpers\Json;
use app\back\entities\UserDocument;
use app\back\modules\task\BaseTask;
use app\back\modules\task\requests\multiCurl\BaseFaceMultiCurlRequest;
use app\back\modules\task\TaskWithModes;
use app\back\modules\task\TaskWithRequest;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFacesSecondary;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersDocumentsFaceRecognizeSecondaryTask extends BaseTask
{
    use TaskWithModes;
    use TaskWithRequest;

    private const int BATCH_SIZE = 1000;
    private const int CONCURRENCY = 5;

    private bool $modeFile = false;
    private bool $modeNoSave = false;
    private int $modeCursor = 0;
    private array $lastProcessedFaceIds = [];

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly UserDocumentsActive $docsActiveView,
        private readonly UserDocumentFacesSecondary $faceSecondaryRepo,
    ) {
    }

    /** @see BaseFaceMultiCurlRequest */
    protected function requestConfig(): array
    {
        return [
            'alwaysSendFiles' => $this->modeFile,
            'requestDataIterator' => $this->docsIterator(),
        ];
    }

    public function process(): void
    {
        foreach ($this->createRequest()->finalData() as $doc => $faces) {
            /** @var UserDocument $doc */
            $this->saveFaces($doc, $faces);
        }
    }

    private function docsIterator(): \Generator
    {
        do {
            $docs = $this->findDocsWithLegacyFacesOnly();
            foreach ($docs as $doc) {
                $this->totalRows++;
                yield $doc;
            }
        } while (!empty($docs));
    }

    private function saveFaces(UserDocument $document, array $faces): void
    {
        array_walk($faces, static function (&$face) use ($document) {
            $face['ratio'] = $face['width'] * $face['height'] / $document->width / $document->height;
        });
        $facesInfo = count($faces);

        $faces = UserDocumentFaces::facesFilterAndSortByRatio($faces);
        if (count($faces) < $facesInfo) {
            $facesInfo = count($faces) . " (sources: $facesInfo)";
        }

        $this->upsertFaces($document, $faces);
        $this->affectedRows++;
        $this->log->debug("$this->affectedRows/$this->totalRows face_id_cursor:$this->modeCursor doc_id:{$document->id} {$document->height}x$document->width faces: $facesInfo");
    }

    private function findDocsWithLegacyFacesOnly(): array
    {
        $time = microtime(true);
        $facesData = (new Query($this->db))
            ->select([
                'doc_ids' => 'jsonb_agg(distinct user_document_id)',
                'face_ids' => 'jsonb_agg(id)'
            ])
            ->from(['f' => UserDocumentFaces::TABLE_NAME])
            ->where(['IN', 'user_document_id', (new Query($this->db))
                ->select('f.user_document_id')
                ->from(['f' => UserDocumentFaces::TABLE_NAME])
                ->leftJoin(['fs' => UserDocumentFacesSecondary::TABLE_NAME], 'fs.id = f.id AND fs.inherited_face IS DISTINCT FROM TRUE')
                ->where([
                    'AND',
                    ['>', 'f.id', $this->modeCursor],
                    ['NOT IN', 'f.id', $this->lastProcessedFaceIds],
                    ['fs.id' => null],
                ])
                ->orderBy(['f.id' => SORT_ASC])
                ->limit(self::BATCH_SIZE)])
            ->one();


        if (isset($facesData['doc_ids'])) {
            $this->lastProcessedFaceIds = Json::decode($facesData['face_ids']);
            $this->modeCursor = min($this->lastProcessedFaceIds);
            $res = iterator_to_array($this->docsActiveView->findEach([
                'id' => Json::decode($facesData['doc_ids'])
            ]));
        } else {
            $res = [];
        }

        $this->log->debug(sprintf("Search documents :%5.3f", microtime(true) - $time));
        return $res;
    }

    private function upsertFaces(UserDocument $document, array $faces): void
    {
        if ($this->modeNoSave) {
            return;
        }

        $upserted = $this->tryUpsertNewWithInheritedSimilarity($document, $faces);
        if ($upserted > 0) {
            return;
        }

        $upserted = $this->upsertFromLegacyFaces($document->id);
        if ($upserted > 0) {
            $this->log->warning("Face area/quantity missmatch docId:$document->id {$document->storagePath()}, imported legacy face(s)");
        } else {
            $this->log->debug("Unable to import lacy faces docId:$document->id {$document->storagePath()}. Looks like document/faces already deleted. Skip");
        }
    }

    private function tryUpsertNewWithInheritedSimilarity(UserDocument $document, array $faces): int
    {
        foreach ($faces as &$face) {
            $face['user_document_id'] = $document->id;
            $face['box'] = '{' . implode(',', $face['box']) . '}';
            $face['eyes'] = isset($face['eyes']) ? '{' . implode(',', $face['eyes']) . '}' : null;
            $face['face_embedding'] = '[' . implode(',', $face['face_embedding']) . ']';
        }
        unset($face);

        $newFacesQuery = $this->selectNewFacesQueryOnlyIfQuantityAndAreaMatchWithLegacy($document, $faces);
        return $this->faceSecondaryRepo->upsert(['id'], $newFacesQuery->getSelect(), fromQuery: $newFacesQuery);
    }

    private function upsertFromLegacyFaces(int $documentId): int
    {
        $legacyFacesQuery = (new Query($this->db))
            ->select([
                'id' => 'fl.id',
                'user_document_id' => 'fl.user_document_id',
                'number' => 'fl.number',
                'ratio' => 'fl.ratio',
                'box' => 'fl.box',
                'eyes' => 'fl.eyes',
                'similar_id' => 'fl.similar_id',
                'angle' => 'fl.angle',
                'image' => "fl.image",
                'recognition_score' => 'fl.recognition_score',
                'is_valid' => 'fl.is_valid',
                'validated_by' => 'fl.validated_by',
                'inherited_face' => '(TRUE)',
                'inherited_similarity' => '(TRUE)',
                'face_embedding' => 'fl.face_embedding',
                'created_at' => new Expression('NOW()'),
            ])
            ->from(['fl' => UserDocumentFaces::TABLE_NAME])
            ->where(['fl.user_document_id' => $documentId]);
        return $this->faceSecondaryRepo->upsert(['id'], $legacyFacesQuery->getSelect(), fromQuery: $legacyFacesQuery);
    }

    private function selectNewFacesQueryOnlyIfQuantityAndAreaMatchWithLegacy(UserDocument $document, array $faces): Query
    {
        $boxLegacy = 'box(point(fl.box[1], fl.box[2]), point(fl.box[3], fl.box[4]))';
        $boxNew = 'box(point(fn.box[1], fn.box[2]), point(fn.box[3], fn.box[4]))';
        // inner_join_area / full_join_bound_box_area > ...
        $areaMatch = "area($boxLegacy # $boxNew) / area(bound_box($boxLegacy, $boxNew)) > 0.5";
        return (new Query($this->db))
            ->withQuery((new Query($this->db))
                ->select(['*', 'faces_count' => 'COUNT(*) OVER ()'])
                ->from(UserDocumentFaces::TABLE_NAME)
                ->where(['user_document_id' => $document->id]), 'fl')
            ->withQuery((new Query($this->db))
                ->select(['*', 'faces_count' => 'COUNT(*) OVER ()'])
                ->from(Db::valuesTable($this->db, $faces, [
                    'user_document_id' => 'int',
                    'number' => 'smallint',
                    'ratio' => 'float',
                    'box' => 'int[]',
                    'eyes' => 'int[]',
                    'recognition_score' => 'float',
                    'face_embedding' => 'vector',
                    'angle' => 'int',
                    'image' => 'text',
                ], 'fn')), 'fn')
            ->withQuery((new Query($this->db))
                ->select(['matched_count' => "COUNT(*) FILTER ( WHERE $areaMatch )"])
                ->from('fl')
                ->innerJoin('fn', [
                    'AND',
                    'fn.user_document_id = fl.user_document_id',
                    'fn.number = fl.number',
                ]), 'matched')
            ->select([
                'id' => 'fl.id',
                'user_document_id' => 'fn.user_document_id',
                'number' => 'fn.number',
                'ratio' => 'fn.ratio',
                'box' => 'fn.box',
                'eyes' => 'fn.eyes',
                'angle' => 'fn.angle',
                'image' => "decode(fn.image, 'base64')",
                'recognition_score' => 'fn.recognition_score',
                'face_embedding' => 'fn.face_embedding',
                'similar_id' => 'fl.similar_id',
                'is_valid' => 'fl.is_valid',
                'validated_by' => 'fl.validated_by',
                'inherited_face' => '(NULL)',
                'inherited_similarity' => '(TRUE)',
                'created_at' => 'fl.created_at',
            ])
            ->from('fl')
            ->innerJoin('matched', 'matched.matched_count = fl.faces_count')
            ->innerJoin('fn', [
                'AND',
                'fn.number = fl.number',
                'fn.faces_count = fl.faces_count',
            ]);
    }
}
