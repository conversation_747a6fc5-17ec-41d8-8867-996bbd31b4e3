<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\BaseAuthAccess;
use app\back\components\ConcurrentlyIterator;
use app\back\components\DocumentImageModify;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\helpers\Url;
use app\back\components\HttpClient;
use app\back\components\Initializable;
use app\back\components\PgArray;
use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentLog;
use app\back\entities\UserWallet;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocumentLogs;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\Users;
use app\back\repositories\UserWallets;
use Symfony\Component\HttpClient\Exception\ServerException;
use Symfony\Component\Mime\Part\DataPart;
use Symfony\Component\Mime\Part\Multipart\FormDataPart;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class DocumentsTagsRecognizeTask extends BaseTask
{
    private const int CONCURRENTLY_TASK_LIMIT = 4;
    private const int CONCURRENTLY_BATCH_SIZE = 10;
    private const int RECOGNITION_PING_INTERVAL = 1;
    private const int RECOGNITION_WAIT_TIMEOUT = 600;
    private const string ENDPOINT_URLS = 'v2/urls';
    private const string ENDPOINT_FILE = 'v2/file';
    public const array DOCUMENT_TYPES = [
        'bank_card' => UserDocument::TAG_BANK_CARD,
        'id_card' => UserDocument::TAG_ID_CARD,
        'passport' => UserDocument::TAG_PASSPORT,
        'driver_license' => UserDocument::TAG_DIVING_LICENSE,
        'other' => 'other',
    ];

    private HttpClient $client;
    private bool $modeFile;
    private bool $modeNoSave;
    private bool $modeLogRequest;
    private bool $modeLogResponse;
    private int $concurrency = 0;

    public function __construct(
        private readonly UserDocumentsActive $docsActiveRepo,
        private readonly BaseAuthAccess $auth,
        private readonly FileStorage $storage,
        private readonly ConnectionInterface $db,
        private readonly DocumentImageModify $documentImageModify,
        private readonly UserWallets $userWalletsRepo,
    ) {
        $this->client = new HttpClient([
            'base_uri' => $_ENV['DOCUMENTS_CLASSIFICATION_URL'],
            'headers' => [
                'Auth-Token' => $_ENV['DOCUMENTS_CLASSIFICATION_TOKEN'],
            ],
        ]);
    }

    #[Initializable]
    final public function parseTaskMode(): void
    {
        $modeParts = $this->mode ? array_flip(explode(',', $this->mode)) : [];
        $extractMode = static function (string $mode) use (&$modeParts) {
            $exists = array_key_exists($mode, $modeParts);
            unset($modeParts[$mode]);
            return $exists;
        };

        $this->modeFile = $extractMode('file');
        $this->modeNoSave = $extractMode('nosave');
        $this->modeLogRequest = $extractMode('logrequest');
        $this->modeLogResponse = $extractMode('logresponse');
        $modeParts = implode('-', $modeParts);

        if (!empty($modeParts)) {
            throw new \InvalidArgumentException("Invalid '$modeParts' in --mode=$this->mode");
        }
    }

    public function process(): void
    {
        foreach ($this->modeFile ? $this->recognizeFiles() : $this->recognizeUrls() as [$doc, $recognition]) {
            /** @var UserDocument $doc */
            $angle = $this->normalizeAngle($recognition['rotate clockwise']['angle']);
            $this->applySelfieType($doc, $recognition['type'], $angle, $recognition['face_iqa'] ?? null);
            $this->applyDocuments($doc, $recognition['documents']);
            $this->applyCountries($doc, array_unique(array_filter(
                array_map(static fn(array $e) => $e['country'], $recognition['documents'])
            )));
            $this->logRecognition($doc, $recognition);

            if (!$this->modeNoSave) {
                $this->updateDocument($doc, $angle, $recognition);
            }
        }
    }

    private function updateDocument(UserDocument $doc, int $angle, array $recognition): void
    {
        $isChanged = $doc->hasChangedFields() || $angle > 0;
        try {
            if ($angle > 0) {
                $updated = $this->documentImageModify->rotate($doc, $angle);
                if (!$updated) {
                    $this->log->error("{$doc->storagePath()} unable to rotate on angle $angle");
                    return;
                }
            }
        } finally {
            // try-finally to save rotation log before tags recognition
            $this->insertDocumentLog($doc, $recognition);
        }

        if ($doc->hasChangedFields()) {
            // if not updated by rotation
            $this->docsActiveRepo->updateOrThrowModified($doc);
        }
        $this->affectedRows += $isChanged;
    }

    private function fromToDocuments($batchGenerator): \Generator
    {
        $docsIterator = $this->docsActiveRepo->findEach([
            'AND',
            ['external_approve_required' => true],
            ['>=', 'created_at', $this->from],
            ['<', 'created_at', $this->to],
            ['<', 'updated_at', new Expression('NOW()')], // for optimistic lock
        ]);

        foreach (Arr::batchIterable($docsIterator, 100) as $docs) {
            $this->totalRows += count($docs);
            $info = $this->docsInfo(...$docs);
            yield from $batchGenerator($docs, $info);
        }
    }

    private function applyDocuments(UserDocument $doc, array $documents): void
    {
        $tagsToAdd = [];
        foreach (array_map(static fn($e) => $e['doc_type'], $documents) as $docType) {
            if (!array_key_exists($docType, self::DOCUMENT_TYPES)) {
                $this->log->warning("Invalid document type '$docType'");
                continue;
            }
            $tagsToAdd[] = self::DOCUMENT_TYPES[$docType];
        }

        $tagsToAdd = array_unique($tagsToAdd);
        $currentTags = $doc->tags->toArray();

        if (in_array(UserDocument::TAG_SELFIE, $currentTags, true)) {
            $tagsToAdd[] = UserDocument::TAG_SELFIE;
        }

        if (empty($tagsToAdd)) {
            $tagsToAdd[] = UserDocument::TAG_NOT_RECOGNIZED;
            $this->log->warning("Tags not recognized {$this->docValidationUrl($doc)}");
        }

        $allDocsScore = array_column($documents, 'iqa_doc_score');

        if (!empty($allDocsScore)) {
            $docScore = min($allDocsScore);
        } else {
            $docScore = null;
        }

        if ($doc->doc_quality !== $docScore) {
            $doc->doc_quality = $docScore;
            $this->pushDocumentChanges($doc, 'doc_quality');
        }

        if ((count($currentTags) === count($tagsToAdd) && empty(array_diff($tagsToAdd, $currentTags)))) {
            return;
        }

        sort($tagsToAdd);
        $doc->tags = new PgArray($tagsToAdd);
        $this->pushDocumentChanges($doc, 'tags');
    }

    private function applyCountries(UserDocument $doc, array $countries): void
    {
        if (empty($countries)) {
            return;
        }
        if (count($countries) > 1) {
            $this->log->debug('Multiple countries, first used: ' . implode(', ', $countries));
        }
        $country = current($countries);
        if (strlen($country) !== 2) {
            $this->log->warning("Invalid country code '$country'");
            return;
        }

        $country = strtoupper($country);
        if ($doc->country === $country) {
            return;
        }

        $doc->country = $country;
        $this->pushDocumentChanges($doc, 'country');
    }

    private function applySelfieType(UserDocument $doc, string $selfieType, int $angle, ?float $selfieQuality): void
    {
        $tags = $doc->tags->toArray();
        $hasSelfieTags = in_array(UserDocument::TAG_SELFIE, $tags, true);

        if ($selfieType === 'not selfie') {
            if ($hasSelfieTags) {
                $tags = array_diff($tags, [UserDocument::TAG_SELFIE]);
                $doc->tags = new PgArray($tags);
                $this->pushDocumentChanges($doc, 'tags');
            }
            return;
        }

        if ($selfieType !== 'selfie') {
            $this->log->error("Unknown selfie type {$doc->storagePath()} '$selfieType'");
            return;
        }

        if ($selfieQuality === null) {
            $this->log->error("Undefined face quality {$doc->storagePath()}");
            return;
        }

        $selfieQuality /= 100;

        if ($doc->selfie_quality !== $selfieQuality) {
            $doc->selfie_quality = $selfieQuality;
            $this->pushDocumentChanges($doc, 'selfie_quality');
        }

        if ($doc->face_processed && $hasSelfieTags && $angle === 0) {
            return;
        }

        // if angle angle > 0 expect rotation and face delete
        if ((!$doc->face_processed || $angle > 0) && !$doc->force_face_recognize) {
            $doc->force_face_recognize = true;
            $this->pushDocumentChanges($doc, 'force_face_recognize');
        }

        if (!$hasSelfieTags) {
            $tags[] = UserDocument::TAG_SELFIE;
            $doc->tags = new PgArray($tags);
            $this->pushDocumentChanges($doc, 'tags');
        }
    }

    private function pushDocumentChanges(UserDocument $doc, string ...$changes): void
    {
        static $employeeId = $this->auth->employeeId();
        $doc->updated_by = $employeeId;
        $doc->pushChangedFields('updated_by', ...$changes);
    }

    private function insertDocumentLog(UserDocument $doc, array $recognition): void
    {
        $this->db->createCommand()->insert(UserDocumentLogs::TABLE_NAME, [
            'document_id' => $doc->id,
            'created_by' => $this->auth->employeeId(),
            'action' => UserDocumentLog::ACTION_AUTO_TAGS,
            'details' => $recognition,
        ])->execute();
    }

    private function recognizeUrls(): iterable
    {
        return $this
            ->concurrentlyIterator(function (array $batch, array $docsInfo) {
                $urlDocsMap = array_combine($this->docStorageUrls(...$batch), $batch);
                $idUrlsMap = array_combine(array_map(static fn(UserDocument $d) => $d->id, $urlDocsMap), array_keys($urlDocsMap));
                foreach (Arr::batchIterable($batch, self::CONCURRENTLY_BATCH_SIZE) as $docs) {
                    $request = $this->docsToUrlRequest($docs, $docsInfo, $idUrlsMap);
                    $taskId = $this->taskSend(self::ENDPOINT_URLS, $request);
                    $this->concurrency++;
                    yield [$taskId, $urlDocsMap, $request];
                }
            })
            ->beforeSuccess(function (string $taskId, array $urlDocsMap) {
                $response = $this->taskResultIfReady(self::ENDPOINT_URLS, $taskId);
                return $response ? [$taskId, $response, $urlDocsMap] : false;
            })
            ->onTimeout(function (string $taskId, array $urlDocsMap, array $request) {
                $this->log->error("Task $taskId waiting timeout reached: " . Json::encode($request));
            })
            ->successIterator(function (string $taskId, array $response, array $urlDocsMap) {
                $this->concurrency--;
                foreach ($response['urls'] as $item) {
                    /* @var UserDocument $doc */
                    $doc = $urlDocsMap[$item['url']];
                    $err = $item['result']['err'] ?? '';
                    if ($item['message'] !== 'OK' || !empty($err)) {
                        $this->log->error("Unable to recognize {$this->docValidationUrl($doc)} $taskId\n" . Json::encode($item, JSON_PRETTY_PRINT));
                        continue;
                    }
                    yield [$doc, $item['result']];
                }
            });
    }

    private function recognizeFiles(): iterable
    {

        return $this
            ->concurrentlyIterator(function (array $docs, array $userInfo) {
                foreach ($docs as $doc) {
                    $request = $this->docToFileRequestData($doc, $userInfo[$doc->id]);
                    $taskId = $this->taskSend(self::ENDPOINT_FILE, $request);
                    $this->concurrency++;
                    yield [$taskId, $doc, $request];
                }
            })
            ->beforeSuccess(function (string $taskId, UserDocument $doc) {
                $response = $this->taskResultIfReady(self::ENDPOINT_FILE, $taskId);
                return $response ? [$taskId, $response, $doc] : false;
            })
            ->onTimeout(function (string $taskId, UserDocument $doc, array $request) {
                $this->log->error("Task $taskId waiting timeout reached: " . Json::encode($request));
            })
            ->successIterator(function (string $taskId, array $response, UserDocument $doc) {
                $this->concurrency--;
                if (!empty($response['result']['err'])) {
                    $this->log->error("Unable to recognize {$this->docValidationUrl($doc)} $taskId\n" . Json::encode($response, JSON_PRETTY_PRINT));
                } else {
                    yield [$doc, $response['result']];
                }
            });
    }

    public function concurrentlyIterator(\Closure $requestGenerator): ConcurrentlyIterator
    {
        return (new ConcurrentlyIterator(self::CONCURRENTLY_TASK_LIMIT, self::RECOGNITION_WAIT_TIMEOUT, self::RECOGNITION_PING_INTERVAL))
            ->setGenerator($this->fromToDocuments($requestGenerator));
    }

    private function taskSend(string $endpoint, array $request): string
    {
        if ($endpoint === self::ENDPOINT_FILE) {
            $formData = new FormDataPart($request);
            $response = $this->client->post($endpoint, $formData->bodyToString(), [
                'headers' => $formData->getPreparedHeaders()->toArray(),
            ])->toArray();
        } else {
            $response = $this->client->post($endpoint, Json::encode($request), [
                'headers' => ['Content-Type' => 'application/json'],
            ])->toArray();
        }

        if ($this->modeLogResponse) {
            $this->log->debug('Response: ' . Json::encode($response));
        }

        ['status' => $status, 'task_id' => $taskId] = $response;

        if ($status !== 'ok') {
            throw new \RuntimeException("Unable to enqueue task. Status: $status\nResponse: " . Json::encode($response));
        }

        return $taskId;
    }

    private function docsToUrlRequest(array $docs, array $docsInfo, array $idUrlsMap): array
    {
        $requestData = [];
        foreach ($docs as $doc) {
            $info = $docsInfo[$doc->id];
            $requestData['urls'][] = $idUrlsMap[$doc->id];
            $requestData['user_country'][] = $info['country'];
            $requestData['user_currency'][] = $info['currency'];
            $requestData['user_locale'][] = $info['locale'];
        }

        if ($this->modeLogRequest) {
            $this->log->debug('Request: ' . Json::encode($requestData, JSON_PRETTY_PRINT));
        }

        return $requestData;
    }

    private function docToFileRequestData(UserDocument $doc, array $docsInfo): array
    {
        $requestData = [
            'file' => new DataPart($this->storage->getContents($doc->storagePath()), $doc->filename, 'image/jpeg'),
            'user_country' => $docsInfo['country'] ?: '',
            'user_currency' => $docsInfo['currency'] ?: '',
            'user_locale' => $docsInfo['locale'] ?: '',
        ];

        if ($this->modeLogRequest) {
            $this->log->debug('REQUEST: ' . Json::encode(array_merge($requestData, [
                'file' => $this->docStorageUrls($doc)[0],
            ]), JSON_PRETTY_PRINT));
        }

        return $requestData;
    }

    private function docStorageUrls(UserDocument ...$docs): array
    {
        return array_map(fn(UserDocument $doc) => $this->storage->getPublicUrlByKey($doc->storagePath(), '+1 day'), $docs);
    }

    private function docValidationUrl(UserDocument $doc): string
    {
        return Url::to('/finance/documents-tags-validation', ['documentId' => $doc->id,], true);
    }

    private function docsInfo(UserDocument ...$docs): array
    {
        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);

        return (new Query($this->db))
            ->select([
                'id' => 'ud.id',
                'site_id' => 'ud.site_id',
                'user_id' => 'ud.user_id',
                'locale' => 'u.locale',
                'country' => 'u.country',
                'currency' => 'uw.currency',
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ud.site_id AND u.user_id = ud.user_id')
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true')
            ->where(['ud.id' => array_map(static fn(UserDocument $doc) => $doc->id, $docs)])
            ->indexBy('id')
            ->all();
    }

    private function taskResultIfReady(string $endpoint, string $taskId): ?array
    {
        try {
            $response = $this->client
                ->get("$endpoint/$taskId")
                ->toArray();
        } catch (ServerException $e) {
            $this->log->warning(date('Y-m-d H:i:s') . " ServerException $endpoint/$taskId\n$e");
            return null;
        }

        if ($this->modeLogResponse) {
            $this->log->debug('Response: ' . Json::encode($response, JSON_PRETTY_PRINT));
        }

        return match ($response['status']) {
            'completed' => $response,
            'scheduled' => null,
            default => throw new \RuntimeException("Task fail: " . Json::encode($response, JSON_PRETTY_PRINT)),
        };
    }

    private function logRecognition(UserDocument $doc, array $recognition): void
    {
        $angle = $recognition['rotate clockwise']['angle'] === 'None' ? '*' : (int)$recognition['rotate clockwise']['angle'];
        $tags = empty($recognition['tags']) ? '' : implode(', ', $recognition['tags']);
        $changes = $doc->getChangedFields();
        if ($angle > 0) {
            $changes[] = 'angle';
        }
        $this->log->debug(sprintf("x%s\t%3s° %-32s %s changes: %s", $this->concurrency, $angle, $tags, $this->docValidationUrl($doc), implode(', ', $changes ?: ['-'])));
    }

    private function normalizeAngle(int | string $angle): int
    {
        return match ($angle) {
            'None' => 0,
            0, 90, 180, 270 => $angle
        };
    }
}
