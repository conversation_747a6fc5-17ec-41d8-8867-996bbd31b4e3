<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\NotFoundException;
use app\back\components\helpers\DateHelper;
use app\back\components\PgArray;
use app\back\components\PgBytea;
use app\back\components\PgVector;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentFace;
use app\back\modules\task\BaseTask;
use app\back\modules\task\TaskWithModes;
use app\back\modules\task\TaskWithRequest;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\UserDocumentFacesSecondary;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersDocumentsFaceRecognizeTask extends BaseTask
{
    use TaskWithModes;
    use TaskWithRequest;

    // todo pickup new value for deepface
    public const float SIMILARLY_DISTANCE_THRESHOLD = 1.1;

    public bool $modeFile = false;
    public bool $modeNoSave = false;
    public bool $modeExternal = false;

    public function __construct(
        protected readonly BaseAuthAccess $auth,
        protected readonly ConnectionInterface $db,
        protected readonly UserDocumentsActive $docsActiveRepo,
        protected readonly UserDocumentFaces $facesRepo,
        protected readonly UserDocumentFaceSimilarities $similaritiesRepo,
    ) {
        $similaritiesRepo->setIvfflatIndexProbes((int)($_ENV['FACE_EMBEDDING_INDEX_PROBES'] ?? null));
    }

    /** @see BaseFaceMultiCurlRequest */
    protected function requestConfig(): array
    {
        return [
            'alwaysSendFiles' => $this->modeFile,
            'requestDataIterator' => $this->docsIterator(),
        ];
    }

    public function process(): void
    {
        foreach ($this->createRequest()->finalData() as $doc => $faces) {
            /** @var UserDocument $doc */
            $this->processResponse($doc, $faces);
        }
    }

    protected function docsIterator(): \Generator
    {
        $findQuery = (new Query($this->db))
            ->from(['d' => UserDocumentsActive::TABLE_NAME])
            ->where([
                'AND',
                ['face_processed' => false],
                ['<', 'updated_at', new Expression('NOW()')], // for optimistic lock
                [
                    'OR',
                    ['coalesce(external_approve_required, false)' => $this->modeExternal],
                    ['coalesce(force_face_recognize, false)' => true],
                ],
            ])
            ->andWhere('id > :docIdCursor')
            ->orderBy(['id' => SORT_ASC])
            ->limit(1000);

        $docIdCursor = 0;
        do {
            $documents = iterator_to_array($this->docsActiveRepo->findByQuery($findQuery->params([
                ':docIdCursor' => $docIdCursor,
            ])));
            foreach ($documents as $doc) {
                $this->totalRows++;
                // cursor required to avoid concurrently select same docs
                $docIdCursor = $doc->id;
                yield $doc;
            }
        } while (!empty($documents));
    }

    protected function processResponse(UserDocument $document, array $faces): void
    {
        $facesInfo = count($faces);

        $findTime = microtime(true);
        $faces = $this->filterAndFindSimilarlyInDb($document, $faces);
        $findTime = microtime(true) - $findTime;

        if (count($faces) < $facesInfo) {
            $facesInfo = count($faces) . " (sources: $facesInfo)";
        }

        $fileInfo = "{$document->storagePath()} {$document->height}x$document->width faces: $facesInfo";
        $updateTime = microtime(true);
        try {
            $similarInfo = $this->saveFaces($document, $faces);
            $this->affectedRows++;
        } catch (NotFoundException) {
            $this->log->notice("SKIP not found or modified document $fileInfo");
            return;
        }
        $updateTime = microtime(true) - $updateTime;

        $timeInfo = sprintf("search: %'05.2f save: %'05.2f sec", $findTime, $updateTime);
        $this->log->debug(str_pad("$this->affectedRows/$this->totalRows doc_id: $document->id $fileInfo similar: $similarInfo ", 100) . $timeInfo);
    }

    protected function beforeInsertFaces(UserDocument $document): void
    {
    }

    private function saveFaces(UserDocument $document, array $faces): string
    {
        if ($this->modeNoSave) {
            return (string)count(array_merge(...array_column($faces, 1)));
        }

        return $this->db->transaction(function () use ($document, $faces) {
            // to avoid orphan faces & similarities
            $this->lockDocumentRow($document);
            $autoMatched = 0;
            $similarFacesBatch = [];
            $docIsBankCard = $document->tags->toArray() === [UserDocument::TAG_BANK_CARD];
            $this->beforeInsertFaces($document);
            foreach ($faces as [$faceEntity, $similarities]) {
                /** @var UserDocumentFace $faceEntity */
                $this->insertFace($faceEntity);

                foreach ($similarities as $similar) {
                    $simRow = $this->similarInfoToSimilarityRow($faceEntity, $docIsBankCard, $similar);
                    $autoMatched += (int)$simRow['approved'];
                    $similarFacesBatch[] = $simRow;
                }
            }

            $this->updateDocumentFaceProcessed($document);

            if (empty($similarFacesBatch)) {
                return '0';
            }

            return $this->db->createCommand()
                ->batchInsert(UserDocumentFaceSimilarities::TABLE_NAME, array_keys($similarFacesBatch[0]), $similarFacesBatch)
                ->execute() . ($autoMatched ? " (auto: $autoMatched)" : '');
        });
    }

    private function lockDocumentRow(UserDocument $document): void
    {
        $selectDocIdQuery = (new Query($this->db))
            ->select('id')
            ->from(UserDocumentsActive::TABLE_NAME)
            ->where(['id' => $document->id]);

        $foundId = $this->db
            ->createCommand("{$selectDocIdQuery->createCommand()->getRawSql()} FOR UPDATE")
            ->queryScalar();

        if (!$foundId) {
            throw new NotFoundException('Document deleted or modified while recognition done');
        }
    }

    private function insertFace(UserDocumentFace $faceEntity): void
    {
        $this->facesRepo->insert($faceEntity);

        $this->db->createCommand()->insert(UserDocumentFacesSecondary::TABLE_NAME, (new Query($this->db))
            ->select([
                'id',
                'user_document_id',
                'number',
                'ratio',
                'box',
                'created_at',
                'similar_id',
                'recognition_score',
                'is_valid',
                'validated_by',
                'face_embedding',
                'eyes',
                'inherited_face' => '(TRUE)',
                'inherited_similarity' => '(TRUE)',
            ])
            ->from(UserDocumentFaces::TABLE_NAME)
            ->where(['id' => $faceEntity->id]))->execute();
    }

    private function updateDocumentFaceProcessed(UserDocument $document): bool
    {
        return $this->db->createCommand()->update(UserDocumentsActive::TABLE_NAME, [
            'face_processed' => true,
            'force_face_recognize' => null,
            'updated_by' => $this->auth->employeeId(),
            'updated_at' => new Expression('NOW()'),
        ], [
            'id' => $document->id,
            'updated_at' => $document->updated_at->format(DateHelper::DATETIME_FORMAT_PHP), // optimistic lock
        ])->execute() or throw new NotFoundException('Document deleted or modified while recognition done');
    }

    private function similarInfoToSimilarityRow(UserDocumentFace $faceEntity, bool $faceDocIsBankCard, array $similarFaceInfo): array
    {
        $approved = UserDocumentFaceSimilarities::isAutoApprove(
            (float)$similarFaceInfo['distance'],
            (float)min($faceEntity->ratio, $similarFaceInfo['ratio']),
            $faceDocIsBankCard || $similarFaceInfo['is_bank_card']
        );
        return [
            'face_a_id' => $faceEntity->id,
            'face_b_id' => $similarFaceInfo['id'],
            'distance' => $similarFaceInfo['distance'],
            'approved' => $approved ?: null,
            'updated_by' => $this->auth->employeeId(),
            'updated_at' => new Expression('NOW()'),
        ];
    }

    private function filterAndFindSimilarlyInDb(UserDocument $document, array $faces): array
    {
        array_walk($faces, static function (&$face) use ($document) {
            $face['ratio'] = $face['width'] * $face['height'] / $document->width / $document->height;
        });
        return array_map(function ($face) use ($document) {
            $faceEntity = $this->arrayToFaceEntity($document->id, $face);
            return [
                $faceEntity,
                $this->similaritiesRepo->findSimilar($faceEntity),
            ];
        }, UserDocumentFaces::facesFilterAndSortByRatio($faces));
    }

    private function arrayToFaceEntity(int $documentId, array $face): UserDocumentFace
    {
        $faceEntity = new UserDocumentFace();
        $faceEntity->user_document_id = $documentId;
        $faceEntity->number = $face['number'];
        $faceEntity->box = new PgArray($face['box']);
        $faceEntity->face_embedding = new PgVector($face['face_embedding']);
        $faceEntity->ratio = $face['ratio'];
        $faceEntity->recognition_score = $face['recognition_score'];
        // todo make required after deepface release
        $faceEntity->eyes = isset($face['eyes']) ? new PgArray($face['eyes']) : null;
        $faceEntity->image = isset($face['image']) ? new PgBytea($face['image']) : null;
        $faceEntity->angle = $face['angle'] ?? null;
        return $faceEntity;
    }
}
