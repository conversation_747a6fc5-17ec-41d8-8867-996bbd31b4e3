<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\Console;
use app\back\components\helpers\Str;
use app\back\entities\Requisite;
use app\back\entities\UserCloudSource;
use app\back\entities\UserContact;
use app\back\modules\task\BaseTask;
use app\back\repositories\Requisites;
use app\back\repositories\UserCloudSources;
use app\back\repositories\UserContacts;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserRequisites;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use Yiisoft\Db\Command\CommandInterface;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersCloudsSourcesTask extends BaseTask
{
    private const int USERS_LIMIT_PER_STEP = 100000;
    private const string TMP_USERS_CLOUD_TABLE = 'reset_users';

    private array $updateUserTimes = [];
    private array $updateSourceTimes = [];
    private CommandInterface $findSourceCommand;
    private CommandInterface $deleteSourcesCommand;
    private CommandInterface $insertSourcesCommand;
    private CommandInterface $updateUserCommand;

    public function __construct(
        private readonly ConnectionInterface $db,
    ) {
    }

    private function prepare(): void
    {
        $this->findSourceCommand = (new Query($this->db))
            ->withQuery((new Query($this->db))
                ->select(['site_id', 'user_id'])
                ->from(Users::TABLE_NAME)
                ->where(['<', 'source_reset_at', $this->to])
                ->limit(self::USERS_LIMIT_PER_STEP), self::TMP_USERS_CLOUD_TABLE)
            ->withQuery((new Query($this->db))
                ->select('s.*', 'DISTINCT')
                ->from(['s' => $this->getTempTableQueryRequisites()
                    ->union($this->getTempTableQueryUsersEmails(), true)
                    ->union($this->getTempTableQueryUsersSocials(), true)
                    ->union($this->getTempTableQueryWmids(), true)
                    ->union($this->getTempTableQueryFaceGroups(), true)
                    ->union($this->getTempTableQueryUsersTaxNumbers(), true)
                ])
                ->where(['IS NOT', 's.val', null]), 'sources')
            ->withQuery((new Query($this->db))
                ->select([
                    'site_id',
                    'user_id',
                    'sources' => 'JSONB_AGG(JSONB_BUILD_ARRAY(type, val, key_type))'
                ])
                ->from('sources')
                ->groupBy(['site_id', 'user_id']), 'results')
            ->select([
                'uc.site_id',
                'uc.user_id',
                'sources' => "COALESCE(r.sources, '[]'::jsonb)"
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE]) // for update user clouds without sources
            ->leftJoin(['r' => 'results'], 'r.site_id = uc.site_id AND r.user_id = uc.user_id')
            ->createCommand();

        $this->deleteSourcesCommand = $this->db->createCommand()->delete(UserCloudSources::TABLE_NAME, [
            'site_id' => new Expression(':site_id'),
            'user_id' => new Expression(':user_id'),
        ]);

        $this->insertSourcesCommand = $this->db->createCommand()->insert(UserCloudSources::TABLE_NAME, (new Query($this->db))
            ->select([
                'site_id' => '(:site_id)::int',
                'user_id' => '(:user_id)::bigint',
                'type' => '(source->>0)::smallint',
                'val' => '(source->>1)::text',
                'key' => "((source->>2)::varchar || '-' || MD5(LOWER((source->>1)::text)))",
            ])
            ->from(new Expression('jsonb_array_elements((:sources)::jsonb) as source')));

        $this->updateUserCommand = $this->db->createCommand()->update(Users::TABLE_NAME, [
            'cid_reset_at' => new Expression('NOW()'),
            'source_reset_at' => new Expression('CASE WHEN source_reset_at < :reset_at_limit THEN NULL ELSE COALESCE(source_reset_at, NOW()) END'),
        ], [
            'site_id' => new Expression(':site_id'),
            'user_id' => new Expression(':user_id'),
        ]);
    }

    public function process(): void
    {
        $startAt = time();
        $this->prepare();
        $this->totalRows = $this->countTotalRows();
        $this->log->notice(Console::format("Users to source update: {$this->totalRows}", Console::BOLD));

        $processed = 0;
        do {
            $timeBeforeBatch = time();
            $this->log->notice(Console::format('Search updated sources with limit ' . self::USERS_LIMIT_PER_STEP . ' users...', Console::BOLD));
            $searchTime = microtime(true);
            $this->updateUserCommand->bindValue('reset_at_limit', gmdate('Y-m-d H:i:s'));
            $sourceReader = $this->findSourceCommand->query();

            $this->log->notice(Console::format("Found {$sourceReader->count()} user sources by " . number_format(microtime(true) - $searchTime, 3) . 's. Update sources...', Console::BOLD));
            if ($sourceReader->count() === 0) {
                break;
            }

            foreach ($sourceReader as $data) {
                $this->delAndInsertSources($data['site_id'], $data['user_id'], $data['sources']);
                $this->affectedRows += $this->clearSourceReset($data['site_id'], $data['user_id']);
                $processed++;
                $processed % 1000 === 0 && $this->partialLog();
            }
            $processed % 1000 !== 0 && $this->partialLog();
            $processedBy = time() - $timeBeforeBatch;
            $this->log->notice(Console::format("Batch: {$sourceReader->count()} users by {$processedBy}", Console::BOLD, Console::FG_GREEN));
        } while ($sourceReader->count() === self::USERS_LIMIT_PER_STEP);

        $processedBy = time() - $startAt;
        $this->log->notice(Console::format("Completed in {$processedBy}", Console::BOLD, Console::FG_GREEN));
    }

    private function partialLog(): void
    {
        $this->log->notice(
            Str::floatListInfo('Sources', $this->updateSourceTimes, '  ') .
            '      ' .
            Str::floatListInfo('Users', $this->updateUserTimes, '  ') .
            '      ' .
            'by ' .
            number_format(array_sum($this->updateSourceTimes) + array_sum($this->updateUserTimes), 3) . ' sec'
        );
        $this->updateSourceTimes = $this->updateUserTimes = [];
    }

    private function delAndInsertSources(int $siteId, int $userId, string $jsonSources): void
    {
        $timeBeforeUpdate = microtime(true);
        $this->db->transaction(function () use ($siteId, $userId, $jsonSources, &$affected) {
            $affected = $this->deleteSourcesCommand->bindValues([
                'site_id' => $siteId,
                'user_id' => $userId,
            ])->execute();
            $affected += $this->insertSourcesCommand->bindValues([
                'site_id' => $siteId,
                'user_id' => $userId,
                'sources' => $jsonSources,
            ])->execute();
        });
        $this->updateSourceTimes[] = microtime(true) - $timeBeforeUpdate;
    }

    private function clearSourceReset(int $siteId, int $userId): int
    {
        $timeBeforeUpdate = microtime(true);
        $affected = $this->updateUserCommand->bindValues([
            'site_id' => $siteId,
            'user_id' => $userId,
        ])->execute();
        $this->updateUserTimes[] = microtime(true) - $timeBeforeUpdate;
        return $affected;
    }

    private function countTotalRows(): int
    {
        return (int)(new Query($this->db))
            ->select('COUNT(*)')
            ->from(Users::TABLE_NAME)
            ->where(['<', 'source_reset_at', gmdate('Y-m-d H:i:s')])
            ->scalar();
    }

    protected function getTempTableQueryRequisites(): Query
    {
        return (new Query($this->db))
            ->select([
                'ur.site_id',
                'ur.user_id',
                'type' => '(' . UserCloudSource::TYPE_REQUISITE . ')',
                'val' => 'r.requisite',
                'key_type' => "('" . UserCloudSource::KEY_TYPE_PREFIXES[UserCloudSource::TYPE_REQUISITE] . "')",
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE])
            ->innerJoin(['ur' => UserRequisites::TABLE_NAME], 'ur.site_id = uc.site_id AND ur.user_id = uc.user_id')
            ->innerJoin(['r' => Requisites::TABLE_NAME], 'r.id = ur.requisite_id')
            ->andWhere(['NOT LIKE', 'r.requisite', '@'])
            ->andWhere(['NOT IN', 'r.type', [
                Requisite::TYPE_CARD_NUMBER,
                Requisite::TYPE_EMAIL,
            ]]);
    }

    protected function getTempTableQueryWmids(): Query
    {
        return (new Query($this->db))
            ->select([
                'ur.site_id',
                'ur.user_id',
                'type' => '(' . UserCloudSource::TYPE_WM_ID . ')',
                'val' => 'r.wmid',
                'key_type' => "('" . UserCloudSource::KEY_TYPE_PREFIXES[UserCloudSource::TYPE_WM_ID] . "')",
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE])
            ->innerJoin(['ur' => UserRequisites::TABLE_NAME], 'ur.site_id = uc.site_id AND ur.user_id = uc.user_id')
            ->innerJoin(['r' => Requisites::TABLE_NAME], 'r.id = ur.requisite_id')
            ->where([
                'AND',
                ['r.type' => Requisite::TYPE_WALLET_WEBMONEY],
                ['!=', 'r.wmid', ''],
            ]);
    }

    protected function getTempTableQueryFaceGroups(): Query
    {
        return (new Query($this->db))
            ->select([
                'ud.site_id',
                'ud.user_id',
                'type' => '(' . UserCloudSource::TYPE_FACE . ')',
                'val' => '(udf.similar_id::text)',
                'key_type' => "('" . UserCloudSource::KEY_TYPE_PREFIXES[UserCloudSource::TYPE_FACE] . "')",
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE])
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.site_id = uc.site_id AND ud.user_id = uc.user_id')
            ->innerJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'ud.id = udf.user_document_id')
            ->where([
                'AND',
                ['IS NOT', 'udf.similar_id', null],
                ['IS NOT', 'udf.is_valid', new Expression('FALSE')],
            ]);
    }

    protected function getTempTableQueryUsersEmails(): Query
    {
        return (new Query($this->db))
            ->select([
                'c.site_id',
                'c.user_id',
                'type' => '(' . UserCloudSource::TYPE_EMAIL . ')',
                'val' => 'c.value',
                'key_type' => "('" . UserCloudSource::KEY_TYPE_PREFIXES[UserCloudSource::TYPE_EMAIL] . "')",
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE])
            ->innerJoin(['c' => UserContacts::TABLE_NAME], 'uc.site_id = c.site_id AND uc.user_id = c.user_id AND c.type = :contact_type AND c.status = :contact_status')
            ->innerJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = uc.site_id AND usi.user_id = uc.user_id AND usi.dep_lt_count > 0')
            ->where(UserContact::notTooShortEmailCondition('c'))
            ->addParams([
                ':contact_type' => UserContact::TYPE_EMAIL,
                ':contact_status' => UserContact::STATUS_ACTIVE,
            ]);
    }

    protected function getTempTableQueryUsersSocials(): Query
    {
        return (new Query($this->db))
            ->select([
                'u.site_id',
                'u.user_id',
                'type' => '(' . UserCloudSource::TYPE_SOCIAL . ')',
                'val' => "(social_id || ':' || social_key)",
                'key_type' => "('" . UserCloudSource::KEY_TYPE_PREFIXES[UserCloudSource::TYPE_SOCIAL] . "')",
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE])
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = uc.site_id AND u.user_id = uc.user_id')
            ->innerJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id AND usi.dep_lt_count > 0')
            ->where([
                'AND',
                ['IS NOT', 'u.social_id', null],
                ['!=', 'u.social_key', ''],
            ]);
    }

    protected function getTempTableQueryUsersTaxNumbers(): Query
    {
        return (new Query($this->db))
            ->select([
                'usi.site_id',
                'usi.user_id',
                'type' => '(' . UserCloudSource::TYPE_TAX_NUMBER . ')',
                'val' => "(usi.tax_number_type || ':' || usi.tax_number)",
                'key_type' => "('" . UserCloudSource::KEY_TYPE_PREFIXES[UserCloudSource::TYPE_TAX_NUMBER] . "')",
            ])
            ->from(['uc' => self::TMP_USERS_CLOUD_TABLE])
            ->innerJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = uc.site_id AND usi.user_id = uc.user_id AND usi.dep_lt_count > 0')
            ->where([
                'AND',
                ['IS NOT', 'usi.tax_number', null],
                ['IS NOT', 'usi.tax_number_type', null],
            ]);
    }
}
