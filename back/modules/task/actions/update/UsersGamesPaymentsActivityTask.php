<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\modules\task\BaseTask;
use app\back\repositories\views\UserGamePaymentActivities;

/** @see https://jira.syneforge.com/browse/AN-2834 */
class UsersGamesPaymentsActivityTask extends BaseTask
{
    public function __construct(
        private readonly UserGamePaymentActivities $userGamesPaymentsActivitiesRepo,
    ) {
    }

    public function process(): void
    {
        $this->userGamesPaymentsActivitiesRepo->refresh();
    }
}
