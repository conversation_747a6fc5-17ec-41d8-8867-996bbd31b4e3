<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\entities\Rate;
use app\back\entities\UserTransaction;
use app\back\modules\task\CurrencyConversionTrait;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\Rates;
use app\back\repositories\UserTransactions;

class UsersCheckboxReceiptsRefundsTask extends ImportTask
{
    use CurrencyConversionTrait;
    use TaskWithFromToRequest;

    private const string REFUND_TYPE_NAME = 'refund';

    public function __construct(
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly UserTransactions $userTransactionsRepo,
        private readonly Rates $ratesRepo,
    ) {
    }

    protected function beforeFind(array &$row): bool
    {
        if ($row['checkout_type_name'] !== self::REFUND_TYPE_NAME) {
            return false;
        }

        $row['site_id'] = $this->siteIdResolver->siteId();

        /** @var UserTransaction $existingTransaction */
        $existingTransaction = $this->userTransactionsRepo->findOne(['site_id' => $row['site_id'], 'transaction_id' => $row['transaction_id']]);

        if ($existingTransaction !== null && $existingTransaction->pay_sys_id !== null) {
            // Skip if transaction already exists
            return false;
        }

        $row['currency'] = Rate::UAH;
        $row['created_at'] = $row['updated_at'];
        $row['op_id'] = UserTransaction::OP_OUT;
        $row['status'] = UserTransaction::STATUS_SUCCESS;
        $row['balance_type'] = UserTransaction::BALANCE_TYPE_REAL;

        $row['fee_profit'] = $row['amount_with_fee'] - $row['amount_orig'];
        $this->convertCurrencyAmounts($row);

        return true;
    }

    protected function repository(): BaseRepository
    {
        return $this->userTransactionsRepo;
    }
}
