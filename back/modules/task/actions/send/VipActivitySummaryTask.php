<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\entities\Operator;
use app\back\entities\UserTransaction;
use app\back\repositories\CustomerUsers;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class VipActivitySummaryTask extends NotifyPmBaseTask
{
    public string $parentPersonalManagerEmail;

    private const array INTERVAL_DAYS = [
        //   from, to days
        8 =>  [1, 8],
        15 => [8, 15],
        22 => [15, 22],
        1 =>  [22, 1],
    ];

    private array $intervals = [];

    private string $dateReportFrom;
    private string $dateReportTo;
    private string $dateStatFrom;

    private function setSettingsDates(): void
    {
        $fromTime = strtotime($this->from);
        foreach (self::INTERVAL_DAYS as [$fromDay, $toDay]) {
            $this->intervals[] = $this->interval($fromDay, (int)$toDay, $fromTime, 'target');
        }

        uasort($this->intervals, static fn($l, $r) => $r['from'] <=> $l['from']);

        $earliestInterval = $this->intervals[array_key_last($this->intervals)];
        $previousInterval = self::INTERVAL_DAYS[$earliestInterval['fromDay']];
        $this->dateStatFrom = $earliestInterval['from'];

        $earliestInterval = $this->interval($previousInterval[0], $previousInterval[1], strtotime($earliestInterval['from']), 'stat');
        $this->intervals[] = $earliestInterval;
        $latestInterval = $this->intervals[array_key_first($this->intervals)];

        $this->dateReportFrom = $earliestInterval['from'];
        $this->dateReportTo = $latestInterval['to'];
    }

    private function interval(int $fromDay, int $toDay, int $fromTime, string $type): array
    {
        $startDay = date('d', strtotime($this->from));

        $monthFromTime = $fromTime;

        if ($startDay <= $fromDay) {
            $monthFromTime = strtotime('first day of -1 month', $fromTime);
        }
        $monthToTime = $toDay === 1 ? $fromTime : $monthFromTime;

        return [
            'from' => date(sprintf("Y-m-%02d", $fromDay), $monthFromTime),
            'to' => date(sprintf("Y-m-%02d", $toDay), $monthToTime),
            'fromDay' => $fromDay,
            'type' => $type,
        ];
    }

    protected function title(): string
    {
        return "VIP Activity Summary {$this->to}";
    }

    protected function columns(): array
    {
        $columns = [
            'customer_id' => 'Customer ID',
            'site' => 'Site',
            'user_id' => 'User ID',
            'manager' => 'Manager',
        ];

        foreach (array_filter($this->intervals, static fn($interval) => $interval['type'] === 'target') as ['from' => $date]) {
            $columns["dep_sum_{$date}"] = "Dep sum ({$date} RUB)";
            $columns["wd_sum_{$date}"] = "'Wd sum ({$date} RUB)";
            $columns["ngr_sum_{$date}"] = "NGR sum ({$date} RUB)";
            $columns["prev_dep_sum_{$date}"] = "Prev. Dep sum ({$date} RUB)";
            $columns["prev_wd_sum_{$date}"] = "Prev. Wd sum ({$date} RUB)";
            $columns["prev_ngr_sum_{$date}"] = "Prev. NGR sum ({$date} RUB)";
        }

        return $columns;
    }

    private function createUsersQuery(): Query
    {
        $operatorIds = [
            ...$this->operatorsRepo->getOperatorIdsByGroupIds([Operator::GROUP_VIP_ULTRA]),
            $this->operatorsRepo->getIdByEmail($this->parentPersonalManagerEmail),
        ];

        $sourceUsersQuery = (new Query($this->db))
            ->select([
                'u.site_id',
                'u.user_id',
                'u.personal_manager',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->where([
                'u.personal_manager' => $operatorIds,
            ]);

        return (new Query($this->db))
            ->select([
                'site_id' => 'COALESCE(u_t.site_id, u_s.site_id)',
                'user_id' => 'COALESCE(u_t.user_id, u_s.user_id)',
                'personal_manager' => 'u_s.personal_manager',
                'manager' => 'COALESCE(u_t.personal_manager, u_s.personal_manager)',
                'customer_id' => 'COALESCE(cu_t.customer_id, cu_s.customer_id)',
            ])
            ->from(['u_s' => $sourceUsersQuery])
            ->leftJoin(['cu_s' => CustomerUsers::TABLE_NAME], 'cu_s.site_id = u_s.site_id AND cu_s.user_id = u_s.user_id')
            ->leftJoin(['cu_t' => CustomerUsers::TABLE_NAME], 'cu_t.customer_id = cu_s.customer_id')
            ->leftJoin(['u_t' => Users::TABLE_NAME], 'u_t.site_id = cu_t.site_id AND u_t.user_id = cu_t.user_id');
    }

    protected function data(): iterable
    {
        $this->setSettingsDates();

        $usersQuery = $this->createUsersQuery();

        $intervalExpr = '(CASE ';
        foreach ($this->intervals as $interval) {
            $intervalExpr .= "WHEN us.updated_at >= '{$interval['from']}' AND us.updated_at < '{$interval['to']}' THEN '{$interval['from']}' ";
        }
        $intervalExpr .= 'END)';

        $statsQuery = (new Query($this->db))
            ->select([
                'date' => $intervalExpr,
                'u.site_id',
                'u.user_id',
                'dep_sum' => 'COALESCE(SUM(us.amount_rub) FILTER (WHERE us.dir = :dir_in), 0)',
                'wd_sum' => 'COALESCE(SUM(us.amount_rub) FILTER (WHERE us.dir = :dir_out), 0)',
            ])
            ->from('u')
            ->leftJoin(['us' => UserTransactions::TABLE_NAME], 'u.site_id = us.site_id AND u.user_id = us.user_id')
            ->where([
                'AND',
                [
                    'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                ],
                ['>=', 'us.updated_at', $this->dateStatFrom],
                ['<', 'us.updated_at', $this->dateReportTo],
            ])
            ->groupBy(['u.site_id', 'u.user_id', $intervalExpr])
            ->addParams([
                'dir_in' => UserTransaction::DIR_IN,
                'dir_out' => UserTransaction::DIR_OUT,
            ]);

        $intervalValues =  "(VALUES ('" . implode("'), ('", array_column($this->intervals, 'from')) .  "')) i(date)";

        $rawDataQuery = (new Query($this->db))
            ->withQuery($usersQuery, 'u')
            ->select([
                'u.site_id',
                'u.user_id',
                'i.date',
                'u.customer_id',
                'u.personal_manager',
                'u.manager',
                'wd_sum' => 'us.wd_sum',
                'dep_sum' => 'us.dep_sum',
                'ngr_sum' => '(us.dep_sum - us.wd_sum)',
            ])
            ->from('u')
            // prevent interval gap if user have no payments
            ->join('CROSS JOIN', $intervalValues)
            ->leftJoin(['us' => $statsQuery], 'u.site_id = us.site_id AND u.user_id = us.user_id AND i.date = us.date');

        $dataWithPrevQuery = (new Query($this->db))
            ->select([
                'date',
                'customer_id',
                'site' => 'site_id',
                'user_id',
                'personal_manager',
                'manager',
                'dep_sum',
                'wd_sum',
                'ngr_sum',
                'prev_dep_sum' => 'LAG(dep_sum, -1) OVER (PARTITION BY site_id, user_id ORDER BY date DESC)',
                'prev_wd_sum' => 'LAG(wd_sum, -1) OVER (PARTITION BY site_id, user_id ORDER BY date DESC)',
                'prev_ngr_sum' => 'LAG(dep_sum - wd_sum, -1) OVER (PARTITION BY site_id, user_id ORDER BY date DESC)',
            ])
            ->from($rawDataQuery);

        $data = (new Query($this->db))
            ->select([
                'customer_id',
                'site',
                'user_id',
                'personal_manager',
                'manager',

                'data' => "jsonb_agg(jsonb_build_object(
                    'dep_sum_' || date, dep_sum,
                    'wd_sum_' || date, wd_sum,
                    'ngr_sum_' || date, ngr_sum,
                    'prev_dep_sum_' || date, prev_dep_sum,
                    'prev_wd_sum_' || date, prev_wd_sum,
                    'prev_ngr_sum_' || date, prev_ngr_sum
                 ))",
            ])
            ->from($dataWithPrevQuery)
            ->where(['>=', 'date', $this->dateReportFrom])
            ->groupBy(['site', 'personal_manager', 'customer_id', 'user_id', 'manager'])
            ->orderBy([
                'site' => SORT_ASC,
                'personal_manager' => SORT_DESC,
                'customer_id' => SORT_DESC,
                'user_id' => SORT_DESC,
            ])
            ->all();

        foreach ($data as &$row) {
            $row['site'] = $this->sitesRepo->getNameById($row['site']);
            $row['manager'] = $this->operatorsRepo->getEmailById($row['manager']);

            foreach (Arr::groupsToFlat(Json::decode($row['data'])) as $col => $value) {
                $row[$col] = $value;
            }
        }

        return $data;
    }
}
