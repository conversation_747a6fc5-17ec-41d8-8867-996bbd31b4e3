<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Initializable;
use app\back\components\SiteUserBuilder;
use app\back\entities\Rate;
use app\back\entities\User;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\repositories\Rates;
use app\back\repositories\Sites;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class NotifyBigDepUsersTask extends BaseSendTask
{
    public array $siteIds = [];

    public string $currency;
    public int $depThreshold;
    public string $contactName;
    public array $localeFilter = [];

    private readonly Sites $sitesRepo;
    private readonly ConnectionInterface $db;
    private readonly SiteUserBuilder $siteUserBuilder;
    private readonly Rates $ratesRepo;
    private readonly UserWallets $userWalletsRepo;

    #[Initializable]
    final public function init(Sites $sitesRepo, ConnectionInterface $db, SiteUserBuilder $siteUserBuilder, Rates $ratesRepo, UserWallets $userWalletsRepo): void
    {
        $this->sitesRepo = $sitesRepo;
        $this->db = $db;
        $this->siteUserBuilder = $siteUserBuilder;
        $this->ratesRepo = $ratesRepo;
        $this->userWalletsRepo = $userWalletsRepo;
    }

    protected function getContactName(): string
    {
        return $this->contactName;
    }

    public function getSubject(): string
    {
        return 'Users with big deposit (' . date('Y-m-d', $this->fromTime) . ')';
    }

    public function getContent(): string
    {
        $today = date('Y-m-d', strtotime('+ 1 day', $this->fromTime));
        $yesterday = date('Y-m-d', $this->fromTime);
        $dbYesterday = date('Y-m-d', strtotime('- 1 day', $this->fromTime));
        $currencyPostfix = strtolower($this->currency);
        $yesterdayDepExpression = "SUM(us.amount_$currencyPostfix) FILTER (WHERE us.updated_at >= :yesterday)";

        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);

        $query = (new Query($this->db))
            ->select([
                'u.site_id',
                'u.user_id',
                'status' => User::getFullStatusExpression(),
                'date' => 'u.date',
                'balance' => 'ROUND(ANY_VALUE(uw.balance_usd))', // USD for now, further conversion needed
                'in_1' => "ROUND($yesterdayDepExpression)",
                'in_2' => "ROUND(SUM(us.amount_$currencyPostfix))",
                'in_1_rub' => "ROUND(SUM(us.amount_rub) FILTER (WHERE us.updated_at >= :yesterday))",
                'in_2_rub' => "ROUND(SUM(us.amount_rub))",
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'us.site_id = u.site_id AND us.user_id = u.user_id')
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true')
            ->where([
                'AND',
                [
                    'us.dir' => UserTransaction::DIR_IN,
                    'us.ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.site_id' => $this->siteIds,
                ],
                ['>=', 'us.updated_at', $dbYesterday],
                ['<', 'us.updated_at', $today],

            ])
            ->groupBy(['u.site_id', 'u.user_id'])
            ->having(['>=', $yesterdayDepExpression, $this->depThreshold])
            ->orderBy(['u.date' => SORT_ASC])
            ->addParams([
                'yesterday' => $yesterday,
            ]);

        if ($this->localeFilter) {
            $query->andWhere([$this->localeFilter[0], 'u.locale', $this->localeFilter[1]]);
        }

        $data = $query->all();

        $title = $this->getSubject();

        $cols = [
            'site' => 'Site',
            'user' => 'User',
            'site_user_id' => 'Site-UserId',
            'status' => 'Status',
            'date' => 'Reg Date',
            'balance' => "Balance {$this->currency}",
            'in_1' => "Dep sum {$this->currency} (yesterday)",
            'in_2' => "Dep sum {$this->currency} (before today)",
            'in_1_rub' => "Dep sum RUB (yesterday)",
            'in_2_rub' => "Dep sum RUB (before today)",
        ];

        foreach ($data as &$d) {
            $d['balance'] = $this->ratesRepo->convert($d['balance'], Rate::USD, Rate::EUR);
            $d['site'] = $this->sitesRepo->getShortNameById($d['site_id']);
            $d['site_user_id'] = $this->siteUserBuilder->siteUserToValue($d['site_id'], $d['user_id']);
            $d['user'] = User::playerLink($d['site_id'], $d['user_id']);
        }

        return $this->table($data, $cols, $title);
    }
}
