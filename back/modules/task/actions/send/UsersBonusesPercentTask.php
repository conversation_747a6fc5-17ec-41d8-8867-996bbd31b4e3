<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Container;
use app\back\components\helpers\DateHelper;
use app\back\components\Initializable;
use app\back\entities\UserIgnoreId;
use app\back\modules\reports\reports\UsersBonusesPercent\UsersBonusesPercentConfig;

class UsersBonusesPercentTask extends BaseSendTask
{
    private const int AVG_PERIOD = 3;
    private const int TREND_PERCENT = 5;

    public string $contactName;
    public array $siteIds;
    public bool $groupByBrands = false;
    public string $subjectPrefix;

    protected array $reportParams;

    protected array $parts = [
        ['title' => 'In', 'metric' => 'in'],
        ['title' => 'In - Out', 'metric' => 'in_out'],
        ['title' => 'Bonus Balance', 'metric' => 'bonus_balance'],
        ['title' => 'Bonus Transfer', 'metric' => 'transfer'],
    ];

    private readonly Container $container;

    #[Initializable]
    final public function init(Container $container): void
    {
        $this->container = $container;
    }

    public function setReportParams(): void
    {
        $this->reportParams = [
            'isHtmlVersion' => true,
            'isTotals' => true,
            'filters' => [
                ['date_range', DateHelper::range(date('Y-m-d', strtotime('-10 days', $this->fromTime)), date('Y-m-d', $this->fromTime))],
                ['site_id', $this->siteIds],
                ['ignore', UserIgnoreId::MODE_IGNORE],
                ['avg_periods', self::AVG_PERIOD],
                ['trend_percent', self::TREND_PERCENT],
            ],
            'groups' => ['date'],
            'split' => $this->groupByBrands ? 'site_id_brand_id' : 'site_id',
        ];
    }

    protected function getContactName(): string
    {
        return $this->contactName;
    }

    public function getSubject(): string
    {
        return "$this->subjectPrefix users bonuses percent (" . date('Y-m-d', $this->fromTime) . ')';
    }

    public function getContent(): string
    {
        $this->setReportParams();

        $tables = [];

        foreach ($this->parts as ['title' => $title, 'metric' => $metric]) {
            $reportParams = array_merge($this->reportParams, [
                'metrics' => [$metric],
            ]);

            $report = (new UsersBonusesPercentConfig($this->container))->loadAndValidateOrException($reportParams);

            ['data' => $data, 'columns' => $cols] = $report->dataAndColumns();

            $this->replaceToStyleObjects($data, $cols);

            if (empty($data)) {
                continue;
            }

            $tables[] = $this->table($data, $cols, $title, ['condensed' => true]);
        }

        return implode('', $tables);
    }
}
