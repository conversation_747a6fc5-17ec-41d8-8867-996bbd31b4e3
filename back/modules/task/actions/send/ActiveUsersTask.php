<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\send;

use app\back\components\Initializable;
use app\back\components\SiteUserBuilder;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserContact;
use app\back\entities\UserTransaction;
use app\back\repositories\Sites;
use app\back\repositories\UserContacts;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class ActiveUsersTask extends BaseSendTask
{
    private const int DEP_SUM_RUB = 50000;

    private readonly SiteUserBuilder $siteUserBuilder;
    private readonly ConnectionInterface $db;
    private readonly Sites $sitesRepo;

    #[Initializable]
    final public function init(Sites $sitesRepo, ConnectionInterface $db, SiteUserBuilder $siteUserBuilder): void
    {
        $this->sitesRepo = $sitesRepo;
        $this->db = $db;
        $this->siteUserBuilder = $siteUserBuilder;
    }

    protected function getContactName(): string
    {
        return 'active_users';
    }

    public function getSubject(): string
    {
        return 'Active users (' . date('Y-m-d', $this->fromTime) . ')';
    }

    public function getContent(): string
    {
        return "Users with more than " . self::DEP_SUM_RUB . " rub dep sum for " . date('Y-m-d', $this->fromTime);
    }

    public function getAttachments(): array
    {
        $today = date('Y-m-d', strtotime('+1 day', $this->fromTime));
        $yesterday = date('Y-m-d', $this->fromTime);
        $days30ago = date('Y-m-d', strtotime('-30 days', strtotime($today)));

        $data = (new Query($this->db))
            ->select([
                'u.site_id',
                'u.user_id',
                'status' => User::getFullStatusExpression(),
                'phone' => 'MIN(uc.value)',
                'dep_sum_yesterday' => 'SUM(us.amount_rub) FILTER (WHERE us.updated_at >= :yesterday)',
                'dep_sum_30d' => 'SUM(us.amount_rub)',
                'personal_manager' => 'u.personal_manager',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['us' => UserTransactions::TABLE_NAME], 'us.site_id = u.site_id AND us.user_id = u.user_id')
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id')
            ->leftJoin(['uc' => UserContacts::TABLE_NAME], 'uc.site_id = u.site_id AND uc.user_id = u.user_id AND uc.type = :phone AND uc.priority = true')
            ->where([
                'AND',
                ['>=', 'us.updated_at', $days30ago],
                ['<', 'us.updated_at', $today],
                [
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.op_id' => UserTransaction::OP_IN,
                    'us.site_id' => Site::getVipSites(),
                ]
            ])
            ->groupBy(['u.site_id', 'u.user_id'])
            ->having(['>=', 'SUM(us.amount_rub) FILTER (WHERE us.updated_at >= :yesterday)', self::DEP_SUM_RUB])
            ->addParams([
                'phone' => UserContact::TYPE_PHONE,
                'yesterday' => $yesterday
            ])
            ->all();

        $cols = [
            'date' => 'Date',
            'site_user_id' => 'Site-User ID',
            'user_id' => 'User ID',
            'site' => 'Site name',
            'phone' => 'Phone',
            'status' => 'Status',
            'dep_sum_yesterday' => 'Dep sum yesterday',
            'dep_sum_30d' => 'Dep sum 30 days',
            'personal_manager' => 'Personal manager',
        ];

        foreach ($data as &$d) {
            $d['date'] = $yesterday;
            $d['site_user_id'] = $this->siteUserBuilder->siteUserToValue($d['site_id'], (int) $d['user_id']);
            $d['site'] = $this->sitesRepo->getShortNameById($d['site_id']);
            $d['phone'] = $d['phone'] ? 'yes' : 'no';
            $d['personal_manager'] = $this->operatorsRepo->getNameById($d['personal_manager']);
        }

        return [
            ['columns' => $cols, 'data' => $data],
        ];
    }
}
