<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\components\services\FileStorage;
use app\back\entities\Site;
use app\back\entities\UserDocument;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocuments;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\Users;
use Yiisoft\Db\Query\Query;

class CopyUsersDocumentsTask extends BaseTask
{
    public function __construct(
        private readonly UserDocuments $dosRepo,
        private readonly UserDocumentsActive $docsActiveRepo,
        private readonly FileStorage $storage,
    ) {
    }

    public function process(): void
    {
        $fromSiteId = Site::S7;
        $fromBrandId = 159;
        $toSiteId = Site::WIN;

        $query = (new Query($this->docsActiveRepo->db))
            ->from(['ud' => $this->docsActiveRepo::TABLE_NAME])
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = ud.site_id AND u.user_id = ud.user_id')
            ->where([
                'u.site_id' => $fromSiteId,
                'u.brand_id' => $fromBrandId,
            ]);

        /** @var UserDocument[] $oldDocs */
        $oldDocs = $this->docsActiveRepo->findByQuery($query);

        foreach ($oldDocs as $oldDoc) {
            $newDocExists = $this->dosRepo->findOne([
                'site_id' => $toSiteId,
                'user_id' => $oldDoc->user_id,
                'filename' => $oldDoc->filename,
            ]);

            if ($newDocExists) {
                $this->log->error("Document for user {$oldDoc->user_id} already exists: {$oldDoc->filename}");
                continue;
            }

            $newDoc = clone $oldDoc;
            unset($newDoc->id);
            $newDoc->site_id = $toSiteId;
            $newDoc->face_processed = false;

            $from = $oldDoc->storagePath();
            $to = $newDoc->storagePath();
            $this->storage->copy($from, $to) or throw new \Exception("Unable to copy uploaded file $from => $to");
            $this->log->debug("Copied $from => $to");

            $this->docsActiveRepo->insert($newDoc);
        }
    }
}
