<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersDocumentsFileNotExistsDeleteTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Users $usersRepo,
        private readonly UserDocumentsActive $docsActiveRepo,
        private readonly FileStorage $storage,
        private readonly UserDocumentFaces $facesRepo,
    ) {
    }

    public function process(): void
    {
        $query = (new Query($this->db))
            ->from(['x' => UserDocumentsActive::TABLE_NAME]);

        $this->totalRows = $query->count();

        foreach ($this->docsActiveRepo->findByQuery($query) as $key => $doc) {
            /** @var $doc UserDocument */
            $progressInfo = str_pad(($key + 1) . '/' . $this->totalRows, 10, ' ');

            $file = $doc->storagePath();

            if (!$this->storage->keyExist($file)) {
                $this->log->debug("{$progressInfo} DELETE NOT FOUND: {$file}");
                $this->usersRepo->resetCloudSources($doc->site_id, $doc->user_id);
                $this->facesRepo->deleteByDocumentId($doc->id);
                $this->affectedRows += $this->docsActiveRepo->delete($doc);
            }

            if (($key + 1) % 100 === 0) {
                $this->log->debug("{$progressInfo} PROCESSED");
            }
        }
    }
}
