<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\components\Console;
use app\back\components\ConsoleTable;
use app\back\components\exceptions\NotFoundException;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\helpers\Url;
use app\back\components\Initializable;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentFace;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class CheckPgVectorTask extends BaseTask
{
    private int $direction;
    private array $errors = [];
    private int $startFromId;
    private int $diffCount = 0;
    private int $numPad;
    private float $timeIndexedSum = 0;
    private float $timeFullSum = 0;
    private int $probes;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly UserDocumentsActive $docsActiveView,
        private readonly UserDocumentFaces $facesRepo,
        private readonly UserDocumentFaceSimilarities $faceSimilaritiesRepo,
    ) {
    }

    #[Initializable]
    final public function setBaseOptions(): void
    {
        try {
            $mode = Json::decode($this->mode ?? '{}');
        } catch (\Throwable) {
            throw new \InvalidArgumentException('Invalid --mode=' . $this->mode);
        }

        if (isset($mode['probes'])) {
            $this->probes = (int)$mode['probes'];
            unset($mode['probes']);
        } else {
            $this->probes = (int)($_ENV['FACE_EMBEDDING_INDEX_PROBES'] ?? 1);
        }

        if (isset($mode['fromId'])) {
            $this->startFromId = $mode['fromId'];
            $this->direction = SORT_ASC;
            unset($mode['fromId']);
        } else {
            $this->startFromId = (new Query($this->facesRepo->db))
                ->select('MAX(id)')
                ->from(UserDocumentFaces::TABLE_NAME)
                ->limit(1)
                ->scalar() + 1;
            $this->direction = SORT_DESC;
        }

        if (!empty($mode)) {
            throw new \InvalidArgumentException('Invalid mode: ' . Json::encode($mode));
        }

        $this->numPad = strlen((string)($this->limit ?? 9999)) * 2 + 3;

        $indexScanType = $this->probes . '/' . (new Query($this->db))
            ->select("(regexp_match(indexdef, 'lists=''(\d+)'''))[1]::int")
            ->from('pg_indexes')
            ->where(['indexname' => 'users_documents_faces_face_embedding'])
            ->scalar();
        Console::write("Index (probes/lists): $indexScanType");
    }

    public function process(): void
    {
        do {
            try {
                $face = $this->nextFace($face->id ?? $this->startFromId);
            } catch (NotFoundException) {
                break;
            }
            $this->totalRows++;

            $this->faceSimilaritiesRepo->setIvfflatIndexProbes($this->probes);
            $simsIndexed = $this->trackTime(fn() => $this->faceSimilaritiesRepo->findSimilar($face), $timeIndexed, $this->timeIndexedSum);
            $simsIndexed = Arr::index($simsIndexed, static fn($s) => "$face->id-$s[id]");

            $this->faceSimilaritiesRepo->setIvfflatIndexProbes(null);
            $simsFull = $this->trackTime(fn() => $this->faceSimilaritiesRepo->findSimilar($face), $timeFullScan, $this->timeFullSum);
            $simsFull = Arr::index($simsFull, static fn($s) => "$face->id-$s[id]");

            $diff = $this->checkSimsMismatches($face->id, $simsFull, $simsIndexed);

            /** @var UserDocument $doc */
            $doc = $this->docsActiveView->findOneOr404(['id' => $face->user_document_id]);
            $link = Url::to('finance/documents', [
                'siteId' => $doc->site_id,
                'userId' => $doc->user_id,
                '#' => "$doc->filename-$face->number-$face->id",
            ], true);

            if (!empty($diff)) {
                $this->diffCount++;
                $matchDiff = str_pad('x' . count($simsFull) . "=>x" . count($simsIndexed), 10);
                Console::error("{$this->stat($timeFullScan, $timeIndexed)} $matchDiff $link lost: {$this->simInfo(Arr::leaveOnlyKeys($simsFull, $diff))}");
            } else {
                $this->affectedRows++;
                $matches = str_pad('x' . count($simsIndexed), 10);
                Console::write("[OK]    {$this->stat($timeFullScan, $timeIndexed)} $matches $link   ok: {$this->simInfo($simsIndexed)}");
            }

            if (isset($this->limit) && $this->totalRows >= $this->limit) {
                break;
            }
        } while (true);

        if (count($this->errors)) {
            ksort($this->errors);
            ConsoleTable::dump([['Lost' => date('Y-m-d H:i:s')] + $this->errors]);
        }
        Console::write('Time ' . $this->timeFormat($this->timeFullSum / ($this->totalRows ?: 1)) . ' => ' . $this->timeFormat($this->timeIndexedSum / ($this->totalRows ?: 1)));
    }

    private function timeFormat(float $time): string
    {
        return number_format($time, 3, '.', '');
    }

    private function stat(float $timeFullScan, float $timeIndexScan): string
    {
        $num = str_pad("$this->totalRows(-$this->diffCount)", $this->numPad, pad_type: STR_PAD_LEFT);
        return "$num.   {$this->timeFormat($timeFullScan)}=>{$this->timeFormat($timeIndexScan)}s";
    }

    private function trackTime(\Closure $closure, ?float &$setTime, ?float &$addTime = null): mixed
    {
        $time = microtime(true);
        $result = $closure();
        $setTime = microtime(true) - $time;
        $addTime += $setTime;
        return $result;
    }

    private function checkSimsMismatches(int $faceId, array $simsOld, array $simsNew): array
    {
        $diff = [];
        $compares = array_unique(array_merge(array_keys($simsOld), array_keys($simsNew)));
        foreach ($compares as $c) {
            if (isset($simsNew[$c], $simsOld[$c])) {
                continue;
            }
            $diff[] = $c;
            if (isset($simsOld[$c])) {
                $keyError = str_replace('.', '', number_format((float)$simsOld[$c]['distance'], 2));
                $this->errors[$keyError] ??= 0;
                $this->errors[$keyError]++;
            } else {
                Console::write("Undefined error $faceId");
            }
        }
        return $diff;
    }

    private function simInfo(array $sims): string
    {
        usort($sims, static fn($a, $b) => $a['distance'] <=> $b['distance']);
        $sims = array_map(static fn($r) => number_format((float)$r['distance'], 3, '.', '') . ':' . $r['id'], array_splice($sims, 0, 3));
        return  implode(', ', $sims);
    }

    private function nextFace(int $prevId): UserDocumentFace
    {
        $face = $this->facesRepo->findOneByQuery(
            (new Query($this->db))
                ->where([match ($this->direction) {
                    SORT_ASC => '>',
                    SORT_DESC => '<',
                }, 'id', $prevId])
                ->orderBy(['id' => $this->direction])
                ->limit(1)
        ) or throw new NotFoundException([]);
        $face instanceof UserDocumentFace or throw new \RuntimeException();
        return $face;
    }
}
