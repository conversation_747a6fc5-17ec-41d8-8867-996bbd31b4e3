<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\modules\task\BaseTask;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocuments;
use app\back\repositories\Users;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersDocumentsCleanOrphansTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly UserDocuments $userDocumentsRepo,
        private readonly UserDocumentFaces $docFacesRepo,
        private readonly FileStorage $fileStorage,
    ) {
    }

    public function process(): void
    {
        /** @var UserDocument[] $documentsWithoutUsers */
        $documentsWithoutUsers = $this->userDocumentsRepo->findByQuery((new Query($this->db))
            ->from(['ud' => UserDocuments::TABLE_NAME])
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ud.site_id AND u.user_id = ud.user_id')
            ->where(['IS', 'u.site_id', null])
            ->orderBy(['ud.id' => SORT_ASC]));

        foreach ($documentsWithoutUsers as $doc) {
            $this->totalRows++;
            $key = $doc->storagePath();
            if ($this->fileStorage->keyExist($key)) {
                $this->fileStorage->delete($key);
                $this->userDocumentsRepo->delete($doc);
                $this->docFacesRepo->deleteByDocumentId($doc->id);
                $this->log->info("Document {$doc->id} deleted for user {$doc->site_id}-{$doc->user_id}");
                $this->affectedRows++;
            }
        }
    }
}
