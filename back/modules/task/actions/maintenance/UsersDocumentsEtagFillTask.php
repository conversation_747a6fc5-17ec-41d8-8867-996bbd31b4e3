<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\components\exceptions\NotFoundException;
use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\modules\task\BaseTask;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersDocumentsEtagFillTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly UserDocumentsActive $docsActiveRepo,
        private readonly FileStorage $fileStorage,
    ) {
    }

    public function process(): void
    {
        $minDocId = 0;
        do {
            $minDocPrev = $minDocId;
            $etagTime = $contentTime = [];
            foreach ($this->emptyETagDocuments($minDocId) as $doc) {
                /** @var UserDocument $doc */
                $minDocId = $doc->id;
                try {
                    $this->updateETag($doc, $etagTime, $contentTime);
                } catch (NotFoundException) {
                    $this->log->error("Storage file not found {$doc->storagePath()}");
                    continue;
                }
            }
            $this->log->debug("$this->totalRows/$this->affectedRows documents, last $minDocId, etagTime: {$this->timeArrayToString($etagTime)}, contentTime: {$this->timeArrayToString($contentTime)}");
        } while ($minDocPrev < $minDocId);
    }

    private function updateETag(UserDocument $doc, array &$etagTime, array &$contentTime): void
    {
        $startTime = microtime(true);
        $doc->etag = trim($this->fileStorage->head($doc->storagePath())?->get('ETag') ?? '', '"');
        $etagTime[] = microtime(true) - $startTime;
        if (strlen($doc->etag) !== 32) {
            $startTime = microtime(true);

            $doc->etag = md5($this->fileStorage->getContents($doc->storagePath()));

            $contentTime[] = microtime(true) - $startTime;
            $this->affectedRows++;
        }
        $this->totalRows += $this->docsActiveRepo->update($doc, ['etag']);
    }

    private function timeArrayToString(array $times): string
    {
        if (empty($times)) {
            return '0';
        }
        $timeSum = (int)array_sum($times);
        $timeAvg = number_format(array_sum($times) / count($times), 3);
        return "$timeSum($timeAvg)";
    }

    private function emptyETagDocuments(int $minDocId): iterable
    {
        return $this->docsActiveRepo->findByQuery((new Query($this->db))
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->where([
                'AND',
                ['etag' => null],
                ['>', 'id', $minDocId]
            ])
            ->orderBy(['id' => SORT_ASC])
            ->limit(1000));
    }
}
