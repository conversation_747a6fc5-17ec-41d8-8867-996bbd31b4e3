<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\maintenance;

use app\back\components\helpers\Json;
use app\back\components\services\FileStorage;
use app\back\modules\finance\documents\DocumentsDeleteRestoreForm;
use app\back\modules\task\BaseTask;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersDocumentsEtagDeduplicateTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly FileStorage $fileStorage,
        private readonly DocumentsDeleteRestoreForm $deleteForm,
    ) {
    }

    public function process(): void
    {
        $lastSiteId = $lastUserId = 0;
        do {
            $startTime = time();
            [$prevSiteId, $prevUserId] = [$lastSiteId, $lastUserId];
            foreach ($this->getDuplicates($lastSiteId, $lastUserId) as $duplicates) {
                $lastUserId = max($duplicates['site_id'] > $lastSiteId ? 0 : $lastUserId, $duplicates['user_id']);
                $lastSiteId = max($lastSiteId, $duplicates['site_id']);

                $checkedMd5 = array_unique(array_map(fn($row) => md5($this->fileStorage->getContents("{$duplicates['site_id']}/{$duplicates['user_id']}/{$row['filename']}")), $duplicates['docs']));
                if (count($checkedMd5) !== 1 || $duplicates['etag'] !== reset($checkedMd5)) {
                    $filenames = implode(', ', array_column($duplicates['docs'], 'filename'));
                    $this->log->error("{$duplicates['site_id']}-{$duplicates['user_id']} MD5 mismatch {$filenames}: {$duplicates['etag']} !== " . implode(', ', $checkedMd5));
                    continue;
                }

                $filenamesToDelete = array_column(array_slice($duplicates['docs'], 1), 'filename');
                $this->log->debug("Delete {$duplicates['site_id']}-{$duplicates['user_id']} " . implode(' ', $filenamesToDelete));
                $this->totalRows = ($this->affectedRows += $this->delete($duplicates['site_id'], $duplicates['user_id'], $filenamesToDelete));
            }
            $this->log->debug("$this->affectedRows documents +" . (time() - $startTime) . 's');
        } while ([$prevSiteId, $prevUserId] !== [$lastSiteId, $lastUserId]);
    }

    private function delete(int $siteId, int $userId, array $filenames): int
    {
        $this->deleteForm->validateOrException(['documents' => array_map(static fn ($filename) => [
            'site_id' => $siteId,
            'user_id' => $userId,
            'filename' => $filename,
        ], $filenames)]);

        return $this->deleteForm->updateIsDeleted(true);
    }

    private function getDuplicates(int $lastSiteId, int $lastUserId): iterable
    {
        $rows = (new Query($this->db))
            ->withQuery((new Query($this->db))
                ->select(['site_id', 'user_id', 'etag', 'duplicates' => 'COUNT(*)'])
                ->from(UserDocumentsActive::TABLE_NAME)
                ->where([
                    'AND',
                    ['IS NOT', 'etag', null],
                    [
                        'OR',
                        ['>', 'site_id', $lastSiteId],
                        ['AND', ['site_id' => $lastSiteId], ['>', 'user_id', $lastUserId]],
                    ]
                ])
                ->groupBy(['site_id', 'user_id', 'etag'])
                ->having(['>', 'COUNT(*)', 1])
                ->orderBy(['site_id' => SORT_ASC, 'user_id' => SORT_ASC])
                ->limit(1000), 'dup')
            ->select(['site_id', 'user_id', 'etag', 'docs' => "json_agg(json_build_object('id', id, 'filename', filename) order by id)"])
            ->from(UserDocumentsActive::TABLE_NAME)
            ->where([
                'AND',
                ['IS NOT', 'etag', null],
                ['IN', ['site_id', 'user_id'], (new Query($this->db))->distinct()->select(['site_id', 'user_id'])->from('dup')],
            ])
            ->groupBy(['site_id', 'user_id', 'etag'])
            ->having(['>', 'COUNT(*)', 1])
            ->all();

        array_walk($rows, static function (&$row) {
            $row['etag'] = str_replace('-', '', $row['etag']);
            $row['docs'] = Json::decode($row['docs']);
            usort($row['docs'], static fn($a, $b) => $a['id'] <=> $b['id']);
        });

        return $rows;
    }
}
