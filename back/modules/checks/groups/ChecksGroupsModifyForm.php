<?php

declare(strict_types=1);

namespace app\back\modules\checks\groups;

use app\back\components\Form;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\CheckGroup;
use app\back\repositories\CheckGroups;

class ChecksGroupsModifyForm
{
    use Form;

    #[IntValidator]
    public int $id;

    #[StringValidator(1, 200, false)]
    public ?string $name = null;

    #[IntInArrayValidator(CheckGroup::DEPARTMENTS)]
    public ?int $department_id = null;

    #[StringInArrayValidator(['name', 'department_id'], true)]
    public string $type;

    public function __construct(
        private readonly CheckGroups $checkGroups
    ) {
    }

    public function update(): void
    {
        $type = $this->type;

        $entity = $this->checkGroups->findOneOr404(['id' => $this->id]);
        $entity->$type = $this->$type;
        $this->checkGroups->update($entity, [$type]);
    }
}
