<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\repositories\views\UserGamePaymentActivities;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UserActivityScoreRule extends BaseSiteUserRule
{
    public const string RESULT_COL_SCORE = 'score';

    #[IntValidator]
    public int $score;
    #[StringInArrayValidator(self::OPERATORS)]
    public string $scoreOperator = self::OPERATOR_GREATER;

    public function getParamsSignatureParts(): array
    {
        return ['score', 'scoreOperator'];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(3, 'score', 'Activity score', [
                    'operators' => Arr::assocToIdName(self::OPERATORS),
                    'operatorPostfix' => 'Operator',
                ]),
            ],
        ];
    }

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $inBuilder->setMap([
            "u.site_id" => 'intval',
            "u.user_id" => 'intval',
        ]);
        $queryParams = [];
        return (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => $this->buildResultExpression($db, $queryParams),
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('u'),
                static::RESULT_COL_SCORE => 'uas.score',
            ])
            ->from($inBuilder->table('u', ['site_id', 'user_id']))
            ->leftJoin(['uas' => UserGamePaymentActivities::TABLE_NAME], 'uas.site_id = u.site_id AND uas.user_id = u.user_id')
            ->addParams($queryParams);
    }

    private function buildResultExpression(ConnectionInterface $db, &$queryParams): string
    {
        $filter = "(COALESCE(uas.score, 0) {$this->scoreOperator} {$this->score})::bool";
        return "CASE WHEN uas.score IS NOT NULL THEN ({$filter}) ELSE NULL END";
    }

    protected function decorateResultRow(int|string $pk, array &$row): void
    {
        if ($row[static::RESULT_COL_RESULT] === null) {
            return;
        }

        $score = $row[static::RESULT_COL_SCORE];
        unset($row[static::RESULT_COL_SCORE]);
        $row[self::RESULT_COL_INFO] = "the activity score is $score";
    }
}
