<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\s2p;

use app\back\components\services\FileStorage;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\entities\UserDocument;
use app\back\modules\api\components\Operators;
use app\back\modules\api\params\ApiParamS2pProjectName;
use app\back\repositories\S2pProjects;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Query\Query;

class UsersDocumentsMethod extends S2pGetMethod
{
    use ApiParamS2pProjectName;

    #[BigIdValidator]
    #[Operators(Operators::EQ_IN)]
    protected array $user_id = [];

    #[DateTimeValidator]
    #[Operators(Operators::COMPARISONS)]
    protected array $created_at = [];

    public function __construct(private readonly FileStorage $fileStorage)
    {
    }

    public function run(): iterable
    {
        $request = $this->createRequest();

        $request->map([
            'project_name' => 'p.name',
            'user_id' => 'ud.user_id',
            'created_at' => 'ud.created_at',
        ], true);

        $query = (new Query($this->db))
            ->select([
                'project_name' => 'p.name',
                'site_id' => 'ud.site_id',
                'user_id' => 'ud.user_id',
                'document_id' => 'ud.id',
                'created_at' => 'ud.created_at',
                'filename' => 'ud.filename',
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->innerJoin(['p' => S2pProjects::TABLE_NAME], 'p.site_id = ud.site_id');

        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure|bool
    {
        return function ($row) {
            $storagePath = UserDocument::composeStoragePath($row['site_id'], $row['user_id'], $row['filename']);
            $row['url'] = $this->fileStorage->getPublicUrlByKey($storagePath);
            unset($row['site_id'], $row['filename']);
            return $row;
        };
    }
}
