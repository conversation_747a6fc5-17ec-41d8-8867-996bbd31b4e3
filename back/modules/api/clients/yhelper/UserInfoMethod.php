<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\yhelper;

use app\back\components\Initializable;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\RequiredWhenAnyEmptyValidator;
use app\back\components\validators\RequiredWhenAnyNotEmptyValidator;
use app\back\components\validators\SiteIdUserIdValidator;
use app\back\entities\User;
use app\back\entities\UserDocument;
use app\back\entities\UserMetric;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\modules\api\ApiGetMethod;
use app\back\modules\api\components\Operators;
use app\back\repositories\Brands;
use app\back\repositories\Cities;
use app\back\repositories\Countries;
use app\back\repositories\LoyaltyStatuses;
use app\back\repositories\Refcodes;
use app\back\repositories\SocialNets;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserKycs;
use app\back\repositories\UserLoyalties;
use app\back\repositories\UserMetrics;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Query\Query;

class UserInfoMethod extends ApiGetMethod
{
    #[Operators(Operators::EQ)]
    #[RequiredWhenAnyNotEmptyValidator('user_id')]
    #[IdValidator]
    protected array $site_id = [];

    #[Operators(Operators::EQ_IN)]
    #[BigIdValidator]
    protected array $user_id = [];

    #[Operators(Operators::IN)]
    #[RequiredWhenAnyEmptyValidator(['site_id', 'user_id'])]
    #[SiteIdUserIdValidator]
    protected array $site_user = [];

    #[Operators(Operators::EQ_IN)]
    #[IdValidator]
    protected array $personal_manager = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IntInArrayValidator([User::class, 'actualStatuses'])]
    protected array $status = [];

    #[Operators(Operators::EQ_IN_NOT_IN)]
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    protected array $active_status = [];

    private readonly UserWallets $userWalletsRepo;

    #[Initializable]
    final public function init(UserWallets $userWalletsRepo): void
    {
        $this->userWalletsRepo = $userWalletsRepo;
    }

    public function run(): iterable
    {
        $request = $this->createRequest();

        $request->map([
            'user_id' => 'u.user_id',
            'site_id' => 'u.site_id',
            'personal_manager' => 'u.personal_manager',
            'status' => 'u.status',
            'active_status' => 'u.active_status',
        ]);

        [$pmFirstContactAtMetricId, $pmFirstContactAt] = UserMetric::M_PM_FIRST_CONTACT_AT;
        [$scoreMetricId, $countScores, $sumScores] = UserMetric::M_YH_SCORE;

        $genderExpression = Users::genderMfExpression($this->db, 'u');

        $query = (new Query($this->db))
            ->select([
                'site_id' => 'u.site_id',
                'user_id' => 'u.user_id',
                'date' => 'u.date',
                'cid' => 'u.cid',
                'login' => 'u.login',
                'email' => 'u.email',
                'email_confirm' => 'u.email_confirm',
                'email_confirmed_at' => 'u.email_confirmed_at',
                'phone' => 'u.phone',
                'phone_confirm' => 'u.phone_confirm',
                'city' => 'ct.name',
                'currency' => 'uw.currency',
                'balance' => 'uw.balance_usd',
                'balance_orig' => 'uw.balance_orig',
                'balance_eur' => 'uw.balance_eur',
                'dep_last_at' => 'usi.dep_last_at',
                'dep_sum' => 'usi.dep_lt_orig',
                'wd_sum' => 'usi.wd_lt_orig',
                'wd_process_sum' => 'wd_p.wd_process_sum',
                'status' => 'u.status',
                'active_status' => 'u.active_status',
                'is_rm' => 'u.is_rm',
                'full_status' => User::getFullStatusExpression(),
                'personal_manager' => 'u.personal_manager',
                'refcode_id' => 'u.refcode_id',
                'timezone' => 'usi.timezone',
                'last_login' => 'usi.login_last_at',
                'country_code' => 'u.country',
                'country' => 'c.name',
                'locale' => 'u.locale',
                'social_name' => 's.social_name',
                'level_name' => 'ls.title',
                'exchange_rate' => '(null)',
                'name' => "COALESCE(usi.name, u.name)",
                'date_first_contact' => 'usi.date_first_contact',
                'gender' => "COALESCE(usi.gender::text, $genderExpression)",
                'address' => 'COALESCE(usi.address, u.address)',
                'birthday' => 'COALESCE(usi.birthday, u.birthday)',
                'no_call' => 'usi.no_call',
                'no_write' => 'usi.no_write',
                'comment' => 'usi.comment',
                's2p_status' => '(null)',
                'status_id' => 'ul.status_id',
                'next_status_id' => '(null)',
                'total_points' => 'ul.total_points',
                'current_points' => 'ul.current_points',
                'required_points' => '(ul.total_points - ul.current_points)',
                'last_comment' => 'usi.comment_last_at',
                'refcode' => 'r.code',
                'is_in_bonus_bl' => 'u.bbl_status',
                'verification_status' => 'kyc.kyc_status',
                'verification_status_updated_at' => 'kyc.status_updated_at',
                'is_toxic' => 'u.is_toxic',
                'brand_id' => 'u.brand_id',
                'brand' => 'b.name',
                'pm_first_contact_at' => "um_fc.$pmFirstContactAt",
                'is_unverified_docs' => 'ud.is_unverified_docs',
                'player_score' => "ROUND(COALESCE(um_score.$sumScores, " . UserMetric::YH_SCORE_MARKS_SUM_START . ")/COALESCE(um_score.$countScores, " . UserMetric::YH_SCORE_MARKS_COUNT_START . "), 1) ",
                'player_score_count' => "COALESCE(um_score.$countScores, " . UserMetric::YH_SCORE_MARKS_COUNT_START . ")",
                'self_excluded' => '(usi.self_excluded_at IS NOT NULL)',
            ])
            ->from(['u' => Users::TABLE_NAME]);

        $request->filterSiteUser($this->db, $query, 'u'); // INNER JOIN must be first for performance

        $newDocumentsQuery = (new Query($this->db))
            ->select(['is_unverified_docs' => '(true)'])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->where(['AND',
                'ud.site_id = u.site_id',
                'ud.user_id = u.user_id',
                ['ud.status' => UserDocument::STATUS_UNVERIFIED],
            ])
            ->limit(1);

        $wdProcessSumQuery = (new Query($this->db))
            ->select([
                'wd_process_sum' => 'COALESCE(SUM(us.amount_orig), 0)',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->where([
                'AND',
                'us.site_id = u.site_id',
                'us.user_id = u.user_id',
                ['us.ext_type' => UserTransaction::EXT_TYPE_NORMAL],
                ['us.status' => [UserTransaction::STATUS_NEW, UserTransaction::STATUS_IN_PROCESS]],
                ['us.dir' => UserTransaction::DIR_OUT],
            ]);

        $walletSubQuery = $this->userWalletsRepo->walletBalancesSubQuery('u', UserWallet::TYPE_REAL);
        $query
            ->leftJoin(['ul' => UserLoyalties::TABLE_NAME], 'ul.site_id = u.site_id AND ul.user_id = u.user_id')
            ->leftJoin(['c' => Countries::TABLE_NAME], 'c.code_2 = u.country')
            ->leftJoin(['s' => SocialNets::TABLE_NAME], 's.social_id = u.social_id')
            ->leftJoin(['ls' => LoyaltyStatuses::TABLE_NAME], 'ls.site_id = ul.site_id AND ls.id = ul.status_id')
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id')
            ->leftJoin(['um_fc' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_fc', 'u', $pmFirstContactAtMetricId))
            ->leftJoin(['kyc' => UserKycs::TABLE_NAME], 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id')
            ->leftJoin(['um_score' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_score', 'u', $scoreMetricId))
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id')
            ->leftJoin(['b' => Brands::TABLE_NAME], 'b.id = u.brand_id')
            ->leftJoin(['ct' => Cities::TABLE_NAME], 'ct.id = u.city_id')
            ->join("LEFT JOIN LATERAL", ['uw' => $walletSubQuery], 'true')
            ->join('LEFT JOIN LATERAL', ['ud' => $newDocumentsQuery], 'true')
            ->join('LEFT JOIN LATERAL', ['wd_p' => $wdProcessSumQuery], 'true');

        $request->filterParams($query);

        return $this->fetchEach($query);
    }

    public function decorator(): \Closure
    {
        return static function ($row) {
            $row['is_in_bonus_bl'] = User::getBonusBlackListStatus($row['is_in_bonus_bl']);

            return $row;
        };
    }
}
