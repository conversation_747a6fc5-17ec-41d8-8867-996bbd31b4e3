<?php

declare(strict_types=1);

namespace app\back\modules\user\tickets;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Db;
use app\back\components\InBuilder;
use app\back\components\Permission;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\AllowedSitesValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UserIdOrSiteUserIdMultilineValidator;
use app\back\entities\User;
use app\back\entities\UserTicket;
use app\back\repositories\Sites;
use app\back\repositories\Users;
use app\back\repositories\UserTickets;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class TicketsFilterForm
{
    use RichTable;

    #[AllowedSitesValidator]
    public ?array $siteId = [];
    #[UserIdOrSiteUserIdMultilineValidator]
    public ?string $siteUser = null;
    #[IntArrayValidator(User::STATUSES)]
    public array $userStatus = [];
    #[StringValidator]
    public ?string $jiraId = null;
    #[DateTimeValidator]
    public ?string $from = null;
    #[DateTimeValidator]
    public ?string $to = null;
    #[IntArrayValidator(UserTicket::STATUSES)]
    public array $status = [];

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly BaseAuthAccess $auth,
        private readonly SecondaryConnection $db,
        private readonly Sites $sitesRepo,
        private readonly SiteUserBuilder $siteUserBuilder,
    ) {
        $this->from = date('Y-m-d', strtotime('-14 day'));
        $this->sort = 'updatedAt-';
        $this->pageSize = 100;
    }

    protected function blocks(): array
    {
        return [[
            $this->selectSiteCell(1, 'siteId'),
            $this->textAreaCell(2, 'siteUser', 'User / Site-User', ['submitOnCtrlEnter' => true]),
            $this->selectCell(1, 'userStatus', 'User status', [
                'list' => array_reverse(Arr::assocToIdName(User::STATUSES)),
                'multiple' => true,
            ]),
            $this->selectCell(2, 'status', 'Ticket status', [
                'list' => Arr::assocToIdName(UserTicket::STATUSES),
                'multiple' => true,
            ]),
            $this->textInputCell(1, 'jiraId', 'Jira ID'),
            $this->dateTimeCell(2, 'from', 'Updated >='),
            $this->dateTimeCell(2, 'to', 'Updated <', [
                'buttonsMode' => 'end',
                'placeholder' => 'now',
            ]),
            $this->submitCell(1, 'Search'),
        ]];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Updated', 'code' => 'updatedAt', 'sortable' => true, 'sortExpr' => 'ut.updated_at'],
            ['name' => 'Status', 'code' => 'status', 'sortable' => true],
            ['name' => 'Site user', 'code' => 'siteUser', 'slotName' => 'siteUser'],
            ['name' => 'User status', 'code' => 'user_status'],
            ['name' => 'Product id', 'code ' => 'product_ticket_id'],
            ['name' => 'Jira key', 'code' => 'jira_key', 'sortable' => true],
            ['name' => 'Id', 'code' => 'id', 'slotName' => 'id'],
        ];
    }

    protected function total(): int
    {
        $query = $this->baseQuery()->select('COUNT(*)');
        $this->applyFilters($query);
        return (int) $query->scalar();
    }

    protected function data(): array
    {
        $query = $this->baseQuery()
            ->select([
                'ut.id',
                'ut.product_ticket_id',
                'ut.jira_key',
                'ut.site_id',
                'ut.user_id',
                'ut.status',
                'ut.updated_at',
                'user_status' => new Expression(Db::buildCaseMapCondition($this->db, 'u.status', User::STATUSES)),
            ])
            ->orderBy($this->getOrderMap())
            ->offset($this->getOffset())
            ->limit($this->getLimit());

        $result = $this->applyFilters($query)->all();

        foreach ($result as &$row) {
            $row['allowApproveButtons'] = ($this->auth->can(Permission::PERM_JIRA_APPROVE_LOST_DEPOSIT) && UserTicket::isApproveDeclineAllowed($row['status']));
            $row['siteUser'] = $this->siteUserBuilder->siteUserToValue($row['site_id'], $row['user_id']);
            $row['status'] = UserTicket::STATUSES[$row['status']];
            $row['updatedAt'] = (new \DateTimeImmutable($row['updated_at']))->format(DateHelper::DATETIME_WITHOUT_SECONDS);
        }

        return $result;
    }

    protected function applyFilters(Query $query): Query
    {
        if ($this->siteUser !== null) {
            $siteUsers = UserIdOrSiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this);
            (new InBuilder($this->db, $siteUsers, ['site_id' => 'intval', 'user_id' => 'intval']))->filterInnerJoin($query, 'ut');
        }

        $to = $this->to ? DateHelper::nextDay($this->to) : null;

        return $query
            ->andFilterWhere(['ut.site_id' => $this->siteId])
            ->andFilterWhere(['ut.status' => $this->status])
            ->andFilterWhere(['ut.jira_key' => $this->jiraId])
            ->andFilterWhere(['>=', 'ut.updated_at', $this->from])
            ->andFilterWhere(['<', 'ut.updated_at', $to])
            ->andFilterWhere(['u.status' => $this->userStatus]);
    }

    private function baseQuery(): Query
    {
        return (new Query($this->db))
            ->from(['ut' => UserTickets::TABLE_NAME])
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ut.site_id AND u.user_id = ut.user_id');
    }
}
