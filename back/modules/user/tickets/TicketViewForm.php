<?php

declare(strict_types=1);

namespace app\back\modules\user\tickets;

use app\back\components\BaseAuthAccess;
use app\back\components\Form;
use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\Permission;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\IdValidator;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\User;
use app\back\entities\UserTicket;
use app\back\entities\UserTicketFile;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\CanonicalPaySystems;
use app\back\repositories\PaySystems;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketFiles;
use app\back\repositories\UserTicketLogs;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class TicketViewForm
{
    use Form;

    #[IdValidator]
    public int $ticketId;

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly BaseAuthAccess $auth,
        private readonly UserTickets $userTicketsRepo,
        private readonly UserTicketFiles $userTicketsFilesRepo,
        private readonly Users $usersRepo,
        private readonly SiteUserBuilder $siteUserBuilder,
        private readonly FileStorage $storage,
    ) {
    }

    public function response(): array
    {
        /** @var UserTicket $ticket */
        $ticket = $this->userTicketsRepo->findOneOr404(['id' => $this->ticketId]);
        return [
            'userUrl' => User::playerHref($ticket->site_id, $ticket->user_id, 'tickets'),
            'jiraUrl' => $ticket->jira_key ? UserTicket::jiraUrl($ticket->jira_key) : null,
            'allowApproveButtons' => ($this->auth->can(Permission::PERM_JIRA_APPROVE_LOST_DEPOSIT) && UserTicket::isApproveDeclineAllowed($ticket->status)),
            'details' => $this->transactionData($ticket),
            'history' => $this->ticketHistory(),
            'files' => $this->ticketFiles(),
        ];
    }

    private function getUserStatus(int $siteId, int $userId): false|string
    {
        return (new Query($this->db))
            ->select(['status' => new Expression(Db::buildCaseMapCondition($this->db, 'u.status', User::STATUSES))])
            ->from(['u' => Users::TABLE_NAME])
            ->where(['u.site_id' => $siteId, 'u.user_id' => $userId])
            ->scalar();
    }

    private function transactionData(UserTicket $ticket): array
    {
        $details = (new Query($this->db))
            ->select([
                'Amount in trans' => 'us.amount_orig',
                'Pay sys' => 'ps.name',
                'Canonical pay sys' => 'cp.name',
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(['ps' => PaySystems::TABLE_NAME], 'ps.id = us.pay_sys_id')
            ->leftJoin(['cps' => CanonicalPaySySources::TABLE_NAME], 'cps.name = ps.name AND cps.source = :source', ['source' => CanonicalPaySySource::SOURCE_PRODUCT])
            ->leftJoin(['cp' => CanonicalPaySystems::TABLE_NAME], 'cp.id = cps.canonical_pay_sys_id')
            ->where([
                'us.site_id' => $ticket->site_id,
                'us.user_id' => $ticket->user_id,
                'us.transaction_id' => $ticket->invoice_id,
            ])->one() ?? [];

        $allDetails = array_merge(
            [
                'Id' => $ticket->id,
                'Site-User' => $this->siteUserBuilder->siteUserToValue($ticket->site_id, $ticket->user_id),
                'User status' => $this->getUserStatus($ticket->site_id, $ticket->user_id),
                'Jira key' => $ticket->jira_key,
            ],
            $details,
            [
                'Invoice id' => $ticket->invoice_id,
                'Performer' => $ticket->performer ?? 'not set',
                'Amount from user' => $ticket->amount_from_user,
            ]
        );

        $result = [];
        foreach ($allDetails as $label => $value) {
            $item = ['label' => $label, 'value' => $value];

            if ($label === 'Site-User') {
                $item['routerUrl'] = User::playerHref($ticket->site_id, $ticket->user_id, 'tickets');
            }

            if ($label === 'Jira key' && $ticket->jira_key) {
                $item['hrefUrl'] = UserTicket::jiraUrl($ticket->jira_key);
            }

            $result[] = $item;
        }

        return $result;
    }

    private function ticketHistory(): array
    {
        $columns = [
            ['name' => 'Status', 'code' => 'status'],
            ['name' => 'Changed at', 'code' => 'created_at'],
            ['name' => 'Changed by', 'code' => 'created_by', 'sortable' => 'true'],
            ['name' => 'Source', 'code' => 'source'],
        ];

        $result = (new Query($this->db))
            ->select([
                'utl.status',
                'utl.source',
                'utl.created_by',
                'utl.created_at',
            ])
            ->from(['utl' => UserTicketLogs::TABLE_NAME])
            ->where(['ticket_id' => $this->ticketId])
            ->orderBy(['utl.created_at' => SORT_DESC])
            ->all();

        foreach ($result as &$row) {
            $row['status'] = UserTicket::STATUSES[$row['status']];
            $row['source'] = UserTicket::SOURCES[$row['source']];
            $row['created_by'] = Str::emailToLdap($row['created_by']);
        }

        return [
            'columns' => $columns,
            'data' => $result,
        ];
    }

    private function ticketFiles(): array
    {
        $result = (new Query($this->db))
            ->select([
                'utf.id',
                'utf.extension',
                'utf.original_name',
                'utf.sync_status',
                'url' => new Expression("NULL"),
            ])
            ->from(['utf' => UserTicketFiles::TABLE_NAME])
            ->where(['utf.ticket_id' => $this->ticketId])
            ->all();

        foreach ($result as &$record) {
            if ($record['sync_status'] === UserTicketFile::SYNC_SUCCESS) {
                $record['url'] = $this->storage->getPublicUrlByKey($this->userTicketsFilesRepo->getStoragePath($record['id'], $record['extension']));
            }
        }
        return $result;
    }
}
