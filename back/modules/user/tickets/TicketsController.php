<?php

declare(strict_types=1);

namespace app\back\modules\user\tickets;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class TicketsController extends WebController
{
    public function actionData(TicketsFilterForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionView(TicketViewForm $form, Request $request): array
    {
        $form->validate($request->json());
        return $form->response();
    }

    public function actionUpdateStatus(TicketApproveForm $form, Request $request): void
    {
        $data = $request->json();
        $form->validateOrException($data);
        $form->process();
        $this->bl()->modify($data);
    }
}
