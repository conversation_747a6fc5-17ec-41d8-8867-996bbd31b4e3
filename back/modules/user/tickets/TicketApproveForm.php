<?php

declare(strict_types=1);

namespace app\back\modules\user\tickets;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\Form;
use app\back\components\helpers\DateHelper;
use app\back\components\Permission;
use app\back\components\services\FileStorage;
use app\back\components\SessionMessages;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\config\tasks\Res;
use app\back\entities\UserTicket;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\repositories\UserTicketFiles;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketLogs;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class TicketApproveForm
{
    use Form;

    #[IdValidator]
    #[CallableValidator([self::class, 'validateTicket'])]
    public int $ticketId;
    #[BooleanValidator]
    public bool $approve;

    private ?UserTicket $ticket;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly BaseAuthAccess $auth,
        private readonly ConnectionInterface $db,
        private readonly UserTickets $userTicketsRepo,
        private readonly UserTicketLogs $userTicketLogsRepo,
        private readonly UserTicketFiles $userTicketFilesRepo,
        private readonly FetchTaskFactory $requestFactory,
        private readonly SessionMessages $sessionMessages,
    ) {
    }

    public function process(): void
    {
        $status = $this->approve ? UserTicket::STATUS_OPEN : UserTicket::STATUS_DECLINED;
        if ($status === UserTicket::STATUS_OPEN && $this->ticket->jira_key === null) {
            $this->createJiraIssueFromTicket();
        }
        $this->updatedTicketStatus($status, !empty($this->ticket->jira_key));
        $this->sessionMessages->success('Ticket status changed');
    }

    private function createJiraIssueFromTicket(): void
    {
        $jiraTask = $this->requestFactory->createFetchTask('jira-ticket-create', Res::JIRA, [
            'ticket' => $this->ticket,
        ]);

        $this->ticket->jira_key = $jiraTask->finalData()[0]['jira_key'] ?? null;

        $this->userTicketsRepo->update($this->ticket, ['jira_key']);

        $files = $this->getFilesToAttach();

        if (count($files) === 0) {
            return;
        }

        $jiraTask = $this->requestFactory->createFetchTask('jira-ticket-attach-file', Res::JIRA, [
            'jiraKey' => $this->ticket->jira_key,
            'files' => $files,
        ]);

        $toUpdate = iterator_to_array($jiraTask->finalData());

        if (count($toUpdate) !== count($files)) {
            $this->sessionMessages->error("Some files weren't uploaded");
        }

        $this->userTicketFilesRepo->batchUpdateDistinct($toUpdate, [], ['id']);
    }

    private function updatedTicketStatus(int $status, bool $updateOnJira): void
    {
        if ($updateOnJira) {
            $jiraTask = $this->requestFactory->createFetchTask('jira-ticket-update-status', Res::JIRA, [
                'jiraKey' => $this->ticket->jira_key,
                'analyticStatus' => $status,
            ]);
            $jiraTask->finalData();
        }

        $this->ticket->status = $status;
        $this->ticket->source = UserTicket::SOURCE_ANALYTIC;
        $this->ticket->updated_at = new \DateTimeImmutable(UserTicket::SQL_NOW_DATETIME);
        $this->ticket->upserted_at = new \DateTimeImmutable(UserTicket::SQL_NOW_DATETIME);

        $this->userTicketsRepo->update($this->ticket, ['status', 'source', 'updated_at', 'upserted_at']);

        $log = $this->userTicketLogsRepo->validateAndCreate([
            'ticket_id' => $this->ticket->id,
            'status' => $this->ticket->status,
            'source' => UserTicket::SOURCE_ANALYTIC,
            'created_by' => $this->auth->employee()->email,
            'created_at' => date(DateHelper::DATETIME_FORMAT_PHP),
        ]);

        $this->userTicketLogsRepo->insert($log);
    }

    private function getFilesToAttach(): array
    {
        return (new Query($this->db))
            ->select([
                'utf.id',
                'utf.extension',
                'utf.original_name',
            ])
            ->from(['utf' => UserTicketFiles::TABLE_NAME])
            ->where([
                'ticket_id' => $this->ticket->id,
                'jira_file_id' => null
            ])
            ->all();
    }

    public static function validateTicket(int $id, self $form): ?string
    {
        if (!$form->auth->can(Permission::PERM_JIRA_APPROVE_LOST_DEPOSIT)) {
            return 'not enough permission to approve ticket';
        }

        /** @var UserTicket $ticket */
        $ticket = $form->userTicketsRepo->findOne(['id' => $id]);

        if ($ticket === null) {
            return "ticket with id $id not found";
        }

        if (!array_key_exists($ticket->site_id, $form->allowedLists->sites())) {
            return "site {$ticket->site_id} is not allowed";
        }

        if (!UserTicket::isApproveDeclineAllowed($ticket->status)) {
            return 'ticket is not eligible to status change';
        }

        $form->ticket = $ticket;

        return null;
    }
}
