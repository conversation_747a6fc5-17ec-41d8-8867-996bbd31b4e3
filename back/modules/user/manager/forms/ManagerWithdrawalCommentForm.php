<?php

declare(strict_types=1);

namespace app\back\modules\user\manager\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\SiteUserIdMultilineValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\User;
use app\back\entities\WithdrawalComment;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;
use app\back\repositories\WithdrawalComments;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class ManagerWithdrawalCommentForm
{
    use FormGrid;

    public const string ACTION_ADD = 'Add';
    public const string ACTION_SHOW = 'Show';
    public const string ACTION_REMOVE = 'Remove';

    public const array COMMENT_ACTIONS = [
        self::ACTION_ADD => ['id' => self::ACTION_ADD, 'name' => self::ACTION_ADD, 'style' => 'success'],
        self::ACTION_SHOW => ['id' => self::ACTION_SHOW, 'name' => self::ACTION_SHOW, 'style' => 'secondary'],
        self::ACTION_REMOVE => ['id' => self::ACTION_REMOVE, 'name' => self::ACTION_REMOVE, 'style' => 'danger', 'confirm' => 'Are you sure to remove this comment?'],
    ];

    #[StringInArrayValidator(self::COMMENT_ACTIONS)]
    public ?string $type;

    #[SiteUserIdMultilineValidator]
    public string $siteUser;

    #[StringValidator(5, 1000)]
    #[CallableValidator([self::class, 'validateIsRequired'])]
    public ?string $comment = null;

    #[IdValidator]
    public ?int $id = null;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Users $usersRepo,
        private readonly UserTransactions $userTransactionsRepo,
        public readonly AllowedLists $allowedLists,
        private readonly SiteUserBuilder $siteUserBuilder,
        private readonly WithdrawalComments $wdCommentsRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textAreaCell(4, 'siteUser', 'Site-User ID'),
                $this->textAreaCell(4, 'comment', 'Comment'),
                $this->multipleSubmit(4, 'type', ['list' => array_values(self::COMMENT_ACTIONS), 'hint' => 'Add comment, Show comments, Remove all comments']),
            ],
        ];
    }

    public function show(): ?array
    {
        $siteUsers = SiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this->allowedLists->sites(true), true);

        $result = (new Query($this->db))
            ->select([
                'comments' => "STRING_AGG(comment, '\n')",
                'cnt' => "COUNT(comment)",
            ])
            ->from(['wc' => WithdrawalComments::TABLE_NAME])
            ->where([
                'AND',
                ['IN', ['site_id', 'user_id'], $siteUsers],
                ['type' => WithdrawalComment::TYPE_USER],
                ['source' => WithdrawalComment::SOURCE_ANALYTICS],
                ['>', 'created_at', $this->timeLimit()],
            ])
            ->one();

        if ($result) {
            $this->comment = $result['comments'];
        }

        return $result;
    }

    public function remove(): bool
    {
        $siteUsers = SiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this->allowedLists->sites(true));

        foreach ($siteUsers as $siteUserArray) {
            $siteUser = array_combine(['site_id', 'user_id'], $siteUserArray);
            /** @var User $user */
            $user = $this->usersRepo->findOne($siteUser);

            if ($user === null) {
                continue;
            }

            $findConditions = ['AND',
                [
                    'user_id' => $siteUser['user_id'],
                    'site_id' => $siteUser['site_id'],
                ],
                ['type' => WithdrawalComment::TYPE_USER],
                ['source' => WithdrawalComment::SOURCE_ANALYTICS],
                ['>', 'created_at', $this->timeLimit()],
                ['ILIKE', 'comment', $this->comment]
            ];

            $wdComments = Arr::fromIterable($this->wdCommentsRepo->findEach($findConditions));

            if (empty($wdComments)) {
                return false;
            }

            foreach ($wdComments as $wdComment) {
                if ($wdComment === null) {
                    continue;
                }

                $this->wdCommentsRepo->delete($wdComment);
            }
        }

        return true;
    }

    public function save(): bool
    {
        $unProcessed = [];
        $siteUsers = SiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this->allowedLists->sites(true));
        foreach ($siteUsers as $siteUserArray) {
            $siteUser = array_combine(['site_id', 'user_id'], $siteUserArray);
            /** @var User $user */
            $user = $this->usersRepo->findOne($siteUser);

            if ($user === null) {
                $unProcessed[] = $siteUserArray;
                continue;
            }

            if (mb_strlen($this->comment) > 1000) {
                $unProcessed[] = $siteUserArray;
                continue;
            }

            if (!$this->wdCommentsRepo->saveComment($this->auth, $siteUser['site_id'], $siteUser['user_id'], $this->comment)) {
                $unProcessed[] = $siteUserArray;
            }
        }

        if (!empty($unProcessed)) {
            $this->siteUser = implode("\n", array_map(fn($siteUser) => $this->siteUserBuilder->siteUserToValue(...$siteUser), $unProcessed));
            return false;
        }

        return true;
    }

    public static function validateIsRequired(?string $value, self $form): ?string
    {
        if (empty($value) && $form->type !== self::ACTION_SHOW) {
            return $form->type === self::ACTION_REMOVE ? 'Comment must be shown before removal' : 'Is required';
        }

        return null;
    }

    private function timeLimit(): string
    {
        return (new \DateTimeImmutable('-1 hour'))->format('Y-m-d H:i:s');
    }
}
