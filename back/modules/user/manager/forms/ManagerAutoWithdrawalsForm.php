<?php

declare(strict_types=1);

namespace app\back\modules\user\manager\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\SiteUserIdMultilineValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\User;
use app\back\entities\UserMetric;
use app\back\repositories\UserMetrics;
use app\back\repositories\Users;
use app\back\repositories\WithdrawalComments;
use Yiisoft\Db\Expression\Expression;

class ManagerAutoWithdrawalsForm
{
    use FormGrid;

    public const string ACTION_DISABLE = 'Disabled';
    public const string ACTION_ENABLE = 'Enabled';

    public const array AUTO_WITHDRAWALS_ACTIONS = [
        self::ACTION_DISABLE => ['id' => self::ACTION_DISABLE, 'name' => self::ACTION_DISABLE, 'style' => 'danger', 'confirm' => 'Are you sure you want to disable automatic withdrawals for these users?'],
        self::ACTION_ENABLE => ['id' => self::ACTION_ENABLE, 'name' => self::ACTION_ENABLE, 'style' => 'success', 'confirm' => 'Are you sure you want to enable automatic withdrawals for these users?'],
    ];

    #[StringInArrayValidator(self::AUTO_WITHDRAWALS_ACTIONS)]
    public ?string $type;

    #[SiteUserIdMultilineValidator]
    public string $siteUser;

    #[StringValidator(1, 1000)]
    public string $comment;

    public function __construct(
        private readonly Users $usersRepo,
        private readonly UserMetrics $userMetricsRepo,
        public readonly AllowedLists $allowedLists,
        private readonly SiteUserBuilder $siteUserBuilder,
        private readonly WithdrawalComments $wdCommentsRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textAreaCell(4, 'siteUser', 'Site-User ID'),
                $this->textAreaCell(4, 'comment', 'Comment'),
                $this->multipleSubmit(4, 'type', ['list' => array_values(self::AUTO_WITHDRAWALS_ACTIONS), 'hint' => 'Disable/Enable auto withdrawals']),
            ],
        ];
    }

    public function process(bool $isDisable): int
    {
        [$metricId, $metricColumn] = UserMetric::M_AUTO_WITHDRAWALS_DISABLE;

        $rows = [];
        $siteUsers = SiteUserIdMultilineValidator::explodeToSiteUserArrays($this->siteUser, $this->allowedLists->sites(true));
        foreach ($siteUsers as [$site_id, $user_id]) {
            $rows[] = [
                'site_id' => $site_id,
                'user_id' => $user_id,
                'metric' => $metricId,
                $metricColumn => 1,
            ];
            $this->createComment($site_id, $user_id);
        }

        if ($isDisable) {
            $affected = $this->userMetricsRepo->batchUpsert($rows, ['updated_at' => new Expression('now()')]);
        } else {
            $affected = $this->userMetricsRepo->deleteAll(['IN', array_keys($rows[0]), $rows]);
        }

        return $affected;
    }

    public function createComment(int $siteId, int $userId): void
    {
        /** @var User $user */
        $user = $this->usersRepo->findOneOr404([
            'user_id' => $userId,
            'site_id' => $siteId
        ]);

        $this->wdCommentsRepo->saveComment($this->auth, $user->site_id, $user->user_id, $this->comment);
    }
}
