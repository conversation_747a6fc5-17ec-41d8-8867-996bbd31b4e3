<?php

declare(strict_types=1);

namespace app\back\modules\user\card;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\WebController;

#[AccessCheckPage]
class CardController extends WebController
{
    public function actionForm(CardForm $form): array
    {
        return $form->response();
    }

    public function actionLoad(CardForm $form, Request $request): array
    {
        $form->validateOrException($request->json());

        return $form->getData();
    }
}
