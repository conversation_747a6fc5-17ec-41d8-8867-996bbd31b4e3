<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\tickets;

use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\Initializable;
use app\back\components\Permission;
use app\back\components\RichTable;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\entities\UserTransaction;
use app\back\entities\UserTicket;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\UserTickets;
use Yiisoft\Db\Query\Query;

class TicketsFilterForm extends BasePlayerForm
{
    use RichTable;

    #[DateValidator]
    public ?string $from = null;
    #[DateValidator]
    public ?string $to = null;
    #[IntArrayValidator(UserTransaction::STATUSES)]
    public array $status = [];

    #[Initializable]
    final public function init(): void
    {
        $this->sort = 'updated_at-';
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateCell(2, 'from', 'Updated from'),
                $this->dateCell(2, 'to', 'Updated to', [
                    'buttonsMode' => 'end',
                    'placeholder' => 'now',
                ]),
                $this->selectCell(2, 'status', 'Status', [
                    'list' => Arr::assocToIdName(UserTicket::STATUSES),
                    'multiple' => true,
                ]),
            ],
        ];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Id', 'code' => 'ticketId', 'slotName' => 'ticketId'],
            ['name' => 'Updated', 'code' => 'updated_at', 'sortable' => true],
            ['name' => 'Status', 'code' => 'status', 'sortable' => true],
            ['name' => 'Product Id', 'code' => 'product_ticket_id'],
            ['name' => 'Jira Id', 'code' => 'jira_key', 'slotName' => 'jira'],
            ['name' => 'Invoice Id', 'code' => 'invoice_id'],
            ['name' => 'User sum', 'code' => 'amount_from_user'],
            ['name' => 'Approve', 'slotName' => 'approve'],
        ];
    }

    protected function data(): array
    {
        $query =  (new Query($this->db))
            ->select([
                'ut.id',
                'ut.product_ticket_id',
                'ut.jira_key',
                'ut.status',
                'ut.type',
                'ut.invoice_id',
                'ut.amount_from_user',
                'ut.performer',
                'ut.updated_at',
            ])
            ->from(['ut' => UserTickets::TABLE_NAME])
            ->where([
                'ut.site_id' => $this->siteId,
                'ut.user_id' => $this->userId,
            ])
            ->orderBy($this->getOrderMap())
            ->offset($this->getOffset())
            ->limit($this->getLimit());

        $result = $this->applyFilters($query)->all();

        foreach ($result as &$row) {
            $row['allowApproveButtons'] = ($this->auth->can(Permission::PERM_JIRA_APPROVE_LOST_DEPOSIT) && UserTicket::isApproveDeclineAllowed($row['status']));

            $row['jira_url'] = isset($row['jira_key']) ? UserTicket::jiraUrl($row['jira_key']) : null;

            $row['status'] = UserTicket::STATUSES[$row['status']];
        }

        return $result;
    }

    protected function applyFilters(Query $query): Query
    {
        $to = $this->to ? DateHelper::nextDay($this->to) : null;

        return $query
            ->andFilterWhere(['ut.status' => $this->status])
            ->andFilterWhere(['>=', 'ut.updated_at', $this->from])
            ->andFilterWhere(['<', 'ut.updated_at', $to]);
    }
}
