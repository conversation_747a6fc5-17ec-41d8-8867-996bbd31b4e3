<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\tickets;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\modules\user\player\blocks\BasePlayerBlockController;
use app\back\modules\user\tickets\TicketCreateForm;

#[AccessCheckPage]
class TicketsController extends BasePlayerBlockController
{
    public function actionAddForm(TicketCreateForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json(), ['siteId']);
    }

    public function actionAdd(TicketCreateForm $form, Request $request, SessionMessages $messages): void
    {
        $form->validateOrException($request->formRequestData());
        $form->create();
        $messages->success('Ticket is created');
        $this->bl()->create($form->params());
    }
}
