<?php

declare(strict_types=1);

namespace app\back\modules\user\player\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\NotFoundException;
use app\back\components\Form;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\components\kyc\KycDetailsRequestModel;
use app\back\components\MaskGenerator;
use app\back\components\SecondaryConnection;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BigIdValidator;
use app\back\entities\Employee;
use app\back\entities\Operator;
use app\back\entities\Refcode;
use app\back\entities\SocialNet;
use app\back\entities\User;
use app\back\entities\UserBettingProfitSegment;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserIgnoreId;
use app\back\entities\UserKyc;
use app\back\entities\UserLogin;
use app\back\entities\UserMetric;
use app\back\entities\UserTransaction;
use app\back\entities\UserWallet;
use app\back\entities\WithdrawalComment;
use app\back\modules\reports\reports\CidUsers\CidUsersConfig;
use app\back\modules\user\player\FlagsList;
use app\back\repositories\Brands;
use app\back\repositories\Employees;
use app\back\repositories\Games;
use app\back\repositories\Operators;
use app\back\repositories\PaySystems;
use app\back\repositories\Refcodes;
use app\back\repositories\S2pAntifraudLogs;
use app\back\repositories\Sites;
use app\back\repositories\SocialNets;
use app\back\repositories\UserBettingProfitSegments;
use app\back\repositories\UserDocumentProgresses;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\UserKycs;
use app\back\repositories\UserLogin4plays;
use app\back\repositories\UserLoyalties;
use app\back\repositories\UserLoyaltyDeposits;
use app\back\repositories\UserMetrics;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use app\back\repositories\UserWallets;
use app\back\repositories\WithdrawalComments;
use Yiisoft\Db\Query\Query;

class InfoForm
{
    use Form;

    private const int MIN_OUT_IMPATIENCE_PERCENT_FOR_SHOW = 20;

    #[AllowedSiteValidator]
    public int $siteId;
    #[BigIdValidator]
    public int $userId;

    private array $groups = [
        [
            ['name' => 'Site', 'key' => 'siteName'],
            ['name' => 'Brand', 'key' => 'brandName'],
            ['name' => 'User ID', 'key' => 'userId'],
            ['name' => 'Status', 'key' => 'fullStatusName'],
            ['name' => 'CRM Group', 'key' => 'crmGroup'],
            ['name' => 'PM first contact', 'key' => 'pmFirstContact'],
            ['name' => 'Flags', 'key' => 'flags', 'raw' => true],
            ['name' => 'Loyalty status', 'key' => 'loyalty_status'],
            ['name' => 'Loyalty deposit status', 'key' => 'loyalty_deposit_status'],
            ['name' => 'Pers. manager', 'key' => 'personalManager'],
            ['name' => 'Bet prof segment', 'key' => 'profitSegment'],
            ['name' => 'Timeline', 'key' => 'timeline', 'raw' => true],
            ['name' => 'CID', 'key' => 'cid', 'handler' => 'cid'],
            ['name' => 'KYC', 'key' => 'kycStatus'],
        ],
        [
            ['name' => 'Reg method', 'key' => 'regMethod'],
            ['name' => 'Reg date', 'key' => 'regDate'],
            ['name' => 'Country / Locale', 'key' => 'countryLocale'],
            ['name' => 'Refcode', 'key' => 'refcode'],
            ['name' => 'Email', 'key' => 'email'],
            ['name' => 'Phone', 'key' => 'phone'],
        ],
        [
            ['name' => 'Name', 'key' => 'name'],
            ['name' => 'Birthday', 'key' => 'birthday'],
            ['name' => 'Location', 'key' => 'location'],
            ['name' => 'Profile', 'key' => 'profile'],
            ['name' => 'Is ludoman at', 'key' => 'isLudomanAt'],
        ],
        [
            ['name' => 'Dep sum (LT)', 'key' => 'sumIn'],
            ['name' => 'Wd sum (LT)', 'key' => 'sumOut'],
            ['name' => 'Wd sum (process)', 'key' => 'sumOutProcess'],
            ['name' => 'Win back sum (LT)', 'key' => 'sumWinBack'],
            ['name' => 'Cashback', 'key' => 'cashback'],
            ['name' => 'Stash', 'key' => 'stash'],
            ['name' => 'Auto withdrawals', 'key' => 'autoWithdrawals'],
        ],
        [
            ['name' => 'Real balance', 'key' => 'balanceReal'],
            ['name' => 'Real wager sum', 'key' => 'refundSumReal'],
            ['name' => 'Real bets sum', 'key' => 'betSumReal'],
        ],
        [
            ['name' => 'Bonus balance', 'key' => 'balanceBonus'],
            ['name' => 'Bonus wager sum', 'key' => 'refundSumBonus'],
            ['name' => 'Bonus bets sum', 'key' => 'betSumBonus'],
            ['name' => 'Bonus bets left sum', 'key' => 'betLeftSumBonus'],
        ],
        [
            ['name' => 'Fav. game (week)', 'key' => 'favoriteGame7'],
            ['name' => 'Fav. game (30d)', 'key' => 'favoriteGame30'],
            ['name' => 'Fav. game (LT)', 'key' => 'favoriteGame'],
        ],
        [
            ['name' => 'Reg to FD (days)', 'key' => 'fdAfterReg'],
            ['name' => 'Last dep (days)', 'key' => 'depDaysAgo'],
            ['name' => 'Last login (days)', 'key' => 'loginDaysAgo'],
            ['name' => 'FD sum ($)', 'key' => 'fdSumUsd'],
            ['name' => 'FD pay system', 'key' => 'fdPs'],
        ],
        [
            ['name' => 'Pay success ratio', 'key' => 'paySuccessRatio'],
            ['name' => 'Pay bonus ratio', 'key' => 'payBonusRatio'],
            ['name' => 'Fav. pay system', 'key' => 'favoritePs'],
            ['name' => 'Play ratio', 'key' => 'playRatio'],
        ],
    ];

    public function __construct(
        private readonly MaskGenerator $maskGenerator,
        private readonly SecondaryConnection $db,
        private readonly BaseAuthAccess $auth,
        public readonly AllowedLists $allowedLists,
        // Repos:
        private readonly Sites $sitesRepo,
        private readonly Brands $brandsRepo,
        private readonly UserLoyaltyDeposits $userLoyaltyDepositsRepo,
        private readonly UserLoyalties $userLoyaltiesRepo,
        private readonly Operators $operatorsRepo,
        private readonly UserIgnoreIds $userIgnoreIdsRepo,
        private readonly Refcodes $refcodesRepo,
        private readonly UserWallets $userWalletsRepo,
        private readonly SocialNets $socialNetsRepo,
        private readonly PaySystems $paySystemsRepo,
        private readonly Games $gamesRepo,
        private readonly UserBettingProfitSegments $userBettingProfitSegmentsRepo,
        private readonly KycDetailsRequestModel $kycDetailsRequestModel,
        private readonly UserTransactions $userTransactionsRepo,
    ) {
    }

    public function groups(): array
    {
        return $this->groups;
    }

    public function data(): ?array
    {
        $siteUser = [
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
        ];

        $user = (new Query($this->db))
            ->select([
                '*', // TODO: specify explicit
                'fullStatusName' => User::getFullStatusExpression(),
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->where($siteUser)
            ->one();

        if ($user === null) {
            throw new NotFoundException('User not found');
        }

        $siteName = $this->sitesRepo->getNameById($user['site_id']);
        $brandName = $user['brand_id'] !== null ? $this->brandsRepo->getNameById($user['brand_id']) : null;

        $result = [
            'siteId' => $user['site_id'],
            'userId' => $user['user_id'],
            'siteName' => $siteName,
            'brandName' => $brandName === $siteName ? null : $brandName,
            'regDate' => $user['date'] === null ? null : date('Y-m-d', strtotime($user['date'])),
            'countryLocale' => "{$user['country']} / {$user['locale']}",
            'cid' => $user['cid'],
            'cidLink' => $user['cid'] ? CidUsersConfig::cidUsersCloudHref($user['cid'], ['site_id', 'user_id', 'reg_date', 'full_status', 'last_login', 'in_out']) : '',
            'email' => $user['email'],
            'emailConfirmed' => $user['email_confirm'],
            'phone' => $user['phone'],
            'phoneConfirmed' => $user['phone_confirm'],
            'status' => $user['status'],
            'activeStatus' => $user['active_status'],
            'isManual' => $user['is_manual_status'],
            'pmId' => $user['personal_manager'],
            'fullStatusName' => $user['fullStatusName'],
            'statusUpdatedAt' => $user['status_updated_at'],
        ];

        if (!$this->auth->canViewEmails()) {
            $result['email'] = $this->maskGenerator->maskEmail($user['email']);
            $result['login'] = $this->maskGenerator->maskLogin($user['login']);
        }

        if (!$this->auth->canViewPhones()) {
            $result['phone'] = $this->maskGenerator->maskPhone($user['phone']);
        }

        $result['loyalty_deposit_status'] = $this->userLoyaltyDepositsRepo->getStatus($siteUser);
        if ($result['loyalty_deposit_status'] === null) {
            $result['loyalty_status'] = $this->userLoyaltiesRepo->getLoyaltyStatus($siteUser);
        }

        /** @var Operator $operator */
        $operator = $this->operatorsRepo->findOne(['id' => $user['personal_manager']]);
        $operatorActive = $operator->status ?? false;
        $result['personalManager'] = [
            'text' => Str::emailToLdap($operator->email ?? null) ?? Operator::NO_MANAGER_NAME,
            'style' => $operator && !$operatorActive ? ['text-decoration' => 'line-through'] : (object)[],
        ];

        /** @var UserIgnoreId $ignored */
        $ignored = $this->userIgnoreIdsRepo->findOne($siteUser);

        $flags = new FlagsList('Casual user');
        $flags->add('Ignored', $ignored !== null, 'bg-danger');
        $flags->add('Blocked', $user['is_blocked'], 'bg-danger');

        [
            'sum_in' => $result['sumIn'],
            'sum_out' => $result['sumOut'],
            'sum_out_process' => $result['sumOutProcess'],
            'sum_win_back' => $result['sumWinBack'],
        ] = $this->getInOut($siteUser);

        /** @var Refcode $refcode */
        $refcode = $this->refcodesRepo->findOne(['id' => $user['refcode_id']]);
        $result['refcode'] = $refcode?->code;

        $result = array_merge($result, $this->getSocialInfo($user['social_id'], $user['social_key']));

        $bl = (new Query($this->db))
            ->select(['COUNT(*)'])
            ->from(['log' => S2pAntifraudLogs::TABLE_NAME])
            ->where([
                'AND',
                ['log.site_id' => $this->siteId],
                ['log.user_id' => $this->userId],
            ])
            ->limit(1)
            ->scalar();

        $flags->add('Was in BL', (bool)$bl, 'bg-warning');

        $flags->add('In bonus BL', $user['bbl_status'] !== User::BONUS_BL_STATUS_NO, 'bg-warning', User::getBonusBlackListStatus($user['bbl_status']));

        if (!empty($result['cid'])) {
            $result['cidCount'] = ' (' . (new Query($this->db))
                ->select(['COUNT(*)'])
                ->from(['u' => Users::TABLE_NAME])
                ->where(['cid' => $user['cid']])
                ->scalar() . ' accounts)';
        }

        $result = [
            ...$result,
            ...$this->getWalletInfo(UserWallet::TYPE_REAL),
            ...$this->getWalletInfo(UserWallet::TYPE_BONUS)
        ];

        $specialInfo = $this->getSpecialInfo();

        $flags->add("CB {$specialInfo['chargebackProbability']}%", $specialInfo['chargebackProbability'] >= UserMetric::MIN_CHARGEBACK_PERCENT_FOR_SHOW, 'bg-danger', "Chargeback Probability: {$specialInfo['chargebackProbability']}%");
        $flags->add("VIP {$specialInfo['vipProbability']}%", $specialInfo['vipProbability'] > UserMetric::MIN_VIP_PERCENT_FOR_SHOW, 'bg-warning', "VIP Probability: {$specialInfo['vipProbability']}%");

        $impatience = $this->userTransactionsRepo->getOutImpatiencePercent($this->siteId, $this->userId);
        $flags->add("Impatient", $impatience >= self::MIN_OUT_IMPATIENCE_PERCENT_FOR_SHOW, 'bg-secondary', "User lost balance while active withdrawal: $impatience%");

        $result = array_merge($result, Arr::leaveOnlyKeys($specialInfo, [
            'birthday',
            'name',
            'location',
            'kycStatus',
            'kycStatusUpdatedAt',
            'crmGroup',
            'isLudomanAt',
            'pmFirstContact',
            'autoWithdrawals',
        ]));
        $result['autoWithdrawals'] = $result['autoWithdrawals'] === null ? 'Enabled' : 'Disabled';

        if ($this->auth->canViewStatData()) {
            $result = array_merge($result, Arr::leaveOnlyKeys($specialInfo, [
                'regMethod',
                'favoriteGame',
                'favoriteGame7',
                'favoriteGame30',
                'depDaysAgo',
                'loginDaysAgo',
                'fdAfterReg',
                'fdSumUsd',
                'fdPs',
                'paySuccessRatio',
                'payBonusRatio',
                'favoritePs',
                'playRatio',
            ]));
        }

        $commentsCnt = $this->getCommentsCount($this->siteId, $this->userId);
        $flags->add($commentsCnt . ' comments', (bool)$commentsCnt, 'bg-secondary');

        $result['email'] = $this->confirmedAnswer($result['email'], $result['emailConfirmed']);
        $result['phone'] = $this->confirmedAnswer($result['phone'], $result['phoneConfirmed']);
        $result['flags'] = $flags->htmlString();

        $result['profitSegment'] = $this->getDataProfitSegment($this->siteId, $this->userId);

        return $result;
    }

    private function confirmedAnswer(?string $text, ?bool $confirmed = null): ?array
    {
        if (empty($text)) {
            return null;
        }

        return [
            'before' => $text,
            'class' => 'badge ' . ($confirmed ? 'bg-success' : 'bg-secondary'),
            'text' => $confirmed ? 'conf.' : 'unconf.',
        ];
    }

    private function getInOut(array $siteUser): array
    {
        $lifetimeStats = (new Query($this->db))
            ->select([
                'sum_in' =>  'COALESCE(SUM(ut.amount_orig) FILTER (WHERE ut.op_id = :in AND ut.status = :success), 0)',
                'sum_out' => 'COALESCE(SUM(ut.amount_orig) FILTER (WHERE ut.op_id = :out AND ut.status = :success), 0)',
                'sum_out_process' => 'COALESCE(SUM(ut.amount_orig) FILTER (WHERE ut.op_id = :out AND ut.status IN(:new, :process)), 0)',
                'sum_win_back' =>  'COALESCE(SUM(ut.amount_orig) FILTER (WHERE ut.op_id IN (:bs_win_back, :bonus_transfer) AND ut.status = :success), 0)',
                'currency' => "STRING_AGG(DISTINCT ut.currency, '|') FILTER (WHERE ut.op_id IN (:in, :out) AND ut.status = :success)",
            ])
            ->from(['ut' => UserTransactions::TABLE_NAME])
            ->addParams([
                'in' => UserTransaction::OP_IN,
                'out' => UserTransaction::OP_OUT,
                'bs_win_back' => UserTransaction::OP_BS_WIN_BACK,
                'bonus_transfer' => UserTransaction::OP_BONUS_TRANSFER,
                'success' => UserTransaction::STATUS_SUCCESS,
                'new' => UserTransaction::STATUS_NEW,
                'process' => UserTransaction::STATUS_IN_PROCESS,
            ])
            ->where($siteUser)
            ->one();

        return [
            'sum_in' => $this->amount($lifetimeStats['sum_in'], $lifetimeStats['currency'], true, 0),
            'sum_out' => $this->amount($lifetimeStats['sum_out'], $lifetimeStats['currency'], true, 0),
            'sum_out_process' => $this->amount($lifetimeStats['sum_out_process'], $lifetimeStats['currency'], false, 0),
            'sum_win_back' => $this->amount($lifetimeStats['sum_win_back'], $lifetimeStats['currency'], false, 0),
        ];
    }

    private function getSocialInfo(?int $socialId, ?string $socialKey): array
    {
        $result = [];

        if ($socialId !== null && !empty($socialKey)) {
            $social = (new Query($this->db))
                ->select([
                    'first_name',
                    'last_name',
                    'country',
                    'city',
                    'birthday',
                    'social_key',
                    'profile',
                ])
                ->from(UserLogin4plays::TABLE_NAME)
                ->where([
                    'social_id' => $socialId,
                    'social_key' => $socialKey,
                ])
                ->one();

            if (!empty($social)) {
                $result['name'] = trim($social['first_name'] . ' ' . $social['last_name']);
                $result['location'] = trim($social['country'] . ', ' . $social['city'], ', ');

                if (!empty($social['birthday'])) {
                    $result['birthday'] = $social['birthday'];
                }

                if ($this->auth->canViewSocialProfiles()) {
                    $socName = $this->socialNetsRepo->getNameById($socialId);
                    $result['socialKey'] = $social['social_key'];
                    $result['socialNetIcon'] = SocialNet::ICONS[$socName] ?? SocialNet::ICON_DEFAULT;
                    $result['socialProfileUrl'] = $social['profile'];
                }
            }
        }

        return $result;
    }

    private function getSpecialInfo(): array
    {
        [$favGameLt, $favGameLtColumn] = UserMetric::M_FAV_GAME_LT_1;
        [$favGame7, $favGame7Column] = UserMetric::M_FAV_GAME_7_1;
        [$favGame30, $favGame30Column] = UserMetric::M_FAV_GAME_30_1;
        [$pmFirstContact, $pmFirstContactColumn] = UserMetric::M_PM_FIRST_CONTACT_AT;
        [$autoWithdrawals, $autoWithdrawalsColumn] = UserMetric::M_AUTO_WITHDRAWALS_DISABLE;
        [$chargeback, $chargebackColumnExp] = UserMetric::percentMetricWithExpression('um_cbp', UserMetric::M_CHARGEBACK_PROBABILITY_PER_MILLE);
        [$vipProbability, $vipProbabilityColumnExp] = UserMetric::percentMetricWithExpression('um_vip', UserMetric::M_VIP_PROBABILITY_PER_MILLE);

        $result = (new Query($this->db))
            ->select([
                'birthday' => 'COALESCE(u.birthday, usi.birthday)',
                'name' => 'usi.name',
                'location' => 'usi.address',
                'regMethod' => "u.registration_method",
                'favoriteGame' => "um_fg.$favGameLtColumn",
                'favoriteGame7' => "um_fg_7.$favGame7Column",
                'favoriteGame30' => "um_fg_30.$favGame30Column",
                'depDaysAgo' => 'now()::DATE - usi.dep_last_at::DATE',
                'loginDaysAgo' => 'now()::DATE - usi.login_last_at::DATE',
                'fdAfterReg' => '(usi.dep_first_at::DATE - u.date::DATE)',
                'fdSumUsd' => 'us.amount_usd',
                'fdPs' => 'us.pay_sys_id',
                'paySuccessRatio' => 'usi.pay_success_ratio_lt',
                'payBonusRatio' => 'usi.pay_bonus_ratio_lt',
                'favoritePs' => 'usi.favorite_pay_sys_id',
                'playRatio' => 'usi.play_ratio_lt',
                'kycStatus' => 'kyc.kyc_status',
                'kycStatusUpdatedAt' => 'kyc.status_updated_at',
                'crmGroup' => 'usi.crm_group',
                'isLudomanAt' => 'usi.is_ludoman_at',
                'pmFirstContact' => "(um_pmfc.$pmFirstContactColumn)::date",
                'autoWithdrawals' => "um_wdaa.$autoWithdrawalsColumn",
                'chargebackProbability' => "ROUND($chargebackColumnExp)",
                'vipProbability' => "ROUND($vipProbabilityColumnExp)",
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id and usi.user_id = u.user_id')
            ->leftJoin(['um_fg' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_fg', 'u', $favGameLt))
            ->leftJoin(['um_fg_7' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_fg_7', 'u', $favGame7))
            ->leftJoin(['um_fg_30' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_fg_30', 'u', $favGame30))
            ->leftJoin(['kyc' => UserKycs::TABLE_NAME], 'kyc.site_id = u.site_id and kyc.user_id = u.user_id')
            ->leftJoin(['um_pmfc' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_pmfc', 'u', $pmFirstContact))
            ->leftJoin(['um_wdaa' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_wdaa', 'u', $autoWithdrawals))
            ->leftJoin(['um_cbp' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_cbp', 'u', $chargeback))
            ->leftJoin(['um_vip' => UserMetrics::TABLE_NAME], UserMetrics::metricJoinCondition('um_vip', 'u', $vipProbability))
            ->leftJoin(['us' => UserTransactions::TABLE_NAME], [
                'AND',
                'us.site_id = u.site_id',
                'us.user_id = u.user_id',
                [
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                    'us.op_id' => UserTransaction::OP_IN,
                    'us.is_first_success' => true,
                ],
            ])
            ->where([
                'AND',
                ['u.site_id' => $this->siteId],
                ['u.user_id' => $this->userId],
            ])
            ->one();

        if ($result === null) {
            return [];
        }

        foreach (['fdPs', 'favoritePs'] as $psKey) {
            if (empty($result[$psKey])) {
                continue;
            }

            $result[$psKey] = $this->paySystemsRepo->getNameById($result[$psKey]);
        }

        foreach (['favoriteGame', 'favoriteGame7', 'favoriteGame30'] as $gameKey) {
            if (empty($result[$gameKey])) {
                continue;
            }

            $result[$gameKey] = $this->gamesRepo->getNameById($result[$gameKey]);
        }

        $ratioFields = [
            'paySuccessRatio',
            'payBonusRatio',
            'playRatio',
        ];

        foreach ($ratioFields as $field) {
            if (empty($result[$field])) {
                continue;
            }

            $result[$field] = $result[$field] * 100 . '%';
        }

        if (isset($result['kycStatus'])) {
            $result['kycStatus'] = [
                'class' => 'btn-' . match ($result['kycStatus']) {
                        UserKyc::KYC_VERIFIED => 'success',
                        UserKyc::KYC_FAILED => 'danger',
                        default => 'secondary',
                },
                'text' => UserKyc::getKycStatusById($result['kycStatus']),
                'details' => $this->getKycDetails($result['kycStatusUpdatedAt']),
            ];
        }

        if (!empty($result['regMethod'])) {
            $result['regMethod'] = UserLogin::getMethodNameById($result['regMethod']);
        }

        if (!empty($result['birthday'])) {
            $age = \DateTime::createFromFormat('Y-m-d', $result['birthday'])->diff(new \DateTime('now'))->y;
            $result['birthday'] .= ' (' . Str::pluralizeByCount($age, 'year') . ')';
        }

        return $result;
    }

    private function getKycDetails(string $modified): array
    {
        $details = ["Modified: $modified"];

        $row = (new Query($this->db))
            ->select([
                'e.kyc_department',
                'request_details' => 'CASE WHEN k.kyc_status IN (:kyc_status_request, :kyc_status_request_repeat) THEN t.request_details END',
                't.transactions_data',
            ])
            ->from(['k' => UserDocumentProgresses::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'k.created_by = e.employee_id')
            ->join('LEFT JOIN LATERAL', ['t' => $this->kycDetailsRequestModel->progressRequestDetailsForJoinQuery('k')], 'TRUE')
            ->where([
                'k.site_id' => $this->siteId,
                'k.user_id' => $this->userId,
                'k.action' => UserDocumentProgress::ACTION_STATUS_CHANGE,
            ])
            ->andWhere(['NOT IN', 'k.kyc_status', [UserKyc::KYC_WAIT, UserKyc::KYC_WAIT_WITHOUT_REQUEST]])
            ->orderBy([
                'k.created_at' => SORT_DESC,
                'k.id' => SORT_DESC,
            ])
            ->limit(1)
            ->addParams([
                ':kyc_status_request' => UserKyc::KYC_REQUEST,
                ':kyc_status_request_repeat' => UserKyc::KYC_REQUEST_REPEAT,
            ])
            ->one();

        if ($row === null) {
            return $details;
        }

        if (isset($row['kyc_department'])) {
            $details[] = "Department: " . (Employee::KYC_DEPARTMENTS[$row['kyc_department']] ?? $row['kyc_department']);
        }

        $kycDetails = $this->kycDetailsRequestModel->detailsDecorateDocsRequest($row, false);
        if (!empty($kycDetails)) {
            array_unshift($kycDetails, '');
        }
        return [ ...$details, ...$kycDetails];
    }

    private function amount(null|float|int|string $amount, ?string $currency, $showZero = false, int $decimals = 2): string
    {
        if ((float)$amount < 0.001) {
            $amount = 0;
        }

        return (!$showZero && empty($amount)) ? '' : number_format((float) $amount, $decimals, '.', ' ') . ' ' . $currency;
    }

    private function getDataProfitSegment(int $siteId, int $userId): ?string
    {
        $data = (new Query($this->db))
            ->select(['segment_id'])
            ->from(UserBettingProfitSegments::TABLE_NAME)
            ->where([
                'site_id' => $siteId,
                'user_id' => $userId,
            ])
            ->one();

        if ($data === null) {
            return null;
        }

        return UserBettingProfitSegment::SEGMENTS[$data['segment_id']] ?? null;
    }

    private function getCommentsCount(int $siteId, int $userId): int
    {
        return (new Query($this->db))
            ->select('COUNT(*)')
            ->from(WithdrawalComments::TABLE_NAME)
            ->where([
                'site_id' => $siteId,
                'user_id' => $userId,
                'type' => WithdrawalComment::TYPE_USER,
            ])
            ->scalar();
    }

    private function getWalletInfo(int $type): array
    {
        $wallet = (new Query($this->db))
            ->select([
                'wagering_target_amount' => 'SUM(wagering_target_amount)',
                'wagering_current_amount' => 'SUM(wagering_current_amount)',
                'balance' => 'SUM(balance)',
                'currency' => "STRING_AGG(DISTINCT currency, ',')",
            ])
            ->from(UserWallets::TABLE_NAME)
            ->where([
                'AND', [
                    'site_id' => $this->siteId,
                    'user_id' => $this->userId,
                    'type' => $type,
                ],
                UserWallets::getActiveCondition(),
            ])
            ->one();

        if ($wallet['balance'] !== null) {
            $postfix = ucfirst(UserWallet::TYPES[$type]);
            $result["balance$postfix"] = $this->amount($wallet['balance'], $wallet['currency'], true);
            $result["refundSum$postfix"] = $this->amount($wallet['wagering_target_amount'], $wallet['currency'], true);
            $result["betSum$postfix"] = $this->amount($wallet['wagering_current_amount'], $wallet['currency'], true);
            if ($type === UserWallet::TYPE_BONUS) {
                $result["betLeftSum$postfix"] = $this->amount($wallet['wagering_target_amount'] - $wallet['wagering_current_amount'], $wallet['currency'], true);
            }
        }

        return $result ?? [];
    }
}
