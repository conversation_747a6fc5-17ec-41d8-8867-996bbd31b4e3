<?php

declare(strict_types=1);

namespace app\back\modules\finance\documentsKyc;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Db;
use app\back\components\helpers\Str;
use app\back\components\kyc\KycDetailsRequestModel;
use app\back\components\kyc\KycDetailsWaitModel;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\SessionMessages;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\AllowedSitesValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\RequiredWhenAllEmptyValidator;
use app\back\components\validators\SiteUserIdMultilineValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\entities\Employee;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\repositories\Employees;
use app\back\repositories\UserDocumentProgresses;
use app\back\repositories\UserKycs;
use Yiisoft\Db\Query\Query;

class DocumentsKycTable
{
    use RichTable;

    private const int ROWS_LIMIT = 1000;

    #[RequiredWhenAllEmptyValidator('siteUser')]
    #[AllowedSitesValidator(true)]
    public ?array $siteId = null;
    #[SiteUserIdMultilineValidator]
    public ?string $siteUser = null;
    #[IntArrayValidator(UserKyc::KYC_STATUSES)]
    public array $status = [];
    #[IntArrayValidator(Employee::KYC_DEPARTMENTS)]
    public array $department = [];
    #[StringMultilineValidator(0, 150)]
    public ?string $editByEmployee = null;
    #[StringMultilineValidator(0, 150)]
    public ?string $requestByEmployee = null;
    #[DateValidator]
    public string $dateFrom;
    #[DateValidator]
    public ?string $dateTo = null;

    private readonly SiteUserBuilder $siteUserBuilder;

    public function __construct(
        public AllowedLists $allowedLists,
        private readonly SecondaryConnection $db,
        private readonly BaseAuthAccess $authAccess,
        private readonly SessionMessages $sessionMessages,
        private readonly KycDetailsWaitModel $kycDetailsWaitModel,
        private readonly KycDetailsRequestModel $kycDetailsRequestModel,
    ) {
        $this->dateFrom ??= DateHelper::weekAgo();
        if (empty($this->department) && $this->authAccess->employee()->kyc_department) {
            $this->department = [$this->authAccess->employee()->kyc_department];
        }
        $this->sort = 'createdAt-';

        $this->siteUserBuilder = new SiteUserBuilder($db);
    }

    protected function data(): array
    {
        $rows = $this->dataQuery()->all();

        if (count($rows) === self::ROWS_LIMIT) {
            $this->sessionMessages->warning('Result too big and limited to ' . self::ROWS_LIMIT . ' rows!');
        }

        return array_map(fn(array $row) => [
            'siteId' => $row['site_id'],
            'userId' => $row['user_id'],
            'siteUser' => $this->siteUserBuilder->siteUserToValue($row['site_id'], (int)$row['user_id']),
            'createdAt' => $row['createdAt'],
            'kycStatus' => UserKyc::KYC_STATUSES[$row['kyc_status']] ?? $row['kyc_status'],
            'requestCreatedBy' => Str::emailToLdap($row['requestCreatedBy']),
            'requestDepartment' => $this->departmentName($row['request_source'], $row['requestDepartment']),
            'requestDetails' => $this->kycDetailsRequestModel->detailsDecorateDocsRequest($row, true),
            'waitDetails' => $this->kycDetailsWaitModel->detailsDecorateDocsWait($row),
        ], $rows);
    }

    private function dataQuery(): Query
    {
        $query = (new Query($this->db))
            ->select([
                'kyc.site_id',
                'kyc.user_id',
                'kyc.kyc_status',
                'createdAt' => 'kyc.status_updated_at',
                'request_source' => 'pr.source',
                'requestCreatedBy' => 'e.email',
                'requestDepartment' => 'e.kyc_department',
                'wd.wait_details',
                'rd.request_details',
                'rd.transactions_data',
            ])
            ->from(['kyc' => UserKycs::TABLE_NAME])

            ->leftJoin(['pr' => UserDocumentProgresses::TABLE_NAME], 'pr.id = kyc.last_request_progress_id')
            ->join('LEFT JOIN LATERAL', ['rd' => $this->kycDetailsRequestModel->progressRequestDetailsForJoinQuery('pr')], 'TRUE')
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = pr.created_by')

            ->leftJoin(['pw' => UserDocumentProgresses::TABLE_NAME], 'pw.id = kyc.last_wait_progress_id')
            ->join('LEFT JOIN LATERAL', ['wd' => $this->kycDetailsWaitModel->progressWaitDetailsForJoinQuery('pw')], 'TRUE')

            ->andFilterWhere([
                'AND',
                ['IN', 'kyc.site_id', $this->siteId],
                ['IN', 'kyc.kyc_status', $this->status],
                ['>=', 'kyc.status_updated_at', $this->dateFrom],
                ['<', 'kyc.status_updated_at', $this->dateTo ? DateHelper::nextDay($this->dateTo) : null],
                ['~*', 'e.email', !empty($this->requestByEmployee) ? Db::prepareDbEnumRegex(Str::explodeText($this->requestByEmployee)) : null],
            ])
            ->orderBy($this->getOrderMap())
            ->limit(self::ROWS_LIMIT);

        if (!empty($this->siteUser)) {
            $this->siteUserBuilder
                ->build($this->siteUser, 'kyc.site_id', 'kyc.user_id')
                ->filterInnerJoin($query);
        }

        if (!empty($this->department)) {
            $conditions = ['e.kyc_department' => $this->department];
            if (in_array(Employee::KYC_DEPARTMENT_S2P_RISK, $this->department, true)) {
                $conditions = ['OR', $conditions, ['pr.source' => UserDocumentProgress::SOURCE_API_S2P]];
            }
            $query->andWhere($conditions);
        }

        if (!empty($this->editByEmployee)) {
            $query
                ->join('INNER JOIN LATERAL', [
                    'ebe' => (new Query($this->db))
                        ->select(['found_progress_id' => 'p.id'])
                        ->from(['p' => UserDocumentProgresses::TABLE_NAME])
                        ->innerJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = p.created_by')
                        ->where('p.site_id = kyc.site_id AND p.user_id = kyc.user_id')
                        ->andWhere(['~*', 'e.email', Db::prepareDbEnumRegex(Str::explodeText($this->editByEmployee))])
                        ->limit(1)
                ], 'found_progress_id IS NOT NULL');
        }

        return $query;
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Site-User ID', 'code' => 'siteUser'],
            ['name' => 'Updated at', 'code' => 'createdAt', 'sortable' => true],
            ['name' => 'Department', 'code' => 'requestDepartment', 'sortable' => true],
            ['name' => 'Request by', 'code' => 'requestCreatedBy', 'sortable' => true],
            ['name' => 'Status', 'code' => 'kycStatus', 'sortable' => true],
            ['name' => 'Links', 'slotName' => 'links'],
            ['name' => 'Last request', 'slotName' => 'requestDetails'],
            ['name' => 'Last wait', 'slotName' => 'waitDetails'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateCell(2, 'dateFrom', 'Date from'),
                $this->dateCell(2, 'dateTo', 'Date to'),
                $this->selectSiteCell(2, 'siteId'),
                $this->textAreaCell(2, 'siteUser', 'Site-User ID'),
                $this->textAreaCell(2, 'editByEmployee', 'Edit by employee name'),
                $this->textAreaCell(2, 'requestByEmployee', 'Request by employee name'),
            ],
            [
                $this->listCell(4, 'department', 'Department', [
                    'list' => Arr::assocToIdName(Employee::KYC_DEPARTMENTS),
                ]),
                $this->selectCell(4, 'status', 'KYC status', [
                    'list' => Arr::assocToIdName(UserKyc::KYC_STATUSES),
                ]),
                $this->submitCell(4, 'Search'),
            ],
        ];
    }

    private function departmentName(?int $source, ?int $department): string | int | null
    {
        $department = match ($source) {
            UserDocumentProgress::SOURCE_API_S2P => Employee::KYC_DEPARTMENT_S2P_RISK,
            default => $department,
        };

        return Employee::KYC_DEPARTMENTS[$department] ?? $department;
    }
}
