<?php

declare(strict_types=1);

namespace app\back\modules\finance\users;

use app\back\components\accessCheck\AccessCheckPage;
use app\back\components\Request;
use app\back\components\SessionMessages;
use app\back\components\WebController;

#[AccessCheckPage]
class UsersController extends WebController
{
    public function actionForm(FinanceUsersForm $form): array
    {
        return $form->response();
    }

    public function actionData(FinanceUsersForm $form, Request $request): array
    {
        return $form->validateAndResponse($request->json());
    }

    public function actionTransactions(UserTransactionsForm $form, Request $request): array
    {
        $data = $request->json();
        $this->bl()->get($data);

        return $form->validateAndResponse($data);
    }

    public function actionGetWithdrawComment(UserWithdrawalCommentForm $form, Request $request): array
    {
        $form->validateOrException($request->json());
        $this->bl()->get($request->json());
        $comment = $form->getComment();

        return ['comment' => $comment[0] ?? null, 'updatedBy' => $comment[1] ?? null];
    }

    public function actionGetWithdrawComments(UserWithdrawalCommentsForm $form, Request $request): array
    {
        $data = $request->json();
        $this->bl()->get($data);

        return $form->validateAndResponse($data);
    }

    public function actionCreateWithdrawComment(UserWithdrawalCommentCreateForm $form, SessionMessages $messages, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->createComment();
        $this->bl()->modify($request->json());
        $messages->success('Comment created successfully');
    }

    public function actionDeleteWithdrawComment(UserWithdrawalCommentDeleteForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->delete();
        $this->bl()->modify($request->json());
    }

    public function actionUpdateWithdrawComment(UserWithdrawalCommentUpdateForm $form, Request $request): void
    {
        $form->validateOrException($request->json());
        $form->update();
        $this->bl()->modify($request->json());
    }
}
