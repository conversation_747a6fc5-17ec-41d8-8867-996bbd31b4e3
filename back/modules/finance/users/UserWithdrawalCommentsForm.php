<?php

declare(strict_types=1);

namespace app\back\modules\finance\users;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Str;
use app\back\components\RichTable;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\WithdrawalComment;
use app\back\modules\finance\components\WithdrawalsRestrictionManager;
use app\back\repositories\Employees;
use app\back\repositories\Sites;
use app\back\repositories\WithdrawalComments;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UserWithdrawalCommentsForm
{
    use RichTable;

    #[AllowedSiteValidator]
    public int $siteId;

    #[IntValidator]
    public int $userId;

    public function __construct(
        private readonly ConnectionInterface $db,
        public readonly AllowedLists $allowedLists,
        private readonly Sites $sitesRepo,
        private readonly WithdrawalsRestrictionManager $withdrawalsRestrictionManager,
        private readonly Employees $employeesRepo,
    ) {
        $this->sort = 'updated_at-';
    }

    protected function blocks(): array
    {
        return [
            [$this->slotCell(12, 'createComment', '', ['hideLabel' => true])],
        ];
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Comment', 'code' => 'comment', 'slotName' => 'comment', 'align' => 'start'],
            ['name' => 'Updated at', 'code' => 'updated_at', 'slotName' => 'updatedAt', 'align' => 'start', 'sortable' => true],
            ['name' => 'Updated by', 'code' => 'updated_by', 'slotName' => 'updatedBy', 'align' => 'start'],
        ];
    }

    protected function data(): array
    {
        $result = $this->baseQuery()
            ->select([
                'id' => 'c.id',
                'comment' => 'c.comment',
                'updated_by' => 'c.updated_by_email',
                'updated_at' => 'c.updated_at',
                'is_editable' => "(now() - c.created_at < interval '1 hour')",
            ])
            ->orderBy($this->getOrderMap())
            ->all();

        foreach ($result as &$row) {
            $row['updated_by'] = Str::emailToLdap($row['updated_by']);
        }

        return $result;
    }

    protected function total(): int
    {
        return $this->baseQuery()->count();
    }

    protected function baseQuery(): Query
    {
        return (new Query($this->db))
            ->from(['c' => WithdrawalComments::TABLE_NAME])
            ->where([
                'c.site_id' => $this->siteId,
                'c.user_id' => $this->userId,
                'c.type' => WithdrawalComment::TYPE_USER,
                'c.source' => [WithdrawalComment::SOURCE_ANALYTICS, WithdrawalComment::SOURCE_VIPAFF],
            ]);
    }
}
