<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus\forms;

use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\NestedValidator;
use app\back\components\validators\StringValidator;
use app\back\modules\finance\components\bonus\BonusDictionaryGi;

class BonusGinMoneyForm extends BonusBaseMoneyForm
{
    #[NestedValidator([self::class, 'validateWager'])]
    public int $wager = 40;
    #[StringValidator]
    public ?string $timeToLive = 'P5D';
    #[StringValidator]
    #[CallableValidator([self::class, 'validateScalarAgainstList'], 'balanceGroups')]
    public ?string $balanceGroups = BonusDictionaryGi::BALANCE_GROUP_CASINO;
    #[IntValidator]
    public ?int $featureMaxTransferAmount = null;
    #[IntValidator]
    public ?int $featureBetAmountMin = null;
    #[IntValidator]
    public ?int $featureBetAmountMax = null;
    #[IntValidator]
    public ?int $featureWageringAmountMax = null;

    protected function fieldsConfig(): array
    {
        return [
            [
                ['width' => 6, 'name' => 'timeToLive', 'title' => 'Time To Live'],
                ['width' => 6, 'name' => 'balanceGroups', 'title' => 'Balance group', 'type' => self::INPUT_SELECT],
            ],
            [
                ['width' => 3, 'name' => 'featureMaxTransferAmount', 'title' => 'Max transfer sum'],
                ['width' => 3, 'name' => 'featureBetAmountMin', 'title' => 'Min bet sum'],
                ['width' => 3, 'name' => 'featureBetAmountMax', 'title' => 'Max bet sum'],
                ['width' => 3, 'name' => 'featureWageringAmountMax', 'title' => 'Max wagering sum'],
            ],
        ];
    }
}
