<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus\forms;

use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntValidator;

class BonusSmenTalismanForm extends BonusBaseForm
{
    #[IntValidator(1)]
    #[CallableValidator([self::class, 'validateScalarAgainstList'], 'talismanProduct')]
    public ?int $talismanProduct = null;
    #[IntValidator(1)]
    public int $talismanTime;

    protected function fieldsConfig(): array
    {
        return [[
            ['width' => 6, 'name' => 'talismanProduct', 'title' => 'Talisman'],
            ['width' => 6, 'name' => 'talismanTime', 'title' => 'Talisman time (in days)'],
        ]];
    }
}
