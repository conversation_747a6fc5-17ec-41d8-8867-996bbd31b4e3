<?php

declare(strict_types=1);

namespace app\back\modules\finance\components\bonus\forms;

use app\back\components\validators\IntValidator;

trait WagerAdditionalFieldsTrait
{
    #[IntValidator(1)]
    public int $wagerHours = 720;
    #[IntValidator]
    public ?int $wagerMaxTransferMultiplier = null;

    protected function wagerAdditionalFields(): array
    {
        return [
            ['width' => 6, 'name' => 'wagerHours', 'title' => 'Wager hours'],
            ['width' => 6, 'name' => 'wagerMaxTransferMultiplier', 'title' => 'Max transfer multiplier'],
        ];
    }
}
