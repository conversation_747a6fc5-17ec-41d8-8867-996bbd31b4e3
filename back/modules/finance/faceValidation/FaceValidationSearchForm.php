<?php

declare(strict_types=1);

namespace app\back\modules\finance\faceValidation;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\SiteUserIdValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\repositories\Employees;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Query\Query;

class FaceValidationSearchForm
{
    use FormGrid {
        validate as formGridValidate;
    }

    private const string VALIDATION_OK = 'valid';
    private const string VALIDATION_INVALID = 'invalid';
    private const string VALIDATION_UNKNOWN = 'unknown';
    private const array VALIDATION_STATUSES = [
        self::VALIDATION_OK => 'OK',
        self::VALIDATION_INVALID => 'Invalid',
        self::VALIDATION_UNKNOWN => 'Unknown',
    ];

    #[SiteUserIdValidator]
    public ?string $siteUser = null;
    #[IdValidator]
    public ?int $faceId = null;
    #[IdValidator]
    public ?int $documentId = null;
    #[IntValidator(1, 1000)]
    public int $limit = 100;
    #[MatchValidator('/^\d+(?:\.\d+)?-\d+(?:\.\d+)?$/')]
    public ?string $score = null;
    #[MatchValidator('/^\d+(?:\.\d+)?-\d+(?:\.\d+)?$/')]
    public ?string $ratio = null;
    #[BooleanValidator]
    public ?bool $joinRelated = false;
    #[BooleanValidator]
    public ?bool $haveApproved = null;
    #[StringArrayValidator(self::VALIDATION_STATUSES)]
    public ?array $isValid = null;
    #[BooleanValidator]
    public ?bool $isSecondary = false;
    #[StringValidator]
    #[MatchValidator(DateHelper::RANGE_PATTERN)]
    public ?string $dateCreated;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly SecondaryConnection $db,
        private readonly FileStorage $storage,
        private readonly SiteUserBuilder $siteUserBuilder,
    ) {
    }

    public function validate(array $data): void
    {
        $this->formGridValidate($data);
        if (empty($data)) {
            $this->score = '0.7-1.0';
            $this->ratio = '0.02-1.0';
            $this->isValid = [self::VALIDATION_UNKNOWN];
        }
    }

    public function search(): array
    {
        $query = $this->query();
        $rows = $query->all();

        $docs = [];

        foreach ($rows as $face) {
            $siteUser = $this->siteUserBuilder->siteUserToValue($face['site_id'], $face['user_id']);
            $docs[$face['document_id']] ??= [
                'id' => $face['document_id'],
                'siteUser' => $siteUser,
                'siteId' => $face['site_id'],
                'userId' => $face['user_id'],
                'faces' => [],
            ];
            $docs[$face['document_id']]['faces'][] = [
                'id' => $face['id'],
                'isValid' => $face['is_valid'],
                'similarId' => $face['similar_id'],
                'validatedBy' => Str::emailToLdap($face['validated_by_email']),
                'score' => $face['recognition_score'],
                'ratio' => $face['ratio'],
                'box' => Json::decode($face['box']),
                'image' => $this->storage->getPublicUrlByKey(implode('/', [$face['site_id'], $face['user_id'], $face['filename']])),
                'doc' => [
                    'id' => $face['document_id'],
                    'createdAt' => $face['created_at'],
                    'width' => $face['width'],
                    'height' => $face['height'],
                ],
            ];
        }

        return [
            'form' => $this->response(),
            'data' => array_values($docs),
        ];
    }

    private function query(): Query
    {
        $query = (new Query($this->db))
            ->select([
                'udf.id',
                'document_id' => 'ud.id',
                'ud.site_id',
                'ud.user_id',
                'ud.filename',
                'ud.height',
                'ud.width',
                'box' => 'array_to_json(udf.box)',
                'udf.recognition_score',
                'udf.ratio',
                'udf.created_at',
                'udf.is_valid',
                'udf.similar_id',
                'validated_by_email' => 'e.email',
            ])
            ->innerJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'udf.user_document_id = ud.id')
            ->leftJoin(['e' => Employees::TABLE_NAME], 'udf.validated_by = e.employee_id');

        if ($this->joinRelated) {
            $filterQuery = (new Query($this->db))
                ->select([
                    'id' => 'ud.id',
                    'site_id' => 'ANY_VALUE(ud.site_id)',
                    'user_id' => 'ANY_VALUE(ud.user_id)',
                    'filename' => 'ANY_VALUE(ud.filename)',
                    'height' => 'ANY_VALUE(ud.height)',
                    'width' => 'ANY_VALUE(ud.width)',
                ])
                ->from(['ud' => UserDocumentsActive::TABLE_NAME])
                ->innerJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'udf.user_document_id = ud.id')
                ->groupBy('ud.id')
                ->orderBy(['MIN(udf.recognition_score)' => SORT_ASC]);

            $query->from(['ud' => $filterQuery]);
        } else {
            $filterQuery = $query
                ->from(['ud' => UserDocumentsActive::TABLE_NAME])
                ->orderBy(['udf.recognition_score' => SORT_ASC]);
        }

        $filterQuery
            ->andFilterWhere(['ud.id' => $this->documentId])
            ->andFilterWhere(['udf.similar_id' => $this->faceId])
            ->andFilterWhere(['BETWEEN', 'udf.recognition_score', ...explode('-', $this->score ?: '-')])
            ->andFilterWhere(['BETWEEN', 'udf.ratio', ...explode('-', $this->ratio ?: '-')]);

        if (!empty($this->isValid) && count(array_unique($this->isValid)) !== count(self::VALIDATION_STATUSES)) {
            $statusToCondition = static fn($val) => ['udf.is_valid' => match ($val) {
                self::VALIDATION_OK => true,
                self::VALIDATION_INVALID => false,
                self::VALIDATION_UNKNOWN => null,
            }];
            $filterQuery->andWhere(['OR', ...array_map($statusToCondition, $this->isValid)]);
        }

        if ($this->siteUser) {
            ['siteId' => $siteId, 'userId' => $userId] = $this->siteUserBuilder->valueToSiteUser($this->siteUser);
            $filterQuery->andWhere(['AND', ['ud.site_id' => $siteId], ['ud.user_id' => $userId]]);
        }

        if ($this->haveApproved !== null) {
            $filterQuery
                ->leftJoin(['udfs' => UserDocumentFaceSimilarities::TABLE_NAME], '(udfs.face_a_id = udf.id OR udfs.face_b_id = udf.id) AND udfs.approved IS TRUE')
                ->andWhere(['(udfs.face_a_id IS NOT NULL)' => !$this->haveApproved]);
        }

        if ($this->isSecondary === true) {
            $filterQuery
                ->leftJoin(['udfz' => UserDocumentFaces::TABLE_NAME], 'udf.user_document_id = udfz.user_document_id AND udfz.number = 0')
                ->andWhere(['>=', 'udfz.recognition_score', 0.99])
                ->andWhere(['>', 'udf.number', 0])
                ->andWhere(['<=', 'udf.ratio', UserDocumentFaces::FACE_QUALITY_RATIO_LOW]);
        }

        if (isset($this->dateCreated)) {
            $period = explode(' - ', $this->dateCreated);
            [$from, $to] = [$period[0], $period[1] ?? $period[0]];
            $to = date(DateHelper::DATE_FORMAT_PHP, strtotime($to) + 24 * 3600);
            $filterQuery
                ->andFilterWhere(['>=', 'udf.created_at', $from])
                ->andFilterWhere(['<', 'udf.created_at', $to]);
        }

        $filterQuery->limit($this->limit);

        return $query;
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(1, 'siteUser', 'Site-User'),
                $this->textInputCell(1, 'documentId', 'Document ID'),
                $this->textInputCell(1, 'faceId', 'Face ID'),
                $this->textInputCell(1, 'score', 'Recognition core'),
                $this->textInputCell(1, 'ratio', 'Area ratio'),
                $this->listCell(2, 'isValid', 'Validation', [
                    'list' => Arr::assocToIdName(self::VALIDATION_STATUSES),
                    'multiple' => true,
                ]),
                $this->selectBooleanCell(1, 'joinRelated', 'Join from doc', [
                    'multiple' => false,
                ]),
                $this->selectBooleanCell(1, 'haveApproved', 'Approved faceID', [
                    'multiple' => false,
                ]),
                $this->textInputCell(1, 'limit', 'Limit'),
                $this->submitCell(2, 'Search'),
            ],
            [
                $this->dateRangeCall(2, 'dateCreated', 'Date created'),
                $this->checkboxCell(1, 'isSecondary', 'Secondary face', [
                    'multiple' => false,
                ]),
            ],
        ];
    }
}
