<?php

declare(strict_types=1);

namespace app\back\modules\finance\faceTestTool;

use app\back\components\BaseAuthAccess;
use app\back\config\tasks\Res;
use app\back\entities\UserDocument;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\components\FormGrid;
use app\back\components\helpers\ImageHelper;
use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\FileValidator;
use app\back\components\validators\FloatValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\SiteUserIdValidator;
use app\back\entities\UserDocumentFaceSimilarity;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFacesSecondary;
use app\back\repositories\views\UserDocumentsActive;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class FaceTestToolSearchForm
{
    use FormGrid {
        validate as formValidate;
    }

    private \Imagick $image;
    #[FileValidator(mimeTypes: ['image/jpeg'], maxCount: 1)]
    public mixed $document = null;
    #[SiteUserIdValidator]
    public ?string $siteUser = null;
    #[FloatValidator(0)]
    public float $distanceMin = 0.;
    #[FloatValidator(0)]
    public float $distanceMax = 0.98;
    #[IntValidator]
    public int $limit = 100;
    #[BooleanValidator]
    public ?bool $hqFacesSearch = null;
    #[BooleanValidator]
    public ?bool $filterNotBankCard = null;
    #[BooleanValidator]
    public ?bool $alternativeModel = null;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly FileStorage $storage,
        private readonly BaseAuthAccess $authAccess,
        private readonly SiteUserBuilder $siteUserBuilder,
        private readonly FetchTaskFactory $requestFactory,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(2, 'siteUser', 'Site-User'),
                $this->checkboxCell(1, 'filterNotBankCard', 'Not Bank Card'),
                $this->textInputCell(1, 'distanceMin', 'Distance >='),
                $this->textInputCell(1, 'distanceMax', 'Distance <'),
                $this->textInputCell(1, 'limit', 'Limit'),
                $this->checkboxCell(2, 'alternativeModel', 'Alternative model', [
                    'multiple' => false
                ]),
                [
                    'title' => 'Image',
                    'name' => 'document',
                    'type' => 'file',
                    'multiple' => false,
                    'width' => 2,
                    'hint' => 'Only .jpg or .jpeg file allowed',
                ],
                $this->submitCell(1, 'Search'),
            ],
        ];
    }

    public function validate(array $data): array
    {
        $errors = $this->formValidate($data);
        if (!empty($errors)) {
            return $errors;
        }

        $file = $this->document ?? null;

        if (!$this->document instanceof UploadedFile) {
            $errors['document'] = "Image required";
            return $errors;
        }

        $image = new \Imagick();
        if ($image->readImage($file->getRealPath()) === false) {
            $errors['document'] = "Unable to read file " . Str::cleanString($file->getClientOriginalName());
            return $errors;
        }

        $this->image = $image;

        return $errors;
    }

    public function search(): array
    {
        ImageHelper::clearRotation($this->image);

        $recognitionTime = microtime(true);
        [$taskName, $resource] = $this->alternativeModel ? ['faces-infer-alternative', Res::ML_HUB] : ['faces-infer', Res::FACES];
        $task = $this->requestFactory->createFetchTask($taskName, $resource, [
            'requestDataIterator' => [$this->image->getImageBlob()],
        ]);
        $faces = current(iterator_to_array($task->finalData()));
        $recognitionTime = number_format(microtime(true) - $recognitionTime, 3);

        $searchTime = microtime(true);
        $faces = $this->findSimilarlyInDb($this->image->getImageHeight(), $this->image->getImageWidth(), $faces);
        $searchTime = number_format(microtime(true) - $searchTime, 3);

        return compact('recognitionTime', 'searchTime', 'faces');
    }

    private function findSimilarlyInDb(int $height, int $width, array $faces): array
    {
        $doc = compact('height', 'width');
        return array_map(fn($face) => [
            'doc' => $doc,
            'box' => $face['box'],
            'matches' => array_map(fn($sim) => [
                'site_id' => $sim['site_id'],
                'user_id' => $sim['user_id'],
                'site_user' => $this->siteUserBuilder->siteUserToValue($sim['site_id'], $sim['user_id']),
                'similar_id' => $sim['similar_id'],
                'distance' => $sim['distance'],
                'tags' => $sim['tags'],
                'box' => Json::decode($sim['box']),
                'doc' => [
                    'src' => $this->storage->getPublicUrlByKey(implode('/', [$sim['site_id'], $sim['user_id'], $sim['filename']])),
                    'width' => $sim['width'],
                    'height' => $sim['height'],
                ],
            ], $this->querySimilarly($face['face_embedding'])->all())
        ], $faces);
    }

    private function querySimilarly(array $vector): Query
    {
        $vectorStr = implode(',', array_map(static fn($val) => is_numeric($val) ? $val : throw new \ErrorException('Invalid vector: ' . print_r($vector, true)), $vector));

        $query = (new Query($this->db))
            ->select([
                'site_id' => 'ud.site_id',
                'user_id' => 'ud.user_id',
                'filename' => 'ud.filename',
                'similar_id' => $this->authAccess->can('/finance/face-validation') ? 'udf.similar_id' : '(null)',
                'distance' => '(udf.face_embedding <-> comp.face_embedding)',
                'box' => "array_to_json(udf.box)",
                'tags' => "array_to_string(ud.tags, ',', '*')",
                'width' => 'ud.width',
                'height' => 'ud.height',
            ])
            ->from(['udf' => $this->alternativeModel ? UserDocumentFacesSecondary::TABLE_NAME : UserDocumentFaces::TABLE_NAME])
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'udf.user_document_id = ud.id')
            ->join('CROSS JOIN', [new Expression("(VALUES(ARRAY[{$vectorStr}]::vector(512))) as comp(face_embedding)")])
            ->where([
                'AND',
                ['>=', '(udf.face_embedding <-> comp.face_embedding)', (string)$this->distanceMin],
                ['<', '(udf.face_embedding <-> comp.face_embedding)', (string)$this->distanceMax],
                ['IS DISTINCT FROM', 'udf.inherited_face', true],
            ])
            ->orderBy(['distance' => SORT_ASC])
            ->limit($this->limit);

        if (isset($this->hqFacesSearch)) {
            $query->andWhere(
                ['(udf.ratio > :low_quality)' => $this->hqFacesSearch],
                ['low_quality' => UserDocumentFaceSimilarity::FACE_QUALITY_RATIO_LOW]
            );
        }

        if ($this->siteUser) {
            ['siteId' => $siteId, 'userId' => $userId] = $this->siteUserBuilder->valueToSiteUser($this->siteUser);
            $query->andWhere([
                'ud.site_id' => $siteId,
                'ud.user_id' => $userId
            ]);
        }

        if ($this->filterNotBankCard) {
            $query->andWhere('ud.tags != array[:bank_card]::varchar[]', [
                ':bank_card' => UserDocument::TAG_BANK_CARD
            ]);
        }

        return $query;
    }
}
