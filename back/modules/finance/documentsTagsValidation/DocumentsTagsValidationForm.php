<?php

declare(strict_types=1);

namespace app\back\modules\finance\documentsTagsValidation;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\IdMultilineValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\SiteUserIdValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentLog;
use app\back\modules\api\components\Operators;
use app\back\repositories\Employees;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentLogs;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Query\Query;
use Yiisoft\Db\QueryBuilder\Condition\NotCondition;
use Yiisoft\Db\QueryBuilder\Condition\SimpleCondition;

class DocumentsTagsValidationForm
{
    use FormGrid {
        validate as gridValidate;
    }

    private const string VALIDATION_OK = 'valid';
    private const string VALIDATION_INVALID = 'invalid';
    private const string VALIDATION_UNKNOWN = 'unknown';
    private const array VALIDATION_STATUSES = [
        self::VALIDATION_OK => 'OK',
        self::VALIDATION_INVALID => 'Invalid',
        self::VALIDATION_UNKNOWN => 'Unknown',
    ];
    private const array RECOGNIZED_TAGS = [
        UserDocument::TAG_ID_CARD => 'Id card',
        UserDocument::TAG_PASSPORT => 'Passport',
        UserDocument::TAG_DIVING_LICENSE => 'Driving license',
        UserDocument::TAG_BANK_CARD => 'Bank card',
        UserDocument::TAG_SELFIE => 'Selfie',
        'other' => 'Other',
    ];
    private const array ACTUAL_TAGS = [
        ...self::RECOGNIZED_TAGS,
        UserDocument::TAG_NOT_RECOGNIZED => 'Not recognized'
    ];

    private const string SCORE_FROM_TO_PATTERN = '/^-?\d+(?:\.\d+)?:-?\d+(?:\.\d+)?$/';

    #[DateTimeValidator]
    public ?string $from = null;
    #[DateTimeValidator]
    public ?string $to = null;
    #[SiteUserIdValidator]
    public ?string $siteUser = null;
    #[IdMultilineValidator]
    public ?string $documentId = null;
    #[IntValidator(1, 1000)]
    public int $limit = 100;
    #[MatchValidator(self::SCORE_FROM_TO_PATTERN)]
    public ?string $selfieIQA = null;
    #[MatchValidator(self::SCORE_FROM_TO_PATTERN)]
    public ?string $docIQA = null;
    #[MatchValidator(self::SCORE_FROM_TO_PATTERN)]
    public ?string $faceRatio = null;
    #[StringArrayValidator(self::RECOGNIZED_TAGS)]
    public ?array $recognizedTags = null;
    #[StringArrayValidator(self::ACTUAL_TAGS)]
    public ?array $actualTags = null;
    #[StringInArrayValidator(Operators::IN_NOT_IN, true)]
    public string $recognizedTagsOperator = Operators::IN;
    #[StringInArrayValidator(Operators::IN_NOT_IN, true)]
    public string $actualTagsOperator = Operators::IN;

    #[BooleanValidator]
    public ?bool $angleExists = null;
    #[BooleanValidator]
    public ?bool $anglePositive = null;
    #[BooleanValidator]
    public ?bool $hasFaceByTagsSearch = null;
    #[BooleanValidator]
    public ?bool $hasFaceByFaceSearch = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly SecondaryConnection $db,
        private readonly FileStorage $storage,
        private readonly SiteUserBuilder $siteUserBuilder,
    ) {
    }

    public function validate(array $data): array
    {
        $errors = $this->gridValidate($data);

        if (empty($errors) && !isset($this->documentId)) {
            $this->from ??= date('Y-m-d 00:00:00');
        }

        return $errors;
    }

    public function search(): array
    {
        $docs = [];
        foreach ($this->query()->all() as $doc) {
            if (empty($doc['last_rotate_date']) || $doc['last_rotate_date'] <= $doc['last_classification_date']) {
                $facesForTags = Json::decode($doc['faces_for_tags']) ?? [];
            } else {
                $facesForTags = [];
            }
            $details = Json::decode($doc['details']) ?? [];
            foreach ($details as $i => $d) {
                if (isset($d['tags'], $d['selfie_quality'])) {
                    $details[$i]['tags'] = self::tagsAddIQA($d['tags'], $d['selfie_quality']);
                }
            }

            $docs[] = [
                'id' => $doc['id'],
                'createdAt' => $doc['created_at'],
                'width' => $doc['width'],
                'height' => $doc['height'],
                'siteUser' => $this->siteUserBuilder->siteUserToValue($doc['site_id'], $doc['user_id']),
                'siteId' => $doc['site_id'],
                'userId' => $doc['user_id'],
                'tags' => self::tagsAddIQA(Json::decode($doc['tags']), $doc['selfie_quality'] ? $doc['selfie_quality'] * 100 : null, $doc['doc_quality']),
                'src' => $this->storage->getPublicUrlByKey(implode('/', [$doc['site_id'], $doc['user_id'], $doc['filename']])),
                'faces_for_match' => array_values(Json::decode($doc['faces_for_match']) ?? []),
                'faces_for_tags' => $facesForTags,
                'recognitions' => $details,
            ];
        }

        return [
            'form' => $this->response(),
            'data' => $docs,
        ];
    }

    private function query(): Query
    {
        $query = (new Query($this->db))
            ->select([
                'id' => 'ud.id',
                'site_id' => 'ANY_VALUE(ud.site_id)',
                'user_id' => 'ANY_VALUE(ud.user_id)',
                'filename' => 'ANY_VALUE(ud.filename)',
                'height' => 'ANY_VALUE(ud.height)',
                'width' => 'ANY_VALUE(ud.width)',
                'created_at' => 'ANY_VALUE(ud.created_at)',
                'selfie_quality' => 'ANY_VALUE(ud.selfie_quality)',
                'doc_quality' => 'ANY_VALUE(ud.doc_quality)',
                'tags' => 'array_to_json(ANY_VALUE(ud.tags))',
                'last_rotate_date' => 'MAX(udl.created_at) FILTER (WHERE udl.action = :action_rotate)',
                'last_classification_date' => 'MAX(udl.created_at) FILTER (WHERE udl.action = :action_tags_recognize)',
                'faces_for_tags' => "(jsonb_agg((CASE WHEN udl.details @?? '$.faces[*].coordinates' THEN jsonb_path_query_array(udl.details, '$.faces[*].coordinates') ELSE udl.details->'faces' END) ORDER BY udl.created_at DESC) FILTER (WHERE udl.action = :action_tags_recognize))[0]",
                'faces_for_match' => "jsonb_object_agg(udf.id, jsonb_build_object(" .
                    "'id', udf.id, " .
                    "'ratio', udf.ratio, " .
                    "'score', udf.recognition_score, " .
                    "'box', array_to_json(udf.box)::jsonb" .
                    ")) FILTER (WHERE udf.id IS NOT NULL)",
                'details' => "jsonb_agg(DISTINCT jsonb_build_object(" .
                    "'created_at', udl.created_at, " .
                    "'tags', udl.details->'tags', " .
                    "'selfie_quality', udl.details->'face_iqa'," .
                    "'angle_value', NULLIF(udl.details->'rotate clockwise'->>'angle', 'None') || '°', " .
                    "'angle_score', NULLIF(udl.details->'rotate clockwise'->>'probability', 'None')" .
                ")) FILTER (WHERE udl.document_id IS NOT NULL AND udl.action = :action_tags_recognize)",
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->leftJoin(['udl' => UserDocumentLogs::TABLE_NAME], 'udl.document_id = ud.id')
            ->leftJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'udf.user_document_id = ud.id')
            ->leftJoin(['e' => Employees::TABLE_NAME], 'udf.validated_by = e.employee_id')
            ->andFilterWhere(['ud.id' => Str::explodeText($this->documentId)])
            ->andFilterWhere(['>=', 'ud.created_at', $this->from])
            ->andFilterWhere(['<', 'ud.created_at', $this->to])
            ->groupBy(['ud.id'])
            ->orderBy(['MAX(udl.created_at)' => SORT_DESC])
            ->limit($this->limit)
            ->addParams([
                ':action_tags_recognize' => UserDocumentLog::ACTION_AUTO_TAGS,
                ':action_rotate' => UserDocumentLog::ACTION_ROTATE,
            ]);

        if (!isset($this->documentId)) {
            $query->having('COUNT(*) FILTER (WHERE udl.action = :action_tags_recognize) > 0');
        }

        $condition = static fn($val) => $val ? '> 0' : '= 0';
        $this->angleExists !== null && $query->andHaving("COUNT(*) FILTER (WHERE udl.details->'rotate clockwise'->>'angle' != 'None') {$condition($this->angleExists)}");
        $this->anglePositive !== null && $query->andHaving("COUNT(*) FILTER (WHERE (NULLIF(udl.details->'rotate clockwise'->>'angle', 'None'))::int > 0) {$condition($this->anglePositive)}");
        $this->hasFaceByTagsSearch !== null && $query->andHaving("COUNT(udl.details->'faces'->>0) {$condition($this->hasFaceByTagsSearch)}");
        $this->hasFaceByFaceSearch !== null && $query->andHaving("COUNT(udf.id) {$condition($this->hasFaceByFaceSearch)}");

        if (!empty($this->recognizedTags)) {
            $condition = new SimpleCondition("count(*) filter (where ARRAY(SELECT jsonb_array_elements_text(udl.details -> 'tags')) && {$this->tagsSqlStr($this->recognizedTags)})", '>', 0);
            $condition = match ($this->recognizedTagsOperator) {
                Operators::IN => $condition,
                Operators::NOT_IN => new NotCondition($condition),
            };
            $query->andHaving($condition);
        }

        if (!empty($this->actualTags)) {
            $condition = 'ud.tags && ' . $this->tagsSqlStr($this->actualTags, true);
            $query->andWhere(match ($this->actualTagsOperator) {
                Operators::IN => $condition,
                Operators::NOT_IN => new NotCondition($condition),
            });
        }

        if (!empty($this->selfieIQA)) {
            $query->andWhere(['BETWEEN', 'ud.selfie_quality', ...explode(':', $this->selfieIQA)]);
        }

        if (!empty($this->docIQA)) {
            $query->andWhere(['BETWEEN', 'ud.doc_quality', ...explode(':', $this->docIQA)]);
        }

        if (!empty($this->faceRatio)) {
            $params = array_combine([':ratio_from', ':ratio_to'], explode(':', $this->faceRatio));
            $query->andHaving('0 < count(udf.id) FILTER (WHERE udf.ratio BETWEEN :ratio_from AND :ratio_to)', $params);
        }

        if ($this->siteUser) {
            ['siteId' => $siteId, 'userId' => $userId] = $this->siteUserBuilder->valueToSiteUser($this->siteUser);
            $query->andWhere(['AND', ['ud.site_id' => $siteId], ['ud.user_id' => $userId]]);
        }

        return $query;
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateCell(1, 'from', 'From', [
                    'enableTime' => true,
                    'dateFormat' => 'Y-m-d H:i:S',
                ]),
                $this->dateCell(1, 'to', 'To', [
                    'enableTime' => true,
                    'dateFormat' => 'Y-m-d H:i:S',
                ]),
                $this->textInputCell(1, 'siteUser', 'Site-User'),
                $this->textAreaCell(1, 'documentId', 'Document ID'),
                $this->textInputCell(1, 'faceRatio', 'Face ratio', ['placeholder' => 'a.g. 0.1:1.0']),
                $this->textInputCell(1, 'selfieIQA', 'Selfie IQA', ['placeholder' => 'a.g. 0.1:1.0']),
                $this->textInputCell(1, 'docIQA', 'Doc IQA', ['placeholder' => 'a.g. 0.1:1.0']),
                $this->textInputCell(1, 'limit', 'Limit'),
            ],
            [
                $this->selectBooleanCell(1, 'angleExists', 'Angle exists', ['multiple' => false]),
                $this->selectBooleanCell(1, 'anglePositive', 'Angle > 0', ['multiple' => false]),
                $this->selectBooleanCell(1, 'hasFaceByTagsSearch', 'Has face (tags search)', ['multiple' => false]),
                $this->selectBooleanCell(1, 'hasFaceByFaceSearch', 'Has face (faces search)', ['multiple' => false]),
                $this->listCell(3, 'recognizedTags', 'Recognized tags', [
                    'list' => Arr::assocToIdName(self::RECOGNIZED_TAGS),
                    'operatorName' => 'recognizedTagsOperator',
                    'operators' => Arr::assocToIdName(Arr::leaveOnlyKeys(Operators::NAMES, Operators::IN_NOT_IN))
                ]),
                $this->listCell(4, 'actualTags', 'Actual tags', [
                    'list' => Arr::assocToIdName(self::ACTUAL_TAGS),
                    'operatorName' => 'actualTagsOperator',
                    'operators' => Arr::assocToIdName(Arr::leaveOnlyKeys(Operators::NAMES, Operators::IN_NOT_IN))
                ]),
                $this->submitCell(1, 'Search'),
            ]
        ];
    }

    public static function tagsAddIQA(?array $tags, float | string | null $faceScore, float | string | null $docScore = null): array
    {
        $tags ??= [];

        sort($tags);

        if ($docScore !== null) {
            $tags[] = 'docs IQA ' . number_format((float)$docScore, 2, '.', '');
        }

        if ($faceScore !== null && in_array(UserDocument::TAG_SELFIE, $tags, true)) {
            $tags = array_diff($tags, [UserDocument::TAG_SELFIE]);
            $tags[] = UserDocument::TAG_SELFIE . ' ' . number_format((float)$faceScore, 2, '.', '') . '%';
        }

        return array_values($tags);
    }

    private function tagsSqlStr(array $tags, bool $asVarchar = false): string
    {
        $quoter = $this->db->getQuoter();
        return 'ARRAY[' . implode(', ', array_map(static fn(string $v) => $quoter->quoteValue($v), $tags)) . ']' . ($asVarchar ? '::varchar[]' : '');
    }
}
