<?php

declare(strict_types=1);

namespace app\back\modules\finance\documentsText;

use app\back\components\exceptions\NotFoundException;
use app\back\components\Form;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\IdValidator;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserDocumentTextManualLabels;
use app\back\repositories\UserDocumentTextPredictions;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class DocumentsTextViewForm
{
    use Form;

    #[IdValidator]
    public ?int $userDocumentId = null;

    public function __construct(
        private readonly FileStorage $storage,
        private readonly SecondaryConnection $db,
        private readonly UserDocumentTextPredictions $userDocumentTextPredictions,
        private readonly SiteUserBuilder $siteUserBuilder,
    ) {
    }

    public function process(): array
    {
        $query = (new Query($this->db))
            ->select([
                'ud.id',
                'ud.site_id',
                'ud.user_id',
                'ud.filename',
                'ud.country',
                'udp.user_document_id',
                'udp.prediction_model_id',
                'udp.text_full',
                'udp.text_bounding_boxes',
                'udm.text_bounding_boxes as text_bounding_boxes_manual',
                'udm.approved',
            ])
            ->from(['udp' => UserDocumentTextPredictions::TABLE_NAME])
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.id = udp.user_document_id')
            ->leftJoin(['udm' => UserDocumentTextManualLabels::TABLE_NAME], 'ud.id = udm.user_document_id')
            ->orderBy('udp.created_at desc');

        if ($this->userDocumentId) {
            $query->andWhere(['ud.id' => $this->userDocumentId]);
        } else {
            $query->andWhere(['udm.approved' => null]);
        }

        $row = $query->one();

        if (!$row) {
            throw new NotFoundException('Document not found');
        }

        $query = (new Query($this->db))
            ->select([
                'ud.id',
                'ud.site_id',
                'ud.user_id',
                'ud.filename',
                'udm.approved',
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->innerJoin(['udp' => UserDocumentTextPredictions::TABLE_NAME], 'ud.id = udp.user_document_id')
            ->leftJoin(['udm' => UserDocumentTextManualLabels::TABLE_NAME], 'ud.id = udm.user_document_id')
            ->where([
                'ud.site_id' => $row['site_id'],
                'ud.user_id' => $row['user_id'],
                'udp.prediction_model_id' => $row['prediction_model_id']
            ])
            ->andWhere([
                '!=', 'ud.id', $row['id']
            ])
            ->orderBy(new Expression('udm.approved IS FALSE, udm.approved IS NOT NULL, ud.created_at DESC'));

        $otherDocuments = array_map(function ($row) {
            return [
                'id' => $row['id'],
                'approved' => $row['approved'],
                'url' => $this->storage->getPublicUrlByKey(implode('/', [$row['site_id'], $row['user_id'], $row['filename']])),
            ];
        }, $query->all());

        return [
            'siteId' => $row['site_id'],
            'userId' => $row['user_id'],
            'country' => $row['country'],
            'siteUser' => $this->siteUserBuilder->siteUserToValue($row['site_id'], $row['user_id']),
            'userDocumentId' => $row['user_document_id'],
            'approved' => $row['approved'],
            'url' => $this->storage->getPublicUrlByKey(implode('/', [$row['site_id'], $row['user_id'], $row['filename']])),
            'fullText' => $row['text_full'],
            'textBoundingBoxes' => json_decode($row['text_bounding_boxes'] ?: 'null', true, 512, JSON_THROW_ON_ERROR),
            'textBoundingBoxesManual' => json_decode($row['text_bounding_boxes_manual']  ?: 'null', true, 512, JSON_THROW_ON_ERROR),
            'otherDocuments' => $otherDocuments,
            'awaitApproveCount' => $this->userDocumentTextPredictions::findAwaitApproveCount($this->db),
        ];
    }
}
