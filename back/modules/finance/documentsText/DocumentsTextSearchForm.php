<?php

declare(strict_types=1);

namespace app\back\modules\finance\documentsText;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\SiteUserIdValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserDocumentTextManualLabels;
use app\back\repositories\UserDocumentTextPredictions;
use Yiisoft\Db\Query\Query;

class DocumentsTextSearchForm
{
    use FormGrid;

    private const string STATUS_IGNORE = 'ignore';
    private const string STATUS_NEW = 'new';
    private const string STATUS_APPROVED = 'approved';

    #[SiteUserIdValidator]
    public ?string $siteUser = null;
    #[StringInArrayValidator([self::STATUS_IGNORE, self::STATUS_NEW, self::STATUS_APPROVED], true)]
    public ?string $status = null;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $from = null;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $to = null;
    #[StringValidator]
    public ?string $surname = null;
    #[IntValidator(1, 500)]
    public int $limit = 100;
    #[StringArrayValidator(1)]
    public array $ignore = [];

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly FileStorage $storage,
        private readonly SecondaryConnection $db,
        private readonly UserDocumentTextPredictions $userDocumentTextPredictions,
        private readonly SiteUserBuilder $siteUserBuilder,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputLiveSearchCell(3, 'surname', 'Surname', '/finance/documents-text/autocomplete-surname', [
                    'focusOnMount' => true,
                    'submitOnEnter' => true,
                ]),
                $this->dateCell(1, 'from', 'From (created)'),
                $this->dateCell(1, 'to', 'To (created)'),
                $this->textInputCell(1, 'siteUser', 'Site-User'),
                $this->selectCell(2, 'status', 'Status', [
                    'list' => Arr::assocToIdName([
                        self::STATUS_IGNORE => 'Ignore',
                        self::STATUS_NEW => 'New',
                        self::STATUS_APPROVED => 'Approved',
                    ]),
                    'allowSearch' => false,
                    'multiple' => false,
                    'input' => false,
                ]),
                $this->submitCell(1, 'Search'),
            ]
        ];
    }

    public function search(): array
    {
        $query = (new Query($this->db))
            ->select([
                'ud.site_id',
                'ud.user_id',
                'ud.filename',
                'udp.user_document_id',
                'udp.text_full',
                'udm.text_full as text_full_manual',
                'udm.approved',
                'ud.created_at',
                'ud.country',
            ])
            ->from(['udp' => UserDocumentTextPredictions::TABLE_NAME])
            ->leftJoin(['udm' => UserDocumentTextManualLabels::TABLE_NAME], 'udp.user_document_id = udm.user_document_id')
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.id = udp.user_document_id')
            ->andFilterWhere(['>=', 'ud.created_at', $this->from?->format('Y-m-d')])
            ->andFilterWhere(['<', 'ud.created_at', $this->to?->format('Y-m-d')])
            ->limit($this->limit);

        foreach ($this->ignore as $ignore) {
            $query
                ->andWhere('udp.text_full !~* :ignore', [':ignore' => "(?:\s|^){$ignore}(?:\s|$)"])
                ->andWhere('udm.text_full IS NULL OR udm.text_full !~* :ignore', [':ignore' => "(?:\s|^){$ignore}(?:\s|$)"]);
        }

        if ($this->surname) {
            $query
                ->addSelect([
                    'lev_avg' => 'LEAST(lvp.average, lvm.average)',
                    'prediction_matches' => 'lvp.labels',
                    'manual_matches' => 'lvm.labels',
                ])
                ->innerJoin(['search' => '(select :surname as v)'], 'TRUE')
                ->innerJoin(['lvp' => 'LEVENSHTEIN_FULL_TEXT(udp.text_full, search.v)'], 'TRUE')
                ->innerJoin(['lvm' => 'LEVENSHTEIN_FULL_TEXT(udm.text_full, search.v)'], 'TRUE')
                ->andWhere('LEAST(lvp.average, lvm.average) < 0.99')
                ->orderBy('lev_avg')
                ->addParams(['surname' => $this->surname]);
        } else {
            $query
                ->addSelect([
                    'lev_avg' => '(NULL)',
                    'prediction_matches' => '(NULL)',
                    'manual_matches' => '(NULL)',
                ])
                ->orderBy('udp.created_at desc');
        }

        if ($this->status === self::STATUS_APPROVED) {
            $query->andWhere(['udm.approved' => true]);
        } elseif ($this->status === self::STATUS_IGNORE) {
            $query->andWhere(['udm.approved' => false]);
        } elseif ($this->status === self::STATUS_NEW) {
            $query->andWhere(['udm.approved' => null]);
        }

        if ($this->siteUser) {
            ['siteId' => $siteId, 'userId' => $userId] = $this->siteUserBuilder->valueToSiteUser($this->siteUser);
            $query->andWhere([
                'ud.site_id' => $siteId,
                'ud.user_id' => $userId,
            ]);
        }

        $res = [];
        foreach ($query->all() as $row) {
            $res[] = [
                'id' => $row['user_document_id'],
                'siteUser' => $this->siteUserBuilder->siteUserToValue($row['site_id'], $row['user_id']),
                'siteId' => $row['site_id'],
                'userId' => $row['user_id'],
                'url' => $this->storage->getPublicUrlByKey(implode('/', [$row['site_id'], $row['user_id'], $row['filename']])),
                'textFull' => $row['text_full'],
                'textFullManual' => $row['text_full_manual'],
                'createdAt' => $row['created_at'],
                'levAvg' => $row['lev_avg'],
                'predictionMatches' => Json::decode($row['prediction_matches']),
                'manualMatches' => Json::decode($row['manual_matches']),
                'country' => $row['country'],
            ];
        }

        return $res;
    }

    public function config(): array
    {
        $response = $this->response();
        $response['awaitApproveCount'] = $this->userDocumentTextPredictions::findAwaitApproveCount($this->db);
        return $response;
    }
}
