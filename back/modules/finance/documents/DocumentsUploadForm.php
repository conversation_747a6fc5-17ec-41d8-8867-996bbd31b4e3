<?php

declare(strict_types=1);

namespace app\back\modules\finance\documents;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\InvalidException;
use app\back\components\exceptions\UserException;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\ImageHelper;
use app\back\components\helpers\Str;
use app\back\components\kyc\KycStatusModel;
use app\back\components\PgArray;
use app\back\components\services\FileStorage;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\FileValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\repositories\Countries;
use app\back\repositories\UserDocumentProgresses;
use app\back\repositories\views\UserDocumentsActive;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class DocumentsUploadForm
{
    use FormGrid;

    #[AllowedSiteValidator]
    public int $siteId;
    #[IntValidator]
    public int $userId;
    #[FileValidator(mimeTypes: ['image/jpeg'], maxCount: 5)]
    public array $files;
    #[StringArrayValidator(UserDocument::TAGS)]
    public array $tag;
    #[StringInArrayValidator([self::class, 'countryNames'])]
    public ?string $country = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly FileStorage $storage,
        private readonly UserDocumentsActive $userDocumentsActive,
        private readonly BaseAuthAccess $authAccess,
        private readonly Countries $countriesRepo,
        private readonly UserDocumentProgresses $userDocumentProgressesRepo,
        private readonly KycStatusModel $kycStatusModel,
    ) {
        $this->kycStatusModel->source = UserDocumentProgress::SOURCE_ANALYTICS;
    }

    public static function countryNames(self $form): array
    {
        return $form->countriesRepo->getNames();
    }

    protected function blocks(): array
    {
        return [
            [
                $this->listCell(12, 'tag', 'Tags', [
                    'list' => Arr::assocToIdName(UserDocument::TAGS),
                ]),
            ],
            [
                $this->fileCell(4, 'files', 'Image', [
                    'multiple' => true,
                    'hint' => 'Only .jpg or .jpeg file allowed',
                ]),
                $this->selectCell(6, 'country', 'Document country', [
                    'list' => Arr::assocToIdName(
                        $this->countriesRepo->getQuantityOrderedNames(UserDocumentsActive::TABLE_NAME, 'country')
                    ),
                    'multiple' => false,
                ]),
                $this->submitCell(2, 'Upload', [
                    'buttonStyle' => 'btn-primary float-end',
                ]),
            ],
        ];
    }

    public function upload(): void
    {
        $docs = $details = [];
        foreach ($this->files as $file) {
            /** @var UploadedFile $file */

            if (!($file instanceof UploadedFile)) {
                throw new InvalidException("File has no name");
            }

            $filename = uniqid('', false) . '.jpg';
            $key = $this->siteId . '/' . $this->userId . '/' . $filename;
            $metadata = [
                'Site' => $this->siteId,
                'User' => $this->userId,
                'Name' => Str::cleanString($file->getClientOriginalName()),
                'Tag' => implode(',', $this->tag),
                'Date' => date('Y-m-d H:i:s'),
            ];

            $imagick = new \Imagick();
            if ($imagick->readImage($file->getPathname()) === false) {
                throw new UserException("Unable to read file " . Str::cleanString($file->getClientOriginalName()));
            }

            ImageHelper::clearRotation($imagick);

            $url = $this->storage->put($key, $imagick->getImageBlob(), $file->getMimeType(), $metadata);

            if ($url) {
                $doc = new UserDocument();
                $doc->site_id = $this->siteId;
                $doc->user_id = $this->userId;
                $doc->filename = $filename;
                $doc->tags = new PgArray($this->tag);
                $doc->country = $this->country;
                $doc->width = $imagick->getImageWidth();
                $doc->height = $imagick->getImageHeight();
                $doc->created_by = $doc->updated_by = $this->authAccess->employeeId();
                $doc->created_at = $doc->updated_at = new \DateTimeImmutable(UserDocument::SQL_NOW_DATETIME);

                $this->userDocumentsActive->insert($doc);

                $docs[] = $doc;
                $details[] = UserDocumentProgress::userDocumentDetailOperations($doc, UserDocumentProgress::ACTION_DOC_UPLOAD);
            }
        }

        if (!empty($docs)) {
            $this->userDocumentProgressesRepo->insertAfterAddOrDelete([
                'site_id' => $this->siteId,
                'user_id' => $this->userId,
                'details' => $details,
                'action' => UserDocumentProgress::ACTION_DOC_UPLOAD,
                'source' => UserDocumentProgress::SOURCE_ANALYTICS,
                'created_by' => $this->authAccess->employeeId()
            ]);
            $this->kycStatusModel->update($this->siteId, $this->userId, UserKyc::KYC_WAIT, docIds: array_map(static fn(UserDocument $d) => $d->id, $docs));
        }

        if (count($docs) !== count($this->files)) {
            throw new UserException("Unable to upload file(s)");
        }
    }
}
