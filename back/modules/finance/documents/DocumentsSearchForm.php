<?php

declare(strict_types=1);

namespace app\back\modules\finance\documents;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\components\Permission;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\UserIdOrSiteUserIdValidator;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\repositories\UserDocumentProgresses;
use app\back\repositories\UserDocuments;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\UserKycs;
use app\back\repositories\Users;
use Yiisoft\Db\Query\Query;

class DocumentsSearchForm
{
    use FormGrid;

    #[BooleanValidator]
    public bool $showDeleted = false;
    #[AllowedSiteValidator]
    public ?int $siteId = null;
    #[UserIdOrSiteUserIdValidator]
    public string $userId;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly BaseAuthAccess $authAccess,
        private readonly SecondaryConnection $db,
        private readonly FileStorage $storage,
        private readonly SiteUserBuilder $siteUserBuilder,
        private readonly UserDocuments $userDocuments,
    ) {
    }

    protected function blocks(): array
    {
        return [[
            $this->checkboxCell(2, 'showDeleted', 'Show in trash'),
            $this->selectSiteCell(4, 'siteId', 'Site ID', ['multiple' => false,]),
            $this->textInputCell(4, 'userId', 'User ID / Site-User ID', ['submit-on-enter' => true]),
            $this->submitCell(2, 'Search'),
        ]];
    }

    public function search(): array
    {
        ['site_id' => $siteId, 'user_id' => $userId] = UserIdOrSiteUserIdValidator::siteIdUserIdFromValue($this->userId, $this);
        [$siteId, $userId] = [(int)$siteId, (int)$userId];

        $response = [
            'values' => $this->params(),
            'user' => [
                'siteId' => $siteId,
                'userId' => $userId,
                'siteUser' => $this->siteUserBuilder->siteUserToValue($siteId, $userId),
                ...$this->findUserFlags($siteId, $userId),
            ],
            'verificationAvailable' => $this->authAccess->can(Permission::PAGE_DOCUMENTS_KYC),
            'files' => [],
        ];

        foreach ($this->documentGroupedByCurrentWait($siteId, $userId) as [$groupName, $documents]) {
            $response['files'][] = [
                'groupName' => $groupName,
                'documents' => array_map(fn(UserDocument $doc) => [
                    'url' => $this->storage->getPublicUrlByKey($doc->storagePath()),
                    'filename' => $doc->filename,
                    'country' => $doc->country,
                    'date' => $doc->created_at?->format(DateHelper::DATETIME_FORMAT_PHP),
                    'tags' => isset($doc->doc_type) ? [UserDocumentProgress::TYPES[$doc->doc_type] ?? $doc->doc_type] : array_map(static fn($t) => UserDocument::TAGS[$t] ?? $t, $doc->tags->toArray()),
                    'needApprove' => $doc->external_approve_required,
                    'selfieQuality' => $doc->selfie_quality ?? null,
                    'docQuality' => $doc->doc_quality ?? null,
                    'isDeleted' => $doc->deleted_at,
                    'aiValidation' => $this->aiValidationInfo($doc),
                ], $documents),
            ];
        }

        return $response;
    }

    private function aiValidationInfo(UserDocument $doc): ?array
    {
        if (!isset($doc->ai_validation[UserDocument::AI_V_KEY_VALID])) {
            return null;
        }

        return [
            'valid' => (bool)$doc->ai_validation[UserDocument::AI_V_KEY_VALID],
            'messages' => array_map(
                static fn($msg) => UserDocument::AI_VALIDATIONS[$msg] ?? (string)$msg,
                $doc->ai_validation[UserDocument::AI_V_KEY_MESSAGES] ?? []
            ),
        ];
    }

    private function documentGroupedByCurrentWait(int $siteId, int $userId): array
    {
        $documents = $this->siteUserDocuments($siteId, $userId);

        if (empty($documents)) {
            return [];
        }

        [$waitStartAt, $waitDocs] = $this->curWaitDocs($siteId, $userId);

        if ($waitStartAt === null) {
            return [['', array_values($documents)]];
        }

        // old waits not in doc_ids
        foreach (array_diff_key($documents, array_flip($waitDocs)) as $doc) {
            if ($doc->created_at->format(DateHelper::DATETIME_FORMAT_PHP) >= $waitStartAt) {
                $waitDocs[] = $doc->id;
            }
        }

        $waitDocs = Arr::leaveOnlyKeys($documents, $waitDocs);

        $res = [['Waiting approve documents', array_values($waitDocs)]];

        $documents = array_values(array_diff_key($documents, $waitDocs));

        if (!empty($documents)) {
            $res[] = ['Other documents', $documents];
        }

        return $res;
    }

    private function siteUserDocuments(int $siteId, int $userId): array
    {
        if ($this->showDeleted) {
            $deletedExpression = ['>=', 'deleted_at', date(DateHelper::DATETIME_FORMAT_PHP, strtotime(UserDocument::EXPIRED_TIME_EXPRESSION))];
        } else {
            $deletedExpression = ['deleted_at' => null];
        }
        $documents = iterator_to_array($this->userDocuments->findByQuery(
            (new Query($this->db))
                ->where([
                    'AND',
                    ['site_id' => $siteId],
                    ['user_id' => $userId],
                    $deletedExpression,
                ])
                ->orderBy(['created_at' => SORT_DESC])
        ));
        return array_combine(array_column($documents, 'id'), $documents);
    }

    private function curWaitDocs(int $siteId, int $userId): array
    {
        $data = (new Query($this->db))
            ->select([
                'wait_start_at' => 'kyc.status_updated_at',
                'doc_ids' => "COALESCE(array_to_json(p.doc_ids), '[]')"
            ])
            ->from(['kyc' => UserKycs::TABLE_NAME])
            ->innerJoin(['p' => UserDocumentProgresses::TABLE_NAME], 'p.id = kyc.last_wait_progress_id')
            ->where([
                'kyc.kyc_status' => [UserKyc::KYC_WAIT, UserKyc::KYC_WAIT_WITHOUT_REQUEST],
                'kyc.site_id' => $siteId,
                'kyc.user_id' => $userId,
            ])
            ->one();

        if ($data === null) {
            return [null, []];
        }

        return [$data['wait_start_at'], Json::decode($data['doc_ids'])];
    }

    private function findUserFlags(int $siteId, int $userId): array
    {
        $flags = (new Query($this->db))
            ->select([
                'isBlocked' => 'u.is_blocked',
                'isIgnoreFinal' => '(ui.user_id IS NOT NULL)'
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->leftJoin(['ui' => UserIgnoreIds::TABLE_NAME], 'ui.site_id = u.site_id AND ui.user_id = u.user_id')
            ->where([
                'u.site_id' => $siteId,
                'u.user_id' => $userId,
            ])
            ->limit(1)
            ->one();

        return $flags ?: [
            'isBlocked' => false,
            'isIgnoreFinal' => false,
        ];
    }
}
