<?php

declare(strict_types=1);

namespace app\back\modules\finance\documents;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\UserException;
use app\back\components\Form;
use app\back\components\helpers\Str;
use app\back\components\PgArray;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\UserDocument;
use app\back\repositories\Countries;
use app\back\repositories\views\UserDocumentsActive;

class DocumentsExternalApproveForm
{
    use Form;

    #[AllowedSiteValidator]
    public int $siteId;
    #[IntValidator]
    public int $userId;
    #[StringArrayValidator]
    public ?array $files = null;
    #[CallableValidator([self::class, 'tagsValidate'])]
    public array $tags = [];
    #[StringInArrayValidator([self::class, 'countryNames'])]
    public string $country;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly DocumentsDeleteRestoreForm $deleteForm,
        private readonly UserDocumentsActive $userDocumentsActive,
        private readonly BaseAuthAccess $authAccess,
        private readonly Countries $countriesRepo,
    ) {
    }

    public static function countryNames(self $form): array
    {
        return $form->countriesRepo->getNames();
    }

    public static function tagsValidate(mixed $value, self $form): ?string
    {
        if (!is_array($value)) {
            return 'is not array';
        }

        $allowedTags = array_merge(UserDocument::TAGS, [DocumentsExternalSearchForm::TAG_TRASH => true]);

        foreach ($value as $pos => $val) {
            $file = strip_tags(Str::cleanString($form->files[$pos] ?? ($pos + 1)));
            if (!is_array($val)) {
                return "is not array for file $file";
            }

            if (empty($val)) {
                return "required for file $file";
            }

            foreach ($val as $item) {
                if (!array_key_exists($item, $allowedTags)) {
                    return "invalid for file $file";
                }
            }
        }

        return null;
    }

    public function approve(): void
    {
        $toDelete = [];
        $files = array_combine($this->files, $this->tags);
        foreach ($files as $filename => $tags) {
            /** @var UserDocument $doc */
            $doc = $this->userDocumentsActive->findOneOr404([
                'site_id' => $this->siteId,
                'user_id' => $this->userId,
                'filename' => $filename
            ]);

            if (!$doc->external_approve_required) {
                continue;
            }

            if ($tags === [DocumentsExternalSearchForm::TAG_TRASH]) {
                $toDelete[] = $filename;
                continue;
            }

            $doc->external_approve_required = null;
            $doc->country = $this->country;
            $doc->tags = new PgArray($tags);
            $doc->updated_by = $this->authAccess->employeeId();
            $doc->updated_at = new \DateTimeImmutable(UserDocument::SQL_NOW_DATETIME);

            $updated = $this->userDocumentsActive->update($doc, ['external_approve_required', 'country', 'tags', 'updated_by', 'updated_at']);
            $updated or throw new UserException('Unable to save document');
        }

        if (!empty($toDelete)) {
            $this->deleteForm->validateOrException([
                'documents' => array_map(fn($filename) => [
                    'site_id' => $this->siteId,
                    'user_id' => $this->userId,
                    'filename' => $filename,
                ], $toDelete),
            ]);
            $this->deleteForm->updateIsDeleted(true);
        }
    }
}
