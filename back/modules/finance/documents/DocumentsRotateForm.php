<?php

declare(strict_types=1);

namespace app\back\modules\finance\documents;

use app\back\components\AllowedLists;
use app\back\components\DocumentImageModify;
use app\back\components\exceptions\NotFoundException;
use app\back\components\exceptions\UserException;
use app\back\components\Form;
use app\back\components\services\FileStorage;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\UserDocument;
use app\back\repositories\views\UserDocumentsActive;

class DocumentsRotateForm
{
    use Form;

    #[AllowedSiteValidator]
    public int $siteId;
    #[IntValidator]
    public int $userId;
    #[StringValidator(max: 128)]
    public string $filename;
    #[IntInArrayValidator([0, 90, 180, 270], true)]
    public int $angle;
    #[BooleanValidator]
    public bool $flip;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly FileStorage $storage,
        private readonly UserDocumentsActive $docsActiveView,
        private readonly DocumentImageModify $documentImageModify,
    ) {
    }

    public function rotate(): bool
    {
        $userDocument = $this->docsActiveView->findOne([
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
            'filename' => $this->filename,
        ]);

        if (!$userDocument instanceof UserDocument) {
            throw new NotFoundException("Document not found");
        }

        if (!$this->documentImageModify->rotate($userDocument, $this->angle, $this->flip)) {
            throw new UserException('Unable to rotate');
        }

        return true;
    }

    public function publicUrl(): string
    {
        return $this->storage->getPublicUrlByKey((new UserDocument([
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
            'filename' => $this->filename,
        ]))->storagePath());
    }
}
