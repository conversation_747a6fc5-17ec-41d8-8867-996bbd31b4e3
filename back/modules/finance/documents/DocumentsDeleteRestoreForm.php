<?php

declare(strict_types=1);

namespace app\back\modules\finance\documents;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\InvalidException;
use app\back\components\Form;
use app\back\components\helpers\Arr;
use app\back\components\services\FileStorage;
use app\back\components\validators\CallableValidator;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarQueues;
use app\back\repositories\UserDocumentProgresses;
use app\back\repositories\UserDocuments;
use app\back\repositories\Users;

class DocumentsDeleteRestoreForm
{
    use Form;

    #[CallableValidator([self::class, 'validateFiles'])]
    public array $documents = [];

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly FileStorage $storage,
        private readonly UserDocuments $userDocuments,
        private readonly Users $users,
        private readonly UserDocumentFaces $faces,
        private readonly UserDocumentFaceSimilarQueues $faceSimilarQueueRepo,
        private readonly BaseAuthAccess $authAccess,
        private readonly UserDocumentProgresses $userDocumentProgressesRepo,
    ) {
    }

    public static function validateFiles(mixed $files, mixed $form): ?string
    {
        if (empty($files)) {
            throw new InvalidException('Documents is not array or empty');
        }

        $allowedSites = $form->allowedLists->sites();

        foreach ($files as $i => $file) {
            if (!is_array($file) || !isset($file['site_id'], $file['user_id'], $file['filename'])) {
                throw new InvalidException("Invalid file format at $i");
            }
            if (!is_int($file['site_id']) || !array_key_exists($file['site_id'], $allowedSites)) {
                throw new InvalidException("Invalid site_id at $i");
            }
            if (!is_int($file['user_id'])) {
                throw new InvalidException("Invalid user_id at $i");
            }
            if (!is_string($file['filename']) || strlen($file['filename']) !== 17) {
                throw new InvalidException("Invalid filename at position $i");
            }
        }

        return null;
    }

    public function updateIsDeleted(bool $delete): int
    {
        /** @var UserDocument[] $foundDocuments */
        $foundDocuments = Arr::fromIterable($this->userDocuments->findEach(['IN', ['site_id', 'user_id', 'filename'], $this->documents]));

        if (count($this->documents) !== count($foundDocuments)) {
            throw new InvalidException('Some document(s) not found');
        }

        $action = $delete ? UserDocumentProgress::ACTION_DOC_DELETE : UserDocumentProgress::ACTION_DOC_RESTORE;

        $progressRows = [];
        try {
            foreach ($foundDocuments as $foundDocument) {
                if (!$this->storage->keyExist($foundDocument->storagePath())) {
                    throw new InvalidException("Cant restore document file: {$foundDocument->storagePath()}");
                }

                $this->addToProgressRows($progressRows, $foundDocument, $action);

                $this->users->db->transaction(function () use ($delete, $foundDocument) {
                    if ($delete) {
                        $this->faces->deleteByDocumentId($foundDocument->id);
                        $foundDocument->deleted_at = new \DateTimeImmutable(UserDocument::SQL_NOW_DATETIME);
                    } elseif ($foundDocument->deleted_at < new \DateTimeImmutable(UserDocument::EXPIRED_TIME_EXPRESSION)) {
                        throw new InvalidException("Cant restore document record: $foundDocument->id");
                    } else {
                        $foundDocument->deleted_at = null;
                    }

                    $foundDocument->face_processed = $delete;
                    $foundDocument->pushChangedFields('face_processed', 'deleted_at');
                    $this->userDocuments->updateOrThrowModified($foundDocument);

                    $this->users->resetCloudSources($foundDocument->site_id, $foundDocument->user_id);
                });
            }
        } finally {
            $this->saveProgressRows($progressRows, $action);
        }

        return count($foundDocuments);
    }

    private function addToProgressRows(array &$progressRows, UserDocument $foundDocument, int $action): void
    {
        $k = $foundDocument->site_id . '-' . $foundDocument->user_id;
        if (!array_key_exists($k, $progressRows)) {
            $progressRows[$k] = [
                'site_id' => $foundDocument->site_id,
                'user_id' => $foundDocument->user_id,
                'details' => [],
            ];
        }
        $progressRows[$k]['details'][] = UserDocumentProgress::userDocumentDetailOperations($foundDocument, $action);
    }

    private function saveProgressRows(array $progressRows, int $action): void
    {
        if (empty($progressRows)) {
            return;
        }
        foreach ($progressRows as $progressRow) {
            $this->userDocumentProgressesRepo->insertAfterAddOrDelete([
                ...$progressRow,
                'action' => $action,
                'source' => UserDocumentProgress::SOURCE_ANALYTICS,
                'created_by' => $this->authAccess->employeeId()
            ]);
        }
    }
}
