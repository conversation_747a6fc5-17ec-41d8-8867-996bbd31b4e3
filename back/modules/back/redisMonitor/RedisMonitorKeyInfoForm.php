<?php

declare(strict_types=1);

namespace app\back\modules\back\redisMonitor;

use app\back\components\Form;
use app\back\components\helpers\DateHelper;
use app\back\components\PersistentClient;
use app\back\components\validators\StringValidator;

class RedisMonitorKeyInfoForm
{
    use Form;

    #[StringValidator(1, 10000)]
    private string $key;

    public function __construct(
        private readonly PersistentClient $redis
    ) {
    }

    public function info(): array
    {
        $type = (string)$this->redis->type($this->key);

        $info = [];

        switch ($type) {
            case RedisMonitorForm::T_STREAM:
                $info['groups'] = $this->redis->xInfoGroups($this->key, true);
                $info['stream'] = $this->redis->xInfoStream($this->key);
                break;
            case RedisMonitorForm::T_STRING:
                $info['ttl'] = $this->redis->ttl($this->key);
                $info['expiresAt'] = \DateTimeImmutable::createFromFormat('U', (string)$this->redis->EXPIRETIME($this->key))->format(DateHelper::DATETIME_FORMAT_PHP);
                break;
            default:
        }

        return $this->redis->decodeJsonRecursive($info);
    }
}
