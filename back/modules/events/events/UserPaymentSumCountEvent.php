<?php

declare(strict_types=1);

namespace app\back\modules\events\events;

use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\entities\UserTransaction;
use app\back\modules\events\rowDatas\BaseEventDataRow;
use app\back\modules\events\rowDatas\UserTransactionRow;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

class UserPaymentSumCountEvent extends BaseEvent
{
    use DbAwareTrait;

    #[IntInArrayValidator(UserTransaction::OPERATIONS)]
    public int $op_id;
    /** @deprecated RENAME TO amount */
    #[IntValidator(PHP_INT_MIN)]
    public ?int $summ_rub_min = null;
    /** @deprecated RENAME TO amount */
    #[IntValidator(PHP_INT_MIN)]
    public ?int $summ_rub_max = null;
    /** @deprecated RENAME TO amount */
    #[IntValidator(PHP_INT_MIN)]
    public ?int $summ_min = null;
    /** @deprecated RENAME TO amount */
    #[IntValidator(PHP_INT_MIN)]
    public ?int $summ_max = null;
    #[IntValidator(PHP_INT_MIN)]
    public ?int $count_min = null;
    #[IntValidator(PHP_INT_MIN)]
    public ?int $count_max = null;

    /** @param UserTransactionRow[] $rows */
    protected function beforeFilterRows(array $rows): array
    {
        $this->appendSumCountMatch($rows);

        return $rows;
    }

    private function appendSumCountMatch(array &$rows): void
    {
        $inTable = $this->createUniqueValuesTable($rows, [
            'site_id' => 'int',
            'user_id' => 'int',
        ]);

        $sumCountRows = (new Query($this->db))
            ->select([
                'i.site_id',
                'i.user_id',
                'is_sum_count_match' => '(true)',
            ])
            ->from($inTable)
            ->leftJoin(['us' => UserTransactions::TABLE_NAME], ['AND',
                'us.site_id = i.site_id',
                'us.user_id = i.user_id',
                [
                    'us.op_id' => $this->op_id,
                    'us.status' => UserTransaction::STATUS_SUCCESS,
                ],
            ])
            ->groupBy(['i.site_id', 'i.user_id'])
            ->andFilterHaving(['>=', 'SUM(amount_rub)', $this->summ_rub_min])
            ->andFilterHaving(['<', 'SUM(amount_rub)', $this->summ_rub_max])
            ->andFilterHaving(['>=', 'SUM(amount_usd)', $this->summ_min])
            ->andFilterHaving(['<', 'SUM(amount_usd)', $this->summ_max])
            ->andFilterHaving(['>=', 'COUNT(*)', $this->count_min])
            ->andFilterHaving(['<', 'COUNT(*)', $this->count_max])
            ->all();

        $this->appendRowsByKey($rows, $sumCountRows, ['site_id', 'user_id'], ['is_sum_count_match']);
    }

    /** @param UserTransactionRow $row */
    public function filterOne(BaseEventDataRow $row): bool
    {
        if ($row->status !== UserTransaction::STATUS_SUCCESS) {
            return false;
        }

        if ($row->op_id !== $this->op_id) {
            return false;
        }

        if (!$row->is_sum_count_match) {
            return false;
        }

        return true;
    }

    public function uniqueKeyColumns(): array
    {
        return ['site_id', 'user_id'];
    }

    public static function getRowClassName(): string
    {
        return UserTransactionRow::class;
    }

    public static function isOneTimeEvent(): bool
    {
        return true;
    }

    public function getResultColumns(): array
    {
        return ['site_id', 'user_id'];
    }
}
