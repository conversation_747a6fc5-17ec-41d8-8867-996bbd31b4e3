<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Json;
use app\back\components\MaskGenerator;
use app\back\entities\UserBlock;
use app\back\entities\UserCloudSource;
use app\back\entities\UserIgnoreId;
use app\back\repositories\Sites;
use app\back\repositories\SocialNets;

class CidVisNodeFactory
{
    private const int ICON_DEFAULT_SIZE = 13;

    public const string TYPE_HIDDEN = '';
    public const string TYPE_USER = 'u';
    public const string TYPE_USER_TOXIC = 'ut';
    public const string TYPE_REQUISITE = 'r';
    public const string TYPE_REQUISITE_OTHER = 'ro';
    public const string TYPE_REQUISITE_QIWI = 'rq';
    public const string TYPE_REQUISITE_CC = 'rcc';
    public const string TYPE_REQUISITE_WM = 'rwm';
    public const string TYPE_REQUISITE_IBAN = 'iban';
    public const string TYPE_EMAIL = 'm';
    public const string TYPE_WM_ID = 'wm';
    public const string TYPE_FACE = 'fs';
    public const string TYPE_SOCIAL = 's';
    public const string TYPE_SOCIAL_VK = 's-vk';
    public const string TYPE_SOCIAL_ODNOKLASNIKI = 's-ok';
    public const string TYPE_SOCIAL_MAIL_RU = 's-mr';
    public const string TYPE_SOCIAL_FACEBOOK = 's-fb';
    public const string TYPE_SOCIAL_YANDEX = 's-ya';
    public const string TYPE_SOCIAL_GOOGLE = 's-goo';
    public const string TYPE_SOCIAL_OTHER = 's-o';
    public const string TYPE_REQ_ATTR_CHARGEBACK = 'r-cb';
    public const string TYPE_REQ_ATTR_BLOCKED = 'r-b';
    public const string TYPE_REQ_ATTR_PREV_BLOCKED = 'r-pb';
    public const string TYPE_USER_ATTR_VIP = 'vip';
    public const string TYPE_USER_ATTR_CONTACTED = 'contacted';
    public const string TYPE_USER_ATTR_BLOCKED = 'blocked';
    public const string TYPE_USER_ATTR_TEST = 'test';
    public const string TYPE_USER_ATTR_VERIFIED = 'verified';
    public const string TYPE_USER_ATTR_BONUS_BLACK_LIST = 'bonus-black-list';
    public const string TYPE_USER_SPECIAL_SOCIAL_EMAIL = 'se';
    public const string TYPE_USER_SPECIAL_ALT_EMAIL = 'ae';
    public const string TYPE_INACTIVE_EMAIL = 'ie';
    public const string TYPE_TAX_NUMBER = 'tn';

    public array $wmInfo;
    public array $reqInfo;
    public array $faceNames;
    private bool $showEmails;
    private bool $showRequisites;
    private bool $showPersonalData;
    private array $allowedSites;
    private CidVisNode $nodeBuilder;
    private MaskGenerator $maskGenerator;

    public function __construct(
        private readonly SocialNets $socialNetsRepo,
        private readonly Sites $sitesRepo,
        BaseAuthAccess $authAccess,
        AllowedLists $allowedLists,
    ) {
        $this->nodeBuilder = new CidVisNode();
        $this->maskGenerator = new MaskGenerator();

        $this->showEmails = $authAccess->canViewEmails();
        $this->showRequisites = $authAccess->canViewRequisites();
        $this->showPersonalData = $authAccess->canViewPersonalData();
        $this->allowedSites = $allowedLists->sites();
    }

    public function sourceNode(int $sourceTypeId, string $value, array $siteUsers): array
    {
        $nodeType = $this->sourceType($sourceTypeId, $value);
        $this->buildSourceNode($nodeType, $value, $siteUsers);
        $this->nodeBuilder->setLinks($this->sourceLinks($nodeType, $value, $siteUsers));
        return $this->nodeBuilder->data();
    }

    public function clusterNode(int $sourceTypeId, string $value, array $siteUsers): array
    {
        $nodeType = $this->sourceType($sourceTypeId, $value);
        $this->buildSourceNode($nodeType, $value, $siteUsers);
        $this->nodeBuilder->setLinks($this->clusterLinks($nodeType, $value));

        if ($nodeType !== self::TYPE_WM_ID) {
            $this->nodeBuilder->setUsersIds(implode(',', $this->maskUsersIds($siteUsers)));
        }

        return $this->nodeBuilder->data();
    }

    private function buildSourceNode(string $type, string $value, array $siteUsers): void
    {
        $this->nodeBuilder->reset();

        if (!$this->isAnySiteUserAllowed($siteUsers)) {
            $this->buildHiddenNode(self::formatFullKey($type, $value));
            return;
        }

        $size = $this->iconSize($type, $value);
        $this->nodeBuilder
            ->setKey(self::formatFullKey($type, $value))
            ->setType($type)
            ->setInfo($this->sourceInfo($type, $value))
            ->setLabel($this->sourceLabel($type, $value))
            ->setCopyData($this->sourceCopyData($type, $value))
            ->setSize($size);

        if ($type === self::TYPE_FACE) {
            $this->nodeBuilder->setFaceId($value);
        }

        foreach ($this->sourceAttributes($type, $value) as $attrType => $attrDescription) {
            $this->nodeBuilder->addAttribute(self::formatFullKey($attrType, $value), [
                'i' => $attrDescription,
                's' => $size,
                't' => $attrType,
            ]);
        }
    }

    public function userNode(array $u): array
    {
        $this->nodeBuilder->reset();
        $userKey = self::userKey($u['site_id'], $u['user_id']);

        if (!isset($this->allowedSites[$u['site_id']])) {
            return $this->buildHiddenNode($userKey)->data();
        }

        $this->nodeBuilder
            ->setKey($userKey)
            ->setType($u['toxic'] ? self::TYPE_USER_TOXIC : self::TYPE_USER)
            ->setLabel($u['site_user_brand'])
            ->setSize($u['size'] ?? static::ICON_DEFAULT_SIZE)
            ->setSiteId($u['site_id'])
            ->setBrandId($u['brand_id'])
            ->setCid($u['cid'])
            ->setInfo($this->userInfo($u));

        foreach ($this->userAttributes($u) as $attrType => $attrData) {
            $this->nodeBuilder->addAttribute(self::formatFullKey($attrType, $u['site_user']), [
                ...$attrData,
                's' => static::ICON_DEFAULT_SIZE,
                't' => $attrType,
            ]);
        }

        foreach ($u['social_email'] as $socEmail) {
            $this->nodeBuilder->addAttribute(self::formatFullKey(self::TYPE_USER_SPECIAL_SOCIAL_EMAIL, $socEmail), [
                'i' => 'Social email: ' . $this->maskEmail($socEmail),
                's' => static::ICON_DEFAULT_SIZE,
                't' => self::TYPE_USER_SPECIAL_SOCIAL_EMAIL,
            ]);
        }

        if (!empty($u['alt_email'])) {
            $this->nodeBuilder->addAttribute(self::formatFullKey(self::TYPE_USER_SPECIAL_ALT_EMAIL, $u['alt_email']), [
                'i' => 'Alt email: ' . $this->maskEmail($u['alt_email']),
                's' => static::ICON_DEFAULT_SIZE,
                't' => self::TYPE_USER_SPECIAL_ALT_EMAIL,
            ]);
        }

        if (!empty($u['inactive_emails'])) {
            foreach (Json::decode($u['inactive_emails']) as $email) {
                $this->nodeBuilder->addAttribute(self::formatFullKey(self::TYPE_INACTIVE_EMAIL, $email), [
                    'i' => 'Inactive email: ' . $this->maskEmail($email),
                    's' => static::ICON_DEFAULT_SIZE,
                    't' => self::TYPE_INACTIVE_EMAIL,
                ]);
            }
        }

        if (!empty($u['sources_links'])) {
            $this->nodeBuilder->setLinks($u['sources_links']);
        }

        return $this->nodeBuilder->data();
    }

    private function userAttributes(array $u): array
    {
        $attrs = [];
        if ($u['is_vip']) {
            $attrs[self::TYPE_USER_ATTR_VIP] = ['i' => 'VIP user'];
        }
        if ($u['contact_multi_account']) {
            $attrs[self::TYPE_USER_ATTR_CONTACTED] = ['i' => 'Contacted user'];
        }
        if ($u['is_blocked']) {
            $attrs[self::TYPE_USER_ATTR_BLOCKED] = [
                'i' => 'Blocked user',
                'l' => $this->getBlockReasonLabel($u),
            ];
        }
        if ($u['is_ignore']) {
            $source = UserIgnoreId::getSourceNameById($u['ignore_source']);
            $attrs[self::TYPE_USER_ATTR_TEST] = [
                'i' => "Ignored user (since {$u['ignore_at']}). Source: $source. Comment: {$u['ignore_comment']}"
            ];
        }
        if ($u['document_verified']) {
            $attrs[self::TYPE_USER_ATTR_VERIFIED] = ['i' => 'Verified user'];
        }
        if ($u['bonus_black_list']) {
            $attrs[self::TYPE_USER_ATTR_BONUS_BLACK_LIST] = ['i' => 'Bonus black list'];
        }

        return $attrs;
    }

    private function getBlockReasonLabel(array $u): ?string
    {
        if (!isset($u['block_source'])) {
            return null;
        }

        return '<span class="text-nowrap">' . implode('<br/>', [
            'Blocked by: ' . ($u['block_by_operator_email'] ?? '-'),
            'updated at: ' . ($u['block_updated_at'] ?? '-'),
            'source: ' . (UserBlock::SOURCES[$u['block_source']] ?? '-'),
            'reason: ' . (UserBlock::REASONS[$u['block_reason']] ?? '-'),
            'comment: ' . strip_tags($u['block_comment'] ?? '-'),
        ]) . '</span>';
    }

    private function buildHiddenNode(string $key): CidVisNode
    {
        return $this->nodeBuilder
            ->setKey($key)
            ->setInfo('N/A')
            ->setSize(static::ICON_DEFAULT_SIZE)
            ->setType(static::TYPE_HIDDEN);
    }

    private function sourceInfo(string $type, string $value): string
    {
        switch ($type) {
            case self::TYPE_REQUISITE_QIWI:
            case self::TYPE_REQUISITE_CC:
            case self::TYPE_REQUISITE_WM:
            case self::TYPE_REQUISITE_IBAN:
            case self::TYPE_REQUISITE_OTHER:
                $info = "S2P req: {$this->maskRequisite($value)}";
                if (!empty($this->reqInfo[$value])) {
                    $info .= sprintf(
                        ' %s, IN: %d, OUT: %d, MIN(D): %s, MAX(D): %s',
                        $this->reqInfo[$value]['pay_sys_codes'] ?? '-',
                        $this->reqInfo[$value]['in'] ?? 0,
                        $this->reqInfo[$value]['out'] ?? 0,
                        $this->reqInfo[$value]['min_date'] ?? '-',
                        $this->reqInfo[$value]['max_date'] ?? '-'
                    );
                }
                return $info;
            case self::TYPE_WM_ID:
                $info = "WMID: {$value}";

                if (isset($this->wmInfo[$value])) {
                    if (!empty($this->wmInfo[$value]['region'])) {
                        $info .= ", {$this->wmInfo[$value]['region']}";
                    }
                    if (!empty($this->wmInfo[$value]['registered_at'])) {
                        $info .= "; Registered {$this->wmInfo[$value]['registered_at']}";
                    }
                }

                $info .= "<img src='https://bl.wmtransfer.com/img/bl/{$value}' alt='BL'>";
                return $info;
            case self::TYPE_FACE:
                return "Face Id: {$value}";
            case self::TYPE_TAX_NUMBER:
                return $value;
            case self::TYPE_EMAIL:
                return "Email: {$this->maskEmail($value)}";
            case self::TYPE_SOCIAL_OTHER:
            case self::TYPE_SOCIAL_GOOGLE:
            case self::TYPE_SOCIAL_YANDEX:
            case self::TYPE_SOCIAL_FACEBOOK:
            case self::TYPE_SOCIAL_MAIL_RU:
            case self::TYPE_SOCIAL_ODNOKLASNIKI:
            case self::TYPE_SOCIAL_VK:
                [$socialId, $externalSocialId] = explode(':', $value);
                $socialName = $this->socialNetsRepo->getNameById($socialId);
                return "Social: {$this->maskSocialId("{$socialName}:{$externalSocialId}")}";
            default:
                return "Illegal source: {$type}";
        }
    }

    private function userInfo(array $u): string
    {
        $info = array_filter([
            'User' => $u['site_user'],
            'brand' => $u['brand_name'],
            'reg date' => $u['reg_date'],
            'last dep' => $u['last_dep_date'],
            'last login' => $u['last_login_date'],
            'last spin' => $u['last_spin_date'],
            'in/out $' => number_format((float)$u['dep_lt_usd'], 2, thousands_separator: ' ') . ' / ' . number_format((float)$u['wd_lt_usd'], 2, thousands_separator: ' '),
        ]);

        return implode(', ', array_map(static fn($key, $value) => "$key: $value", array_keys($info), $info));
    }

    private function sourceLabel(string $type, string $value): ?string
    {
        switch ($type) {
            case self::TYPE_REQUISITE_CC:
            case self::TYPE_REQUISITE_QIWI:
            case self::TYPE_REQUISITE_WM:
            case self::TYPE_REQUISITE_OTHER:
                $labelChunks[] = $this->maskRequisite($value);

                if (!empty($this->reqInfo[$value])) {
                    if (!empty($this->reqInfo[$value]['card_holders'])) {
                        $labelChunks[] = $this->reqInfo[$value]['card_holders'];
                    }
                    if (!empty($this->reqInfo[$value]['block_reason'])) {
                        $labelChunks[] = $this->reqInfo[$value]['block_reason'];
                    }
                }

                if (!empty($labelChunks)) {
                    return implode('<br/>', $labelChunks);
                }
                return null;
            case self::TYPE_WM_ID:
                return "WMID {$value}";
            case self::TYPE_FACE:
                $nobr = static fn($v) => "<i style='white-space: nowrap'>{$v}</i>";
                $fullNames = explode("\n", $this->faceNames[$value] ?? '');
                return implode('<br>', array_map($nobr, [
                    ...["Face ID: {$value}"],
                    ...$fullNames
                ])) ?: null;
            default:
                return null;
        }
    }

    private function sourceCopyData(string $type, string $value): ?string
    {
        if (self::isRequisite($type)) {
            return $this->maskRequisite($value);
        }

        if ($type === self::TYPE_FACE) {
            return $value;
        }

        return null;
    }

    private function iconSize(string $type, string $value): int
    {
        if (static::isRequisite($type)) {
            return $this->reqInfo[$value]['size'] ?? static::ICON_DEFAULT_SIZE;
        }
        return static::ICON_DEFAULT_SIZE;
    }

    private function sourceLinks(string $type, string $val, array $siteUsers)
    {
        if ($type === self::TYPE_WM_ID && !empty($this->wmInfo[$val]['links_to'])) {
            return $this->wmInfo[$val]['links_to'];
        }

        return array_map(static fn($siteUser) => self::userKey(...$siteUser), $siteUsers);
    }

    private function clusterLinks(string $type, string $val)
    {
        if ($type === self::TYPE_WM_ID && !empty($this->wmInfo[$val]['links_to'])) {
            return $this->wmInfo[$val]['links_to'];
        }

        return [];
    }

    private function sourceAttributes(string $type, string $value): array
    {
        if (!in_array($type, [self::TYPE_REQUISITE_CC, self::TYPE_REQUISITE_QIWI, self::TYPE_REQUISITE_WM, self::TYPE_REQUISITE_OTHER])) {
            return [];
        }
        $attrs = [];
        if ($this->reqInfo[$value]['block_status'] ?? false) {
            if ($this->reqInfo[$value]['block_status'] === 'blocked') {
                $blockReason = isset($this->reqInfo[$value]['block_reason']) ? " ({$this->reqInfo[$value]['block_reason']})" : null;
                $attrs[self::TYPE_REQ_ATTR_BLOCKED] = 'Blocked' . $blockReason;
            } else {
                $attrs[self::TYPE_REQ_ATTR_PREV_BLOCKED] = 'Prev blocked';
            }
        }
        if ($this->reqInfo[$value]['chargeback_first_at'] ?? false) {
            $attrs[self::TYPE_REQ_ATTR_CHARGEBACK] = "Chargeback" .
                " first: {$this->reqInfo[$value]['chargeback_first_at']}" .
                " last: {$this->reqInfo[$value]['chargeback_last_at']}" .
                " sum: \${$this->reqInfo[$value]['chargeback_sum_usd']}";
        }

        return $attrs;
    }

    private function maskUsersIds(array $siteUsers): array
    {
        $ids = [];
        foreach ($siteUsers as [$siteId, $userId]) {
            if (isset($this->allowedSites[$siteId])) {
                $ids[] = $this->sitesRepo->getShortNameById($siteId) . '-' . $userId;
            } else {
                $ids[] = static::userKey($siteId, $userId);
            }
        }
        return $ids;
    }

    private function maskRequisite(string $req): string
    {
        return $this->showRequisites ? $req : $this->maskGenerator->mask($req);
    }

    private function maskEmail(string $email): string
    {
        return $this->showEmails ? $email : $this->maskGenerator->maskEmail($email);
    }

    private function maskSocialId(string $socialId): string
    {
        return $this->showPersonalData ? $socialId : $this->maskGenerator->maskSocialId($socialId);
    }

    private function isAnySiteUserAllowed(array $siteUsers): bool
    {
        foreach (array_column($siteUsers, 0) as $siteId) {
            if (isset($this->allowedSites[$siteId])) {
                return true;
            }
        }
        return false;
    }

    private function sourceType($type, $value): string
    {
        switch ($type) {
            case UserCloudSource::TYPE_EMAIL:
                return self::TYPE_EMAIL;
            case UserCloudSource::TYPE_SOCIAL:
                return match ($this->socialNetsRepo->getNameById(explode(':', $value)[0])) {
                    'vkontakte' => self::TYPE_SOCIAL_VK,
                    'odnoklassniki' => self::TYPE_SOCIAL_ODNOKLASNIKI,
                    'mailru' => self::TYPE_SOCIAL_MAIL_RU,
                    'facebook' => self::TYPE_SOCIAL_FACEBOOK,
                    'yandex' => self::TYPE_SOCIAL_YANDEX,
                    'google', 'googleplus' => self::TYPE_SOCIAL_GOOGLE,
                    default => self::TYPE_SOCIAL_OTHER,
                };
            case UserCloudSource::TYPE_REQUISITE:
                if (str_starts_with($value, 'bn:')) {
                    return self::TYPE_REQUISITE_IBAN;
                }
                if (preg_match('/^\+\d+$/', $value)) {
                    return self::TYPE_REQUISITE_QIWI;
                }
                if (preg_match('/^\d+\*+/', $value)) {
                    return self::TYPE_REQUISITE_CC;
                }
                if (preg_match('/^[a-z]\d{12}$/i', $value)) {
                    return self::TYPE_REQUISITE_WM;
                }
                return self::TYPE_REQUISITE_OTHER;
            case UserCloudSource::TYPE_WM_ID:
                return self::TYPE_WM_ID;
            case UserCloudSource::TYPE_FACE:
                return self::TYPE_FACE;
            case UserCloudSource::TYPE_TAX_NUMBER:
                return self::TYPE_TAX_NUMBER;
            default:
                return self::TYPE_HIDDEN;
        }
    }

    private static function isRequisite(string $type): bool
    {
        return in_array($type, [
            self::TYPE_REQUISITE_OTHER,
            self::TYPE_REQUISITE_CC,
            self::TYPE_REQUISITE_WM,
            self::TYPE_REQUISITE_QIWI
        ]);
    }

    private static function isSocial(string $type): bool
    {
        return in_array($type, [
            self::TYPE_SOCIAL_VK,
            self::TYPE_SOCIAL_ODNOKLASNIKI,
            self::TYPE_SOCIAL_MAIL_RU,
            self::TYPE_SOCIAL_FACEBOOK,
            self::TYPE_SOCIAL_YANDEX,
            self::TYPE_SOCIAL_GOOGLE,
            self::TYPE_SOCIAL_OTHER,
        ]);
    }

    private static function userKey(int $siteId, int $userId): string
    {
        return self::formatFullKey(self::TYPE_USER, "{$siteId}-{$userId}");
    }

    private static function formatFullKey(string $type, string $uniqIdent): string
    {
        if (self::isRequisite($type)) {
            $type = self::TYPE_REQUISITE;
        } elseif (self::isSocial($type)) {
            $type = self::TYPE_SOCIAL;
        }
        return sprintf('%s-%s', $type, md5(mb_strtolower($uniqIdent)));
    }
}
