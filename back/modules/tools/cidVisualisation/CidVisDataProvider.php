<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Json;
use app\back\components\SecondaryConnection;
use app\back\entities\Requisite;
use app\back\entities\S2pAntifraudLog;
use app\back\entities\S2pOrder;
use app\back\entities\S2pTransaction;
use app\back\entities\User;
use app\back\entities\UserBlock;
use app\back\entities\UserCloudSource;
use app\back\entities\UserContact;
use app\back\entities\UserKyc;
use app\back\modules\task\actions\update\UsersCloudsSourcesTask;
use app\back\repositories\Brands;
use app\back\repositories\Requisites;
use app\back\repositories\S2pAntifraudLogs;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\S2pTransactions;
use app\back\repositories\Sites;
use app\back\repositories\UserBlockComments;
use app\back\repositories\UserBlocks;
use app\back\repositories\UserCidInfoIgnores;
use app\back\repositories\UserCloudSources;
use app\back\repositories\UserContacts;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentTextManualLabels;
use app\back\repositories\UserIgnoreIds;
use app\back\repositories\UserKycs;
use app\back\repositories\UserLogin4plays;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\Wmids;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class CidVisDataProvider
{
    protected array $allowedSitesIds;

    public function __construct(
        private readonly SecondaryConnection $db,
        private readonly BaseAuthAccess $authAccess,
        AllowedLists $allowedLists
    ) {
        $this->allowedSitesIds = array_keys($allowedLists->sites());
    }

    public function getCloudSourcesByCid(array $cloudId): \Generator
    {
        $sourcesReader = (new Query($this->db))
            ->select([
                's.type',
                's.val',
                's.key',
                'users' => 'jsonb_agg(ARRAY[s.site_id, s.user_id])'
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['s' => UserCloudSources::TABLE_NAME], 'u.site_id = s.site_id and u.user_id = s.user_id')
            ->leftJoin(['ii' => UserCidInfoIgnores::TABLE_NAME], 's.val = ii.val')
            ->where([
                'u.cid' => $cloudId,
                'ii.val' => null,
            ])
            ->groupBy(['s.type', 's.val', 's.key'])
            ->createCommand()
            ->query();

        foreach ($sourcesReader as $row) {
            $row['users'] = Json::decode($row['users']);
            yield $row;
        }
    }

    public function getCloudUsersByCid(array $cloudId): \Generator
    {
        $filterQuery = (new Query($this->db))
            ->select(['site_id', 'user_id'])
            ->from(Users::TABLE_NAME)
            ->where(['cid' => $cloudId]);

        foreach ($this->baseUsersQuery($filterQuery)->each() as $u) {
            $u['social_email'] = empty($u['social_email']) ? [] : explode(',', $u['social_email']);
            yield $u;
        }
    }

    public function getUsersData(array $users): \Generator
    {
        $filterQuery = (new Query($this->db))
            ->select([
                'site_id' => '(u->>0)::integer',
                'user_id' => '(u->>1)::bigint',
            ])
            ->from(['u' => 'jsonb_array_elements((:site_users)::jsonb)'])
            ->addParams(['site_users' => Json::encode($users)]);

        $linksQuery = (new Query($this->db))
            ->select([
                'f.site_id',
                'f.user_id',
                'keys' => "STRING_AGG(DISTINCT links.key, ',')",
            ])
            ->from('f')
            ->innerJoin(
                ['links' => UserCloudSources::TABLE_NAME],
                'links.site_id = f.site_id AND links.user_id = f.user_id AND links.type != :type_wmid',
                ['type_wmid' => UserCloudSource::TYPE_WM_ID]
            )
            ->groupBy([
                'f.site_id',
                'f.user_id',
            ]);

        $usersQuery = $this->baseUsersQuery($filterQuery)
            ->withQuery($linksQuery, 'links')
            ->leftJoin('links', 'links.site_id = cu.site_id AND links.user_id = cu.user_id')
            ->addSelect(['sources_links' => 'links.keys']);

        foreach ($usersQuery->each() as $u) {
            $u['sources_links'] = empty($u['sources_links']) ? [] : explode(',', $u['sources_links']);
            $u['social_email'] = empty($u['social_email']) ? [] : explode(',', $u['social_email']);
            yield $u;
        }
    }

    private function baseUsersQuery(Query $filterQuery): Query
    {
        $brandNameExpr = Sites::brandNameIfNotMatchingSite();
        $siteUserBrandExpression = "(s.short_name || '-' || u.user_id || COALESCE(' ' || NULLIF($brandNameExpr, ''), ''))";
        $cloudUsersQuery = (new Query($this->db))
            ->select([
                'u.cid',
                'u.site_id',
                'u.user_id',
                'u.brand_id',
                'site_user' => "(s.short_name || '-' || u.user_id)",
                'brand_name' => $brandNameExpr,
                'site_user_brand' => $siteUserBrandExpression,
                'toxic' => 'COALESCE((COALESCE(usi.dep_lt_usd, 0) - usi.wd_lt_usd) < 0, FALSE)',
                'reg_date' => 'DATE(u.date)',
                'is_vip' => '(CASE WHEN u.personal_manager > 0 THEN TRUE ELSE FALSE END)',
                'u.is_blocked',
                'block_source' => 'ub.source',
                'block_updated_at' => 'COALESCE(ub.updated_at, ub.created_at)',
                'block_reason' => 'ub.reason',
                'block_comment' => 'ubc.comment',
                'block_by_operator_email' => 'ubo.email',
                'usi.dep_lt_count',
                'usi.dep_lt_usd',
                'usi.wd_lt_usd',
                'last_dep_date' => 'DATE(usi.dep_last_at)',
                'last_login_date' => 'DATE(usi.login_last_at)',
                'last_spin_date' => 'DATE(usi.spin_last_at)',
                'contact_multi_account' => 'usi.contact_multi_account',
                'document_verified' => '(kyc.kyc_status = :kyc_verified)',
                'is_ignore' => '(ui.user_id IS NOT NULL)',
                'ignore_comment' => 'ui.comment',
                'ignore_source' => 'ui.source',
                'ignore_at' => '(ui.created_at::date)',
                'bonus_black_list' => '(u.bbl_status != :bbl_status_no)',
                'social_email' => "(CASE WHEN ul4p.email != '' AND ii.val IS null THEN LOWER(ul4p.email) END)",
                'alt_email' => "u.alt_email",
            ])
            ->from('f')
            ->join('NATURAL JOIN', ['u' => Users::TABLE_NAME])
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id')
            ->leftJoin(['kyc' => UserKycs::TABLE_NAME], 'kyc.site_id = u.site_id AND kyc.user_id = u.user_id')
            ->leftJoin(['ui' => UserIgnoreIds::TABLE_NAME], 'ui.site_id = u.site_id AND ui.user_id = u.user_id')
            ->leftJoin(['ul4p' => UserLogin4plays::TABLE_NAME], 'ul4p.social_id = u.social_id AND ul4p.social_key = u.social_key')
            ->leftJoin(
                ['ub' => UserBlocks::TABLE_NAME],
                'ub.site_id = u.site_id AND ub.user_id = u.user_id AND ub.active AND ub.type = :login_block_type',
                [ ':login_block_type' => UserBlock::BLOCK_TYPE_LOGIN]
            )
            ->leftJoin(['ubc' => UserBlockComments::TABLE_NAME], 'ubc.id = ub.comment_id')
            ->leftJoin(['ubo' => Users::TABLE_NAME], 'ub.site_id = ubo.site_id AND ub.updated_by_user_id = ubo.user_id')
            ->leftJoin(['ii' => UserCidInfoIgnores::TABLE_NAME], 'ul4p.email IS NOT NULL AND ul4p.email = ii.val')
            ->leftJoin(['s' => Sites::TABLE_NAME], 'u.site_id = s.site_id')
            ->leftJoin(['b' => Brands::TABLE_NAME], 'u.brand_id = b.id')
            ->addParams([
                'kyc_verified' => UserKyc::KYC_VERIFIED,
                'bbl_status_no' => User::BONUS_BL_STATUS_NO,
            ]);

        $inactiveEmailsQuery = (new Query($this->db))
            ->select([
                'cu.site_id',
                'cu.user_id',
                'inactive_emails' => 'JSON_AGG(c.value)',
            ])
            ->from('cu')
            ->innerJoin(['c' => UserContacts::TABLE_NAME], [
                'AND',
                'cu.site_id = c.site_id',
                'cu.user_id = c.user_id',
                ['c.type' => UserContact::TYPE_EMAIL],
                UserContact::notTooShortEmailCondition('c'),
                [
                    /* @see UsersCloudsSourcesTask::getTempTableQueryUsersEmails() reversed condition */
                    'OR',
                    ['COALESCE(cu.dep_lt_count, 0)' => 0],
                    ['!=', 'c.status', UserContact::STATUS_ACTIVE],
                ],
            ])
            ->groupBy([
                'cu.site_id',
                'cu.user_id',
            ]);

        return (new Query($this->db))
            ->withQuery($filterQuery, 'f')
            ->withQuery($cloudUsersQuery, 'cu')
            ->withQuery($inactiveEmailsQuery, 'ie')
            ->select(['cu.*', 'ie.inactive_emails'])
            ->from('cu')
            ->leftJoin('ie', 'cu.site_id = ie.site_id AND cu.user_id = ie.user_id');
    }

    public function getRequisiteInfoByRequisite(string $requisiteValue): array
    {
        [$requisite, $token] = Requisites::splitRequisiteAndToken($requisiteValue);

        return $this->baseRequisiteInfoQuery()
            ->andWhere(['o.requisite' => $requisite, 'o.requisite_token' => $token])
            ->all();
    }

    public function getRequisiteInfoByCid(array $cid): array
    {
        return $this->baseRequisiteInfoQuery()
            ->innerJoin(['u' => Users::TABLE_NAME], 'o.site_id = u.site_id AND o.user_id = u.user_id')
            ->andWhere(['u.cid' => $cid])
            ->all();
    }

    private function baseRequisiteInfoQuery(): Query
    {
        $query = (new Query($this->db))
            ->select([
                'requisite' => "COALESCE(LOWER(o.requisite || COALESCE(:delim || o.requisite_token, '')), '[empty]')",
                'card_holders' => "STRING_AGG(DISTINCT o.card_holder, ',')",
                'in' => 'COUNT(*) FILTER (WHERE o.type = :in)',
                'out' => 'COUNT(*) FILTER (WHERE o.type = :out)',
                'min_date' => 'MIN(o.date)::date',
                'max_date' => 'MAX(o.date)::date',
                'chargeback_count' => 'COUNT(t.*)',
                'chargeback_sum_usd' => 'SUM(t.amount_usd)',
                'chargeback_first_at' => 'MIN(t.success_at)::date',
                'chargeback_last_at' => 'MAX(t.success_at)::date',
                'pay_sys_codes' => "STRING_AGG(DISTINCT ps.code, ',')",
                'count' => 'COUNT(*)', // count, min_count, max_count used for icons scaling and counted over whole data
                'min_count' => 'MIN(COUNT(*)) OVER ()',
                'max_count' => 'MAX(COUNT(*)) OVER ()',
            ])
            ->from(['o' => S2pOrders::TABLE_NAME])
            ->leftJoin(['t' => S2pTransactions::TABLE_NAME], ['AND', 't.order_id = o.id', [
                't.type' => S2pTransaction::TYPE_CHARGEBACK,
                't.status' => S2pTransaction::STATUS_SUCCESS
            ]])
            ->leftJoin(['ps' => S2pPaySystems::TABLE_NAME], 'o.pay_sys_id = ps.id')
            ->where(['AND',
                ['o.site_id' =>  $this->allowedSitesIds],
                ['OR', ['o.status' => S2pOrder::STATUS_SUCCESS], 't.id IS NOT NULL'], // Success orders or chargeback orders
            ])
            ->groupBy(['o.requisite', 'o.requisite_token'])
            ->addParams([
                'delim' => Requisites::CARD_HASH_DELIMITER,
                'in' => S2pOrder::TYPE_IN,
                'out' => S2pOrder::TYPE_OUT,
            ]);

        $logQuery = (new Query($this->db))
            ->select([
                'block_status' => new Expression("CASE WHEN l.action = :blocked THEN 'blocked' ELSE 'prev-blocked' END", ['blocked' => S2pAntifraudLog::ACTION_ADD]),
                'l.block_reason',
            ])
            ->from(['l' => S2pAntifraudLogs::TABLE_NAME])
            ->where('l.requisite = o.requisite')
            ->orderBy(['l.created_at' => SORT_DESC])
            ->limit(1);

        return $query
            ->addSelect([
                'block_status' => 'ANY_VALUE(lq.block_status)',
                'block_reason' => 'ANY_VALUE(lq.block_reason)',
            ])
            ->join('LEFT JOIN LATERAL', ['lq' => $logQuery], 'true')
            ->indexBy('requisite');
    }

    public function getSourceInfo(string $key): array
    {
        return (new Query($this->db))
            ->select(['site_id', 'user_id', 'type', 'val'])
            ->from(['ucs' => UserCloudSources::TABLE_NAME])
            ->where(['key' => $key])
            ->all();
    }

    public function getWmInfo(array $cloudId): array
    {
        $rows = (new Query($this->db))
            ->select([
                'r.wmid',
                'wm.region',
                'wm.registered_at',
                'links_to' => '(JSONB_AGG(DISTINCT ucs.key))',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = u.site_id AND o.user_id = u.user_id') //TODO: use S2pUsersRequisites instead of S2pOrders
            ->innerJoin(['r' => Requisites::TABLE_NAME], 'r.requisite = o.requisite')
            ->innerJoin(['ucs' => UserCloudSources::TABLE_NAME], 'ucs.type = :type_requisite AND ucs.val = r.requisite AND ucs.site_id = u.site_id AND ucs.user_id = u.user_id')
            ->leftJoin(['wm' => Wmids::TABLE_NAME], 'wm.id = r.wmid')
            ->where([
                'u.cid' => $cloudId,
                'o.status' => S2pOrder::STATUS_SUCCESS,
                'o.requisite_type' => S2pOrder::REQUISITE_TYPE_WALLET_WEBMONEY,
                'r.type' => Requisite::TYPE_WALLET_WEBMONEY,
            ])
            ->andWhere(['!=', 'r.wmid', ''])
            ->groupBy([
                'r.wmid',
                'wm.region',
                'wm.registered_at',
            ])
            ->addParams([
                'type_requisite' => UserCloudSource::TYPE_REQUISITE
            ])
            ->indexBy('wmid')
            ->all();

        foreach ($rows as &$row) {
            $row['links_to'] = Json::decode($row['links_to']);
        }
        return $rows;
    }

    public function getFacesFullNames(array $val): array
    {
        if (!$this->authAccess->can('/finance/documents')) {
            return [];
        }

        return (new Query($this->db))
            ->select([
                'full_names' => "string_agg(DISTINCT CASE u.locale WHEN 'ru' THEN COALESCE(REPLACE(udt.text_full, '\n', ' '), u.name) ELSE u.name END, '\n')",
                'udf.similar_id',
            ])
            ->from(['u' => Users::TABLE_NAME])
            ->innerJoin(['ucs' => UserCloudSources::TABLE_NAME], 'ucs.site_id = u.site_id AND ucs.user_id = u.user_id')
            ->innerJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'udf.similar_id = ucs.val::int')
            ->innerJoin(['udt' => UserDocumentTextManualLabels::TABLE_NAME], 'udt.user_document_id = udf.user_document_id')
            ->where([
                'u.cid' => $val,
                'u.site_id' => $this->allowedSitesIds,
                'ucs.type' => UserCloudSource::TYPE_FACE,
                'udt.approved' => true,
            ])
            ->groupBy(['udf.similar_id'])
            ->indexBy('similar_id')
            ->column();
    }
}
