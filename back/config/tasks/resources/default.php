<?php

declare(strict_types=1);

use app\back\config\tasks\Res;
use app\back\modules\task\actions\maintenance\{
    CheckPgVectorTask,
    CloudSourceResetTask,
    CopyUsersDeleteBrandTask,
    CopyUsersDocumentsTask,
    CopyUsersTask,
    RokeenteSessionsCleanTask,
    S2pOrdersCleanRequisiteIdTask,
    UsersDocumentsEtagDeduplicateTask,
    UsersDocumentsCleanOrphansTask,
    UsersDocumentsEtagFillTask,
    UsersDocumentsFileNotExistsDeleteTask,
    UsersRequisitesRemoveNotUsedTask};
use app\back\modules\task\actions\update\{
    CountriesDepsDistrTask,
    DeleteNotUsedLyraScenariosTask,
    DocumentsDeleteFromBinTask,
    LyraDeviceFinancialActivityStatTask,
    LyraStreamEntityDistributionStatTask,
    UsersDocumentsTextRecognizeTask,
    EmployeesPermissionsTask,
    EventsLogCleanTask,
    LyraFeatureSetStatTask,
    LyraGeoSumStatTask,
    LyraScenarioPredictionsTask,
    LyraScenarioStatTask,
    RefcodesRefTypeTask,
    RefcodesSourceSiteTask,
    RefcodesTsTask,
    RefcodesWebmasterTask,
    UseragentsTask,
    UsersGamesAggTask,
    UsersGamesPaymentsActivityTask};

return [
    Res::DEFAULT => [
        'check-pg-vector' => CheckPgVectorTask::class,
        'copy-users' => CopyUsersTask::class,
        'copy-users-delete-brand' => CopyUsersDeleteBrandTask::class,
        'copy-users-documents' => CopyUsersDocumentsTask::class,
        'delete-not-used-lyra-scenarios' => DeleteNotUsedLyraScenariosTask::class,
        'update-cloud-source-reset' => CloudSourceResetTask::class,
        'update-countries-deps-distr' => CountriesDepsDistrTask::class,
        'update-employees-permissions' => EmployeesPermissionsTask::class,
        'update-events-log-clean' => ['class' => EventsLogCleanTask::class, 'storeInterval' => '1 month'],
        'update-lyra-feature-set-stat' => LyraFeatureSetStatTask::class,
        'update-lyra-geo-sum-stat' => LyraGeoSumStatTask::class,
        'update-lyra-scenarios-predictions' => LyraScenarioPredictionsTask::class,
        'update-lyra-scenarios-stat' => LyraScenarioStatTask::class,
        'update-lyra-device-financial-activity-stat' => LyraDeviceFinancialActivityStatTask::class,
        'update-lyra-stream-entity-distribution' => LyraStreamEntityDistributionStatTask::class,
        'update-refcodes-ref-type' => RefcodesRefTypeTask::class,
        'update-refcodes-source-site' => RefcodesSourceSiteTask::class,
        'update-refcodes-ts' => RefcodesTsTask::class,
        'update-refcodes-webmaster' => RefcodesWebmasterTask::class,
        'update-rokeente-sessions-clean' => RokeenteSessionsCleanTask::class,
        'update-s2p-orders-clean-requisite-id' => S2pOrdersCleanRequisiteIdTask::class,
        'update-useragents' => UseragentsTask::class,
        'update-users-documents-clean-orphans' => UsersDocumentsCleanOrphansTask::class,
        'update-users-documents-delete-from-bin' => DocumentsDeleteFromBinTask::class,
        'update-users-documents-etag-deduplicate' => UsersDocumentsEtagDeduplicateTask::class,
        'update-users-documents-etag-fill' => UsersDocumentsEtagFillTask::class,
        'update-users-documents-file-not-exists-delete' => UsersDocumentsFileNotExistsDeleteTask::class,
        'update-users-documents-text-recognize' => UsersDocumentsTextRecognizeTask::class,
        'update-users-games-agg' => UsersGamesAggTask::class,
        'update-users-games-payments-activity' => UsersGamesPaymentsActivityTask::class,
        'update-users-requisites-remove-not-used' => UsersRequisitesRemoveNotUsedTask::class,
    ],
];
