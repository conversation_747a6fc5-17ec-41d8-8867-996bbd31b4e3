<?php

declare(strict_types=1);

use app\back\config\tasks\Res;

// Tasks configs per resource (site)
return [
    Res::ADS_FACEBOOK => [
        'ads-stats-facebook' => [
            'class' => \app\back\modules\task\actions\import\ads\AdsStatsFacebookApiTask::class,
            'dateFormat' => 'Y-m-d',
            'request' => [
                'class' => \app\back\modules\task\requests\AdsStatsFacebookApiRequest::class,
                'objectAsSingleRow' => true,
                'aliases' => [
                    'currency' => 'account_currency',
                    'account_id' => 'account_id',
                    'account_name' => 'account_name',
                    'creative_id' => 'ad_id',
                    'creative_name' => 'ad_name',
                    'set_id' => 'adset_id',
                    'set_name' => 'adset_name',
                    'campaign_id' => 'campaign_id',
                    'campaign_name' => 'campaign_name',
                    'clicks' => 'clicks',
                    'views' => 'impressions',
                    'spend_orig' => 'spend',
                    'date' => 'date_start',
                    'actions' => 'actions',
                ],
            ]
        ],
    ],
    Res::ADS_GOOGLE => [
        'ads-stats-google' => [
            'class' => \app\back\modules\task\actions\import\ads\AdsStatsGoogleTask::class,
            'dateFormat' => 'Y-m-d',
            'request' => [
                'class' => \app\back\modules\task\requests\AdsStatsGoogleRequest::class,
                'aliasesLocales' => [
                    'Day,Campaign status,Campaign' => [
                        'date' => 'Day',
                        //'Campaign status',
                        'campaign_name' => 'Campaign',
                        //'Budget name',
                        'currency' => 'Currency code',
                        //'Budget',
                        //'Budget type',
                        //'Status',
                        'views' => 'Impr.',
                        'clicks' => 'Clicks',
                        //'CTR',
                        //'Avg. CPC',
                        'spend' => 'Cost',
                        //'Cost / Install',
                        //'Cost / In-app action',
                        'installs' => 'Installs',
                        //'In-app actions',
                        'campaign_id' => 'Campaign ID',
                        //'Bid strategy type',
                        //'Exclusion',
                        //'country' => 'Location',
                        //'Bid strategy',
                        //'Campaign type',
                        //'Campaign subtype',
                        //'Target CPA',
                        //'Target ROAS',
                        //'Target Impression Share',
                        //'Max CPC Bid Limit for Target IS',
                        //'Location Goal for Target IS',
                    ],
                    'День,Статус кампании,Кампания' => [
                        'date' => 'День',
                        'campaign_name' => 'Кампания',
                        'currency' => 'Код валюты',
                        'views' => 'Показы',
                        'clicks' => 'Kлики',
                        'spend' => 'Стоимость',
                        'spend_alt' => 'Расходы',
                        'installs' => 'Количество установок',
                        'campaign_id' => 'Идентификатор кампании',
                    ],
                    'День,Статус кампанії,Кампанія' => [
                        'date' => 'День',
                        'campaign_name' => 'Кампанія',
                        'currency' => 'Код валюти',
                        'views' => 'Покази',
                        'clicks' => 'Кліки',
                        'spend' => 'Вартість',
                        'installs' => 'Встановлення',
                        'campaign_id' => 'Ідентифікатор кампанії',
                    ],
                ],
            ],
        ],
    ],
    Res::ADS_IRON => [
        'ads-stats-iron' => [
            'class' => \app\back\modules\task\actions\import\ads\AdsStatsIronTask::class,
            'dateFormat' => 'Y-m-d\TH:i:s.vP',
            'request' => [
                'class' => \app\back\modules\task\requests\AdsStatsIronRequest::class,
                'url' => 'advertisers/v2/reports?format=csv&count=10000&breakdowns=day,campaign,title,application,country,os,creative&metrics=impressions,clicks,installs,spend&startDate=:startDate&endDate=:endDate',
                'curlTimeout' => 300,
                'aliases' => [
                    'date' => 'date',
                    'campaign_id' => 'campaignId',
                    'campaign_name' => 'campaignName',
                    'target_name' => 'titleName',
                    'target_id' => 'titleBundleId',
                    'application_id' => 'applicationId',
                    'source' => 'applicationName',
                    'creative_id' => 'creativeId',
                    'creative_name' => 'creativeName',
                    'country' => 'country',
                    //'deviceType' => 'deviceType',
                    'platform' => 'os',
                    'views' => 'impressions',
                    'clicks' => 'clicks',
                    'installs' => 'installs',
                    'spend_usd' => 'spend',
                ],
            ]
        ],
    ],
    Res::ADS_UNITY => [
        'ads-stats-unity' => [
            'class' => \app\back\modules\task\actions\import\ads\AdsStatsUnityTask::class,
            'dateFormat' => 'Y-m-d',
            'request' => [
                'class' => \app\back\modules\task\requests\AdsStatsUnityRequest::class,
                'url' => 'advertise/stats/v2/organizations/:organization/reports/acquisitions?scale=day&metrics=views,clicks,installs,spend&breakdowns=creativePack,campaign,targetGame,platform,sourceAppId,country&start=:start&end=:end',
                'curlTimeout' => 600,
                'aliases' => [
                    'date' => 'timestamp',
                    'target_id' => 'target id',
                    //'target_store_id' => 'target store id',
                    'target_name' => 'target name',
                    'creative_id' => 'creative pack id',
                    'creative_name' => 'creative pack name',
                    'campaign_id' => 'campaign id',
                    'campaign_name' => 'campaign name',
                    'country' => 'country',
                    'platform' => 'platform',
                    'source' => 'source app id',
                    'views' => 'views',
                    'clicks' => 'clicks',
                    'installs' => 'installs',
                    'spend_usd' => 'spend',
                    //'cvr' => 'cvr',
                    //'ctr' => 'ctr',
                    //'ecpm' => 'ecpm',
                    //'cpi' => 'cpi',
                ],
            ]
        ],
    ],
    Res::ADS_VUNGLE => [
        'ads-stats-vungle' => [
            'class' => \app\back\modules\task\actions\import\ads\AdsStatsVungleTask::class,
            'dateFormat' => 'Y-m-d',
            'request' => [
                'class' => \app\back\modules\task\requests\AdsStatsVungleRequest::class,
                'url' => 'ext/adv/reports/spend?dimensions=date,country,platform,creative,site,campaign,platform,application&aggregates=views,clicks,installs,spend&start=:start&end=:end',
                'aliases' => [
                    'date' => 'date',
                    'target_id' => 'application id',
                    'target_name' => 'application name',
                    'campaign_id' => 'campaign id',
                    'campaign_name' => 'campaign name',
                    //'site_id' => 'site id',
                    'source' => 'site name',
                    'creative_id' => 'creative id',
                    'creative_name' => 'creative name',
                    'platform' => 'platform',
                    'country' => 'country',
                    'views' => 'views',
                    'clicks' => 'clicks',
                    'installs' => 'installs',
                    'spend_usd' => 'spend',
                ],
            ]
        ],
    ],
    Res::ADS_MINTEGRAL => [
        'ads-stats-mintegral' => [
            'class' => \app\back\modules\task\actions\import\ads\AdsStatsMintegralTask::class,
            'dateFormat' => 'Ymd',
            'request' => [
                'class' => \app\back\modules\task\requests\AdsStatsMintegralRequest::class,
                'host' => 'https://ss-api.mintegral.com',
                'url' => 'api/v2/reports/data',
                'aliases' => [
                    'date' => 'Date',
                    'offer_id' => 'Offer Id', // 204929
                    // 'offer_uuid' => 'Offer Uuid', // ss_Camille_26130_34088_RU_MP_Android_M_051023_N
                    'offer_name' => 'Offer Name', // Camille_26130_34088_RU_MP_Android_M_051023_N
                    // 'campaign_id' => 'Campaign Id', // 59805
                    // 'creative_id' => 'Creative Id',
                    // 'creative_name' => 'Creative Name', // LuckyCasino1280_720.jpg // STP-4706_9x16_pl.mp4 // Arrow_Racer.jpg
                    // 'ad_type' => 'Ad Type', // more_offer
                    'source' => 'Sub Id', // mtg1518524115
                    // 'package_name' => 'Package Name', // solitaire.patience.card.games.klondike.free
                    'country' => 'Location',
                    'currency' => 'Currency',
                    'views' => 'Impression',
                    'clicks' => 'Click',
                    'installs' => 'Conversion',
                    'spend' => 'Spend',
                    // 'Ecpm' => 'Ecpm', // spend/views*1000
                    // 'Cpc' => 'Cpc', // spend/installs
                    // 'Ctr' => 'Ctr', // clicks/views
                    // 'Cvr' => 'Cvr', // clicks/installs
                    // 'Ivr' => 'Ivr', // installs/views
                ],
            ]
        ],
    ],
];
