<?php

declare(strict_types=1);

namespace app\back\migrations;

class m240610_093802_drop_sgd_sites extends BaseMigration
{
    public function up(): void
    {
        $siteIdCondition = [
            'site_id' => [500, 501, 502],
        ];

        $this->db->createCommand()->delete('sites', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users_stats', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users_logins', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users_contacts', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users_special_info', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users_games_tokens', $siteIdCondition)->execute();
        $this->db->createCommand()->delete('users_wallets', $siteIdCondition)->execute();
    }
}
