<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250603_104653_s2p_antifraud_log_add_reason_column extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand()->addColumn('s2p_antifraud_log', 'block_reason', 'varchar(100)')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropColumn('s2p_antifraud_log', 'block_reason')->execute();
    }
}
