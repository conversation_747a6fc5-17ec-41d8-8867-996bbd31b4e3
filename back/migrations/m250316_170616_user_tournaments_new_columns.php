<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250316_170616_user_tournaments_new_columns extends BaseMigration
{
    public function safeUp(): void
    {
        $this->sql("ALTER TABLE users_tournaments ADD COLUMN currency varchar(10)");
        $this->sql("ALTER TABLE users_tournaments ADD COLUMN qualified boolean");
        $this->sql("ALTER TABLE users_tournaments ADD COLUMN total_bet_amount NUMERIC(20,2)");
        $this->sql("ALTER TABLE users_tournaments ADD COLUMN total_win_amount NUMERIC(20,2)");
        $this->sql("ALTER TABLE users_tournaments ADD COLUMN total_bet_count int");
        $this->sql("ALTER TABLE users_tournaments ADD COLUMN total_win_count int");
    }

    public function safeDown(): void
    {
        $this->sql("ALTER TABLE users_tournaments DROP COLUMN currency");
        $this->sql("ALTER TABLE users_tournaments DROP COLUMN qualified");
        $this->sql("ALTER TABLE users_tournaments DROP COLUMN total_bet_amount");
        $this->sql("ALTER TABLE users_tournaments DROP COLUMN total_win_amount");
        $this->sql("ALTER TABLE users_tournaments DROP COLUMN total_bet_count");
        $this->sql("ALTER TABLE users_tournaments DROP COLUMN total_win_count");
    }
}
