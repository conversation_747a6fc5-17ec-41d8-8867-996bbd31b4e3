<?php

declare(strict_types=1);

namespace app\back\migrations;

class m230911_125716_new_golden_gate_sites extends BaseMigration
{
    public function safeUp(): void
    {
        $this->db->createCommand()->insert('sites', [
            'site_id' => 81,
            'site_name' => 'GGate D1',
            'short_name' => 'GGD1',
            'group' => 'GGate',
        ])->execute();

        $this->db->createCommand()->insert('sites', [
            'site_id' => 82,
            'site_name' => 'GGate HD',
            'short_name' => 'GGHD',
            'group' => 'GGate',
        ])->execute();

        $this->db->createCommand()->insert('sites', [
            'site_id' => 83,
            'site_name' => 'GGate P1',
            'short_name' => 'GGP1',
            'group' => 'GGate',
        ])->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->delete('sites', ['site_id' => [81, 82, 83]])->execute();
    }
}
