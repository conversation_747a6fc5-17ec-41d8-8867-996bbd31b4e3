<?php

declare(strict_types=1);

namespace app\back\migrations;

class m231107_044835_ads_stats_iron_unique extends BaseMigration
{
    public function up(): void
    {
        $this->sql('DELETE FROM ads_stats WHERE platform_id = 2');
        $this->sql('CREATE UNIQUE INDEX CONCURRENTLY ads_stats_iron_unique ON ads_stats (date, campaign_id, target_id, country, ua_platform_id) WHERE (platform_id = 2)');
    }

    public function down(): void
    {
        $this->sql('DROP INDEX CONCURRENTLY ads_stats_iron_unique');
    }
}
