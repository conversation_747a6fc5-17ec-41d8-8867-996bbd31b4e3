<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\components\helpers\Json;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentFace;
use app\back\entities\UserDocumentFaceSimilarity;
use app\back\modules\task\actions\update\UsersDocumentsFaceRecognizeTask;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserDocumentFaceSimilarities extends BaseRepository
{
    public const string ENTITY_CLASS = UserDocumentFaceSimilarity::class;
    public const string TABLE_NAME = 'users_documents_faces_similarity';
    public const array PRIMARY_KEY = ['face_a_id', 'face_b_id'];

    public const float AUTO_MATCH_LIMIT_TOP = 0.98;

    private ?int $l2VectorIndexProbes = null;
    private static ?int $l2VectorIndexProbesStored = null;

    public static function isAutoApprove(float $distance, float $minFaceRatio, bool $isSimilarityIncludeBankCard): bool
    {
        return $distance <= self::AUTO_MATCH_LIMIT_TOP && $minFaceRatio > UserDocumentFaces::FACE_QUALITY_RATIO_LOW && !$isSimilarityIncludeBankCard;
    }

    public function findSimilar(UserDocumentFace $face): array
    {
        $maxFaceId = $face->id ?? null;
        $distance = '(f.face_embedding <-> (select embedding from face))';
        $query = (new Query($this->db))
            ->addParams([':face_embedding' => Json::encode($face->face_embedding->toArray())])
            ->withQuery('SELECT (:face_embedding)::vector(512) AS embedding', 'face')
            ->withQuery((new Query($this->db))
                ->select(['f.id', 'f.face_embedding', 'f.user_document_id', 'f.ratio', 'ud.tags', 'distance' => $distance])
                ->from(['fd' => UserDocumentsActive::TABLE_NAME])
                ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], '(fd.site_id, fd.user_id) = (ud.site_id, ud.user_id)')
                ->innerJoin(['f' => UserDocumentFaces::TABLE_NAME], 'f.user_document_id = ud.id')
                ->where([
                    'AND',
                    ['fd.id' => $face->user_document_id],
                    ['!=', 'ud.id', $face->user_document_id],
                    'f.inherited_face IS DISTINCT FROM TRUE'
                ])
                ->andFilterWhere(['<', 'f.id', $maxFaceId]), 'account_faces');

        if (isset($this->l2VectorIndexProbes)) {
            // index works guaranteed only with order by and limit and without where
            $this->refreshIvfflatIndexProbes();
            $query
                ->withQuery((new Query($this->db))
                    ->select(['id', 'face_embedding', 'user_document_id', 'ratio', 'number', 'distance' => $distance, 'inherited_face'])
                    ->from(['f' => UserDocumentFaces::TABLE_NAME])
                    ->orderBy([$distance => SORT_ASC])
                    ->limit(1000), 'sims_dirty')
                ->withQuery((new Query($this->db))
                    ->select(['sd.id', 'sd.face_embedding', 'sd.user_document_id', 'sd.ratio', 'ud.tags', 'distance'])
                    ->from(['sd' => 'sims_dirty'])
                    ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.id = sd.user_document_id')
                    ->where([
                        'AND',
                        ['!=', 'sd.user_document_id', $face->user_document_id],
                        ['<', 'sd.distance', UsersDocumentsFaceRecognizeTask::SIMILARLY_DISTANCE_THRESHOLD],
                        ['OR', ['>', 'sd.ratio', UserDocumentFaces::FACE_QUALITY_RATIO_LOW], ['sd.number' => 0]],
                        'sd.inherited_face IS DISTINCT FROM TRUE'
                    ])
                    ->andFilterWhere(['<', 'sd.id', $maxFaceId])
                    ->union((new Query($this->db))->from('account_faces')), 'sims_nearest');
        } else {
            $query
                ->withQuery((new Query($this->db))
                    ->select(['f.id', 'f.face_embedding', 'f.user_document_id', 'f.ratio', 'ud.tags', 'distance' => $distance])
                    ->from(['f' => UserDocumentFaces::TABLE_NAME])
                    ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.id = f.user_document_id')
                    ->where([
                        'AND',
                        ['!=', 'f.user_document_id', $face->user_document_id],
                        ['<', $distance, UsersDocumentsFaceRecognizeTask::SIMILARLY_DISTANCE_THRESHOLD],
                        ['OR', ['>', 'f.ratio', UserDocumentFaces::FACE_QUALITY_RATIO_LOW], ['f.number' => 0]],
                        'f.inherited_face IS DISTINCT FROM TRUE'
                    ])
                    ->andFilterWhere(['<', 'f.id', $maxFaceId])
                    ->union((new Query($this->db))->from('account_faces')), 'sims_nearest');
        }

        return $query
            ->select([
                'id',
                'user_document_id',
                'ratio',
                'distance',
                'is_bank_card' => new Expression('tags = ARRAY[:tag_bank_card]::VARCHAR[]', [':tag_bank_card' => UserDocument::TAG_BANK_CARD]),
            ])
            ->from('sims_nearest')
            ->orderBy(['distance' => SORT_ASC])
            ->indexBy('id')
            ->all();
    }

    public function setIvfflatIndexProbes(?int $probes): void
    {
        $this->l2VectorIndexProbes = max(0, $probes) ?: null;
    }

    protected function refreshIvfflatIndexProbes(): void
    {
        if (self::$l2VectorIndexProbesStored !== $this->l2VectorIndexProbes) {
            $this->db->createCommand('SET ivfflat.probes = ' . $this->l2VectorIndexProbes)->execute();
            self::$l2VectorIndexProbesStored = $this->l2VectorIndexProbes;
        }
    }
}
