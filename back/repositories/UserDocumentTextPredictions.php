<?php

declare(strict_types=1);

namespace app\back\repositories;

use app\back\entities\UserDocumentTextPrediction;
use app\back\repositories\views\UserDocumentsActive;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UserDocumentTextPredictions extends BaseRepository
{
    public const string ENTITY_CLASS = UserDocumentTextPrediction::class;
    public const string TABLE_NAME = 'users_documents_text_predictions';
    public const array PRIMARY_KEY = ['user_document_id', 'prediction_model_id'];

    public static function findAwaitApproveCount(ConnectionInterface $db): int
    {
        return (new Query($db))
            ->from(['udp' => self::TABLE_NAME])
            ->leftJoin(['udm' => UserDocumentTextManualLabels::TABLE_NAME], 'udp.user_document_id = udm.user_document_id')
            ->innerJoin(['ud' => UserDocumentsActive::TABLE_NAME], 'ud.id = udp.user_document_id')
            ->where(['udm.user_document_id' => null])
            ->count();
    }
}
