<?php

declare(strict_types=1);

namespace app\back\repositories\views;

use app\back\entities\UserGameTokenAgg;
use app\back\repositories\BaseRepository;

class UserGameTokenAggs extends BaseRepository
{
    use DbMaterializedViewHelper;

    public const string ENTITY_CLASS = UserGameTokenAgg::class;
    public const string TABLE_NAME = 'users_games_tokens_agg';
    public const array PRIMARY_KEY = ['site_id', 'day'];
}
