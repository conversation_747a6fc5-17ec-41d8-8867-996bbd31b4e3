<?php

declare(strict_types=1);

namespace app\back\components;

use app\back\components\helpers\Arr;
use app\back\repositories\PermissionSecuredDict;
use app\back\repositories\PermissionSecuredGroupedDict;

final class Permission
{
    public const string PERM_ALL = 'all';
    public const string PERM_PAGES = 'pages';
    public const string PERM_PERMISSIONS = 'permissions';

    public const string PERM_SITES = 'sites';
    public const string PERM_SITES_PREFIX = 'site-';
    public const string PERM_BRANDS = 'brands';
    public const string PERM_BRANDS_PREFIX = 'brand-';
    public const string PERM_S2P_PROJECTS = 's2p-projects';
    public const string PERM_S2P_PROJECTS_PREFIX = 's2p-project-';
    public const string PERM_TRAFFIC_SOURCES = 'traffic-sources';
    public const string PERM_TRAFFIC_SOURCE_PREFIX = 'traffic-source-';
    public const string PERM_SETTINGS = 'app-settings';
    public const string PERM_SETTING_PREFIX = 'app-setting-';
    public const string PERM_CHECKS_RULES = 'checks-rules';
    public const string PERM_CHECKS_RULE_PREFIX = 'checks-rule-';
    public const string PERM_CHECKS_GROUPS = 'checks-groups';
    public const string PERM_CHECKS_GROUP_PREFIX = 'checks-group-';
    public const string PERM_API_CLIENTS = 'api-clients';

    public const string PERM_VIEW_PERSONAL_DATA = 'view-personal-data';
    public const string PERM_VIEW_REQUISITES = 'view-requisites';
    public const string PERM_VIEW_EMAILS = 'view-emails';
    public const string PERM_VIEW_SOCIAL_PROFILES = 'view-social-profiles';
    public const string PERM_VIEW_PHONES = 'view-phones';
    public const string PERM_LISTEN_PHONE_CALLS = 'listen-phone-calls';
    public const string PERM_VIEW_TAX_NUMBER = 'view-tax-number';
    public const string PERM_VIEW_ADDRESS = 'view-address';

    public const string PERM_VIEW_S2P_MIDS = 'view-s2p-mids';
    public const string PERM_VIEW_S2P_PAY_CLASSES = 'view-s2p-pay-classes';
    public const string PERM_VIEW_S2P_TRUST = 'view-s2p-trust';
    public const string PERM_VIEW_S2P_TRUST_GROUPS = 'view-s2p-trust-groups';
    public const string PERM_VIEW_S2P_TRUST_SCORE = 'view-s2p-trust-score';
    public const string PERM_VIEW_S2P_ROUTING_BRANCH = 'view-s2p-routing-branch';

    public const string PERM_CHARTS = 'charts';
    public const string PERM_CHARTS_PANELS_PREFIX = 'chart-panel-';
    public const string PERM_CHARTS_EDIT = 'charts-edit';
    public const string PERM_CHARTS_CREATE = 'charts-create';

    public const string PERM_CONTACTS_MANAGE = 'contacts-manage';
    public const string PERM_CONTACTS_ADD = 'contacts-add';
    public const string PERM_CONTACTS_DELETE = 'contacts-delete';
    public const string PERM_CONTACTS_PRIORITY = 'contacts-priority';
    public const string PERM_CONTACTS_PRIVACY = 'contacts-privacy';
    public const string PERM_CONTACTS_SUBSCRIPTIONS = 'contacts-subscriptions';
    public const string PERM_CONTACTS_EMAIL_STATUS = 'contacts-email-status';
    public const string PERM_CONFIRM_CONTACTS = 'confirm-contacts';

    public const string PERM_CUSTOMER_VIEW = 'customer-view';
    public const string PERM_CUSTOMER_EDIT = 'customer-edit';
    public const string PERM_CUSTOMER_LINK = 'customer-link';
    public const string PERM_CUSTOMER_ATTR_ADD = 'customer-attr-add';
    public const string PERM_CUSTOMER_PHOTO = 'customer-photo';

    public const string PERM_USER_STATUS = 'user-status-edit';
    public const string PERM_USER_PM = 'user-pm-edit';
    public const string PERM_CHANGE_CRM_GROUP = 'user-crm-group-edit';

    public const string PERM_ASSIGN_ALL_PRIZES = 'assign-prizes';
    public const string PERM_ASSIGN_PRIZE_PREFIX  = 'assign-prize-';

    public const string PERM_CREATE_BONUS_OFFERS = 'create-bonus-offers';

    public const string PERM_WITHDRAW_OVER_LIMIT = 'withdraw-over-limit';
    public const string PERM_TEST_USERS_WITHDRAWAL = 'test-users-withdrawal';
    public const string PERM_ALLOW_DEP_MANUAL_SUCCESS = 'allow-dep-manual-success';

    public const string PERM_ADVANCED_BONUS_MODE = 'advanced-bonus-mode';
    public const string PERM_NO_BONUS_RESTRICTIONS = 'no-bonus-restrictions';
    public const string PERM_BONUS_RESTRICTION_PREFIX = 'bonus-restriction-';

    public const string PERM_VIEW_PLAYER_PREFIX = 'view-player-';
    public const string PERM_VIEW_PLAYER = 'view-player';

    public const string PERM_VIEW_CONVERSION_CPA = 'view-conversion-cpa';
    public const string PERM_VIEW_CONVERSION_LETTERS = 'view-conversion-letters';

    public const string PERM_USER_BONUS_BALANCE_RESET = 'user-bonus-balance-reset';

    public const string PERM_USER_MANAGER_TABS = 'user-manager-tabs';
    public const string PERM_USER_MANAGER_TAB_PREFIX = 'user-manager-tab-';

    public const string PERM_JIRA_APPROVE_LOST_DEPOSIT = 'jira-approve-lost-deposit';

    public const string PERM_VIEW_COMMUNICATIONS = 'view-communications';
    public const string PERM_VIEW_ADJUST_INFO = 'view-adjust-info';
    public const string PERM_VIEW_STAT_DATA = 'view-stats-data';
    public const string PERM_VIEW_AFF_HIDDEN = 'view-aff-hidden';

    public const string PERM_VIEW_SQL = 'view-sql';

    public const string PAGE_HELP = '/back/help';

    public const string PAGE_FACE_MATCHING = '/finance/face-matching';
    public const string PAGE_FACE_VALIDATION = '/finance/face-validation';
    public const string PAGE_DOCUMENTS = '/finance/documents';
    public const string PAGE_DOCUMENTS_KYC = '/finance/documents-kyc';

    public static function pagesToPermissions(array $pages, ?string $parentPermission = 'menu'): array
    {
        $result = [];
        foreach ($pages as $page) {
            $permissionName = null;
            if (array_key_exists('permission', $page)) {
                $permissionName = $parentPermission . '-' . $page['permission'];
            } elseif (isset($page['url'])) {
                $permissionName = $page['url'];
            } else {
                continue;
            }

            $perm = [
                'name' => $page['label'],
            ];

            if (array_key_exists('items', $page)) {
                $perm['children'] = self::pagesToPermissions($page['items'], $permissionName);
            }

            $result[$permissionName] = $perm;
        }

        return $result;
    }

    public static function assocToPermissions(array $items, string $prefix = ''): array
    {
        $result = [];

        foreach ($items as $id => $name) {
            $result[$prefix . $id] = [
                'name' => $name,
            ];
        }

        return $result;
    }

    public static function dict(PermissionSecuredDict $dict): array
    {
        return self::assocToPermissions($dict->permissionNames(), $dict->permissionPrefix());
    }

    public static function groupedDict(PermissionSecuredGroupedDict $dict): array
    {
        $result = [];

        foreach ($dict->permissionGroups() as $group => $childrenKeys) {
            $groupItems = Arr::leaveOnlyKeys($dict->permissionNames(), $childrenKeys);
            $result[$dict->permissionGroupPrefix() . mb_strtolower((string) $group)] = [
                'name' => $group,
                'children' => self::assocToPermissions($groupItems, $dict->permissionPrefix()),
            ];
        }

        return $result;
    }

    public static function getManagerPermissionNameByActionName(string $tabName): string
    {
        return self::PERM_USER_MANAGER_TAB_PREFIX . $tabName;
    }
}
