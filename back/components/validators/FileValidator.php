<?php

declare(strict_types=1);

namespace app\back\components\validators;

use Symfony\Component\HttpFoundation\File\UploadedFile;

#[\Attribute]
class FileValidator extends BaseValidator
{
    public function __construct(
        private readonly array $mimeTypes = [],
        private readonly ?int $maxSizeBytes = 10_000_000,
        private readonly int $maxCount = 1,
    ) {
    }

    public function validate(mixed $value, $form, array $context): ?string
    {
        if (!is_array($value) || count($value) === 0) {
            return 'is not valid file';
        }

        if (count($value) > $this->maxCount) {
            return "limit ({$this->maxCount}) exceeded";
        }

        foreach ($value as $file) {
            /** @var UploadedFile $file */

            $baseName = $file->getClientOriginalName();

            if ($file->isValid() === false) {
                return $file->getErrorMessage();
            }

            if (!empty($this->mimeTypes)) {
                $mimeType = $file->getMimeType();
                $allowedMimeTypes = static::callableToHaystack($this->mimeTypes, $form, $context);
                if (!in_array($mimeType, $allowedMimeTypes, true)) {
                    return "'$baseName' type ($mimeType) is not allowed";
                }
            }

            if ($this->maxSizeBytes !== null && $file->getSize() > $this->maxSizeBytes) {
                return "'$baseName' size above limit ($this->maxSizeBytes)";
            }
        }

        return null;
    }

    /**
     * @param mixed $value
     * @param $form
     * @return null|UploadedFile[]|UploadedFile
     */
    public function cast(mixed $value, $form): null|array|UploadedFile
    {
        if ($value === null) {
            return null;
        }

        return $this->maxCount === 1 ? $value[0] : $value;
    }
}
