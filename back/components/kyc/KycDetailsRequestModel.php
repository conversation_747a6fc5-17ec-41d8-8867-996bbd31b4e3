<?php

declare(strict_types=1);

namespace app\back\components\kyc;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Json;
use app\back\components\MaskGenerator;
use app\back\components\SecondaryConnection;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\UserDocumentProgress;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\CanonicalPaySystems;
use app\back\repositories\PaySystems;
use app\back\repositories\Requisites;
use app\back\repositories\S2pOrders;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

readonly class KycDetailsRequestModel
{
    public const array DOC_TYPES_REQUISITE_DEPENDENT = [
        ...self::DOC_TYPES_REQUISITE_DEPENDENT_WITHOUT_OTHER,
        UserDocumentProgress::TYPE_OTHER,
    ];
    public const array DOC_TYPES_REQUISITE_DEPENDENT_WITHOUT_OTHER = [
        UserDocumentProgress::TYPE_SELFIE_WITH_BANK_CARD,
        UserDocumentProgress::TYPE_BANK_STATEMENT,
        UserDocumentProgress::TYPE_BANK_SCREENSHOT,
        UserDocumentProgress::TYPE_BANK_CARD_FRONT,
        UserDocumentProgress::TYPE_BANK_CARD_BACK,
        UserDocumentProgress::TYPE_MOBILE_COMMERCE_CONTRACT,
        UserDocumentProgress::TYPE_WALLET_ACCOUNT_SCREENSHOT,
    ];
    public const array DOC_TYPES_DEFAULT = [
        UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD,
        UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD
    ];

    public function __construct(
        private SecondaryConnection $db,
        private MaskGenerator $maskGenerator,
        private BaseAuthAccess $authAccess,
    ) {
    }

    public function transactionInfoForDocumentsQuery(array $condition): Query
    {
        return (new Query($this->db))
            ->select([
                'transactions_data' => "jsonb_object_agg(us.transaction_id, jsonb_build_object(
                    'transaction_id', us.transaction_id,
                    'canonical_pay_system', cps.name,
                    'requisite', coalesce(o.requisite, r.requisite, us.wallet)
                ))",
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = us.site_id AND o.user_id = us.user_id AND o.invoice_id = us.transaction_id')
            ->leftJoin(['r' => Requisites::TABLE_NAME], 'r.id = us.requisite_id')
            ->leftJoin(['ps' => PaySystems::TABLE_NAME], 'ps.id = us.pay_sys_id')
            ->leftJoin(['pss' => CanonicalPaySySources::TABLE_NAME], 'pss.name = ps.name AND pss.source = :can_pay_source_product')
            ->leftJoin(['cps' => CanonicalPaySystems::TABLE_NAME], 'cps.id = pss.canonical_pay_sys_id')
            ->where($condition)
            ->addParams([':can_pay_source_product' => CanonicalPaySySource::SOURCE_PRODUCT]);
    }

    public function progressRequestDetailsForJoinQuery(string $progressTableAlias): Query
    {
        $progressTableAlias = $this->db->getQuoter()->quoteTableName($progressTableAlias);
        $query = $this->transactionInfoForDocumentsQuery([
            'AND',
            "us.site_id = $progressTableAlias.site_id",
            "us.user_id = $progressTableAlias.user_id",
            "us.transaction_id = ANY (ARRAY(SELECT jsonb_array_elements_text(jsonb_path_query_array($progressTableAlias.details, '$[*].transactionId'))))"
        ]);
        return $query->addSelect(['request_details' => "$progressTableAlias.details"]);
    }

    public function detailsDecorateDocsRequest(array $row, bool $forceShowComments): array
    {
        $detailsData = Json::decode($row['request_details'] ?: '[]');
        $transactionsData = Json::decode($row['transactions_data'] ?: '[]');
        return $this->kycRequestDetailsUniq($detailsData, $forceShowComments, $transactionsData);
    }

    private function kycRequestDetailsUniq(array $detailsList, bool $showComments, ?array $transactionsData): array
    {
        $showComments ??= $this->authAccess->canViewRequisites();
        $rows = array_map(function ($details) use ($showComments, $transactionsData) {
            $info = [implode(', ', array_map(static fn($r) => UserDocumentProgress::REASONS[$r] ?? $r ?? '-', $details[UserDocumentProgress::DOC_DETAIL_FIELD_REASONS] ?? []))];
            if ($showComments && !empty($details[UserDocumentProgress::DOC_DETAIL_FIELD_COMMENT])) {
                $info[] = $details[UserDocumentProgress::DOC_DETAIL_FIELD_COMMENT];
            }
            if (!empty($details[UserDocumentProgress::DOC_DETAIL_FIELD_REQUISITE]) && $this->authAccess->canViewRequisites()) {
                $info[] = implode(', ', (array)$details[UserDocumentProgress::DOC_DETAIL_FIELD_REQUISITE]);
            }
            if (isset($details[UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID], $transactionsData[$details[UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID]])) {
                $info[] = $this->transactionDataToMaskedViewString($transactionsData[$details[UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID]]);
            }
            if (isset($details[UserDocumentProgress::DOC_DETAIL_FIELD_REQUISITE_HINT])) {
                $info[] = $details[UserDocumentProgress::DOC_DETAIL_FIELD_REQUISITE_HINT];
            }

            return [...$info, UserDocumentProgress::TYPES[$details[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE] ?? null] ?? $details[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE] ?? '[empty type]'];
        }, $detailsList);

        $uniqueReasons = array_values(array_unique(array_column($rows, 0)));

        if (count($uniqueReasons) === 1 && count($rows) > 1) {
            return ["$uniqueReasons[0]:", ...array_map(static fn($info) => implode(' ', array_reverse(array_slice($info, 1))), $rows)];
        }

        return array_map(static fn($info) => array_pop($info) . ": " . implode(' ', array_reverse($info)), $rows);
    }

    public function transactionDataToMaskedViewString(array $data): string
    {
        return implode(' ', array_filter([
            "Tr. id: {$data['transaction_id']}",
            $data['canonical_pay_system'],
            $this->authAccess->canViewRequisites() ? $data['requisite'] : $this->maskGenerator->mask($data['requisite']),
        ]));
    }
}
