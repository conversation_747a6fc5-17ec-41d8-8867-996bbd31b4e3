<?php

declare(strict_types=1);

namespace app\back\components\kyc;

use app\back\components\helpers\Json;
use app\back\components\SecondaryConnection;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\repositories\UserDocuments;
use Yiisoft\Db\Query\Query;

readonly class KycDetailsWaitModel
{
    public function __construct(
        private SecondaryConnection $db,
    ) {
    }

    public function progressWaitDetailsForJoinQuery(string $pregressTableAlias): Query
    {
        $pregressTableAlias = $this->db->getQuoter()->quoteTableName($pregressTableAlias);

        return (new Query($this->db))
            ->select([
                'wait_details' => "jsonb_agg(jsonb_build_object(" .
                    "'type', ud.doc_type, " .
                    "'tags', ud.tags, " .
                    "'valid', ud.ai_validation->'valid', " .
                    "'messages', ud.ai_validation->'messages'" .
                    ") ORDER BY ud.ai_validation['valid']::boolean DESC NULLS LAST, ud.doc_type)",
            ])
            ->from(['ud' => UserDocuments::TABLE_NAME])
            ->where([
                'AND',
                ["$pregressTableAlias.action" => UserDocumentProgress::ACTION_STATUS_CHANGE],
                ["$pregressTableAlias.kyc_status" => [UserKyc::KYC_WAIT, UserKyc::KYC_WAIT_WITHOUT_REQUEST]],
                "ud.id = ANY ($pregressTableAlias.doc_ids)",
            ]);
    }

    public function detailsDecorateDocsWait(array $row): array
    {
        $aiRows = [];
        foreach (Json::decode($row['wait_details'] ?: '[]') as ['type' => $docType, 'tags' => $tags, 'valid' => $valid, 'messages' => $messages]) {
            $aiRows[] = $this->docTypeStr($docType, $tags) . $this->aiMessagesStr($valid, $messages);
        }
        return $this->mergeDuplicates($aiRows);
    }

    private function mergeDuplicates(array $rows): array
    {
        $rows = array_count_values($rows);
        return array_map(static fn($str, $count) => ($count > 1 ? "$count x " : '') . $str, array_keys($rows), $rows);
    }

    private function docTypeStr(int|null $kycDocType, array|null $tags): string
    {
        if (isset($kycDocType)) {
            return UserDocumentProgress::TYPES[$kycDocType] ?? (string)$kycDocType;
        }

        return implode(', ', array_map(static fn($tag) => UserDocument::TAGS[$tag] ?? $tag, $tags ?: []));
    }

    private function aiMessagesStr(bool|null $isValid, array|null $messages): string
    {
        $messages = array_map(static fn($messageId) => UserDocument::AI_VALIDATIONS[$messageId] ?? $messageId, $messages ?? []);
        if ($isValid) {
            array_unshift($messages, 'Valid');
        }
        return empty($messages) ? '' : ': ' . implode(', ', $messages);
    }
}
