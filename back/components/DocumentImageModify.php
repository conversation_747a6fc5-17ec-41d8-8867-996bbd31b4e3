<?php

declare(strict_types=1);

namespace app\back\components;

use app\back\components\services\FileStorage;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentLog;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentLogs;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\UserDocumentTextPredictions;
use app\back\repositories\Users;

readonly class DocumentImageModify
{
    private const string BROKEN_IMAGE_MD5 = '98253047a3e02b485df5cebd26311873';

    public function __construct(
        private BaseAuthAccess $authAccess,
        private FileStorage $storage,
        private UserDocumentsActive $userDocumentsActive,
        private UserDocumentFaces $userDocumentFaces,
        private UserDocumentTextPredictions $userDocumentTextPredictions,
        private UserDocumentLogs $userDocumentLogs,
        private Users $users,
    ) {
    }

    public function rotate(UserDocument $userDocument, int $angle, bool $flip = false): bool
    {
        if (str_replace('-', '', $userDocument->etag ?? '') === self::BROKEN_IMAGE_MD5) {
            return false;
        }

        $imagick = new \Imagick();
        $isChanged = $imagick->readImageBlob($this->storage->getContents($userDocument->storagePath(), $metadata))
            && $imagick->setImageOrientation(\Imagick::ORIENTATION_UNDEFINED);

        if ($angle) {
            $isChanged = $isChanged && $imagick->rotateImage('black', $angle);
        }

        if ($flip) {
            $isChanged = $isChanged && $imagick->flopImage();
        }

        if (!$isChanged) {
            return false;
        }

        $tmpFile = 'temporary/' . date('Ymd_His_') . "{$userDocument->site_id}_{$userDocument->user_id}_{$userDocument->filename}_" . uniqid('', false) . '.jpg';
        $this->storage->put($tmpFile, $imagick->getImageBlob(), $imagick->getImageMimeType(), $metadata);

        try {
            $docLog = new UserDocumentLog();
            $docLog->document_id = $userDocument->id;
            $docLog->created_by = $this->authAccess->employeeId();
            $docLog->action = UserDocumentLog::ACTION_ROTATE;
            $docLog->details = ['angle' => $angle];
            $this->userDocumentLogs->insert($docLog);

            $userDocument->etag = md5($imagick->getImageBlob());
            $userDocument->width = $imagick->getImageWidth();
            $userDocument->height = $imagick->getImageHeight();
            $userDocument->pushChangedFields('etag', 'width', 'height');

            $this->userDocumentsActive->db->transaction(function () use ($userDocument) {
                $this->updateFaceNotProcessed($userDocument);
                $this->userDocumentFaces->deleteByDocumentId($userDocument->id);
                $this->userDocumentTextPredictions->deleteAll(['user_document_id' => $userDocument->id]);
                $this->users->resetCloudSources($userDocument->site_id, $userDocument->user_id);
            });
            $this->storage->copy($tmpFile, $userDocument->storagePath()) or throw new \Exception("Unable to copy uploaded file $tmpFile => {$userDocument->storagePath()}");
        } finally {
            $this->storage->delete($tmpFile) or throw new \Exception("Unable to delete temporary file $tmpFile");
        }

        return true;
    }

    private function updateFaceNotProcessed(UserDocument $document): void
    {
        $document->face_processed = false;
        $document->updated_by = $this->authAccess->employeeId();
        $document->pushChangedFields('face_processed', 'updated_by');

        // reset only if not set manually
        if (!$document->isFieldChanged('force_face_recognize') && $document->force_face_recognize !== null) {
            $document->force_face_recognize = null;
            $document->pushChangedFields('force_face_recognize');
        }

        $this->userDocumentsActive->updateOrThrowModified($document);
    }
}
