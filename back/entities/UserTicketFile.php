<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringValidator;

class UserTicketFile extends BaseEntity
{
    public const int SYNC_TO_DOWNLOAD = 1;
    public const int SYNC_SUCCESS = 2;
    public const int SYNC_INVALID = 3;
    public const int SYNC_FAILED = 4;
    public const int SYNC_NOT_FOUND = 5;

    public const array SYNC_STATUSES = [
        self::SYNC_TO_DOWNLOAD => 'Download ready',
        self::SYNC_SUCCESS => 'Download success',
        self::SYNC_INVALID => 'File not valid',
        self::SYNC_FAILED => 'Download failed',
        self::SYNC_NOT_FOUND => 'File not found',
    ];

    public int $id;
    #[StringValidator(3)]
    public string $extension;
    #[IdValidator]
    public int $ticket_id;
    #[IntInArrayValidator(self::SYNC_STATUSES)]
    public int $sync_status;
    #[StringValidator(1, 300)]
    public string $original_name;
    #[IntValidator]
    public int $source;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $upserted_at;
    #[IdValidator]
    public ?int $jira_file_id;
    #[StringValidator(32, 32)]
    public ?string $product_file_key;
}
