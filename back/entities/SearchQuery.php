<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringLimitedValidator;

class SearchQuery extends BaseEntity
{
    #[IntValidator]
    public int $site_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $date;
    #[StringLimitedValidator(255)]
    public string $query;
    #[IntValidator]
    public int $count = 0;
}
