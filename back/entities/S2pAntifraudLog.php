<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringLimitedValidator;
use app\back\components\validators\StringValidator;

class S2pAntifraudLog extends BaseEntity
{
    public const int ACTION_ADD = 1;
    public const int ACTION_REMOVE = 0;

    public const int REQUISITE_TYPE_CARD = 1;
    public const int REQUISITE_TYPE_IP = 2;
    public const int REQUISITE_TYPE_CLIENT = 3;
    public const int REQUISITE_TYPE_PHONE = 4;
    public const int REQUISITE_TYPE_WALLET = 5;
    public const int REQUISITE_TYPE_EMAIL = 6;
    public const int REQUISITE_TYPE_PROJECT = 7;
    public const int REQUISITE_TYPE_COUNTRY = 8;
    public const int REQUISITE_TYPE_PERSON = 9;
    public const int REQUISITE_TYPE_BIN = 10;
    public const int REQUISITE_TYPE_HOST = 11;
    public const int REQUISITE_TYPE_PAYER = 12;
    public const int REQUISITE_TYPE_UNKNOWN = 100;

    public const array ACTIONS = [
        self::ACTION_ADD => 'Add',
        self::ACTION_REMOVE => 'Remove',
    ];

    public const array REQUISITE_TYPES = [
        self::REQUISITE_TYPE_CARD => 'card',
        self::REQUISITE_TYPE_IP => 'ip',
        self::REQUISITE_TYPE_CLIENT => 'client',
        self::REQUISITE_TYPE_PHONE => 'phone',
        self::REQUISITE_TYPE_WALLET => 'wallet',
        self::REQUISITE_TYPE_EMAIL => 'email',
        self::REQUISITE_TYPE_PROJECT => 'project',
        self::REQUISITE_TYPE_COUNTRY => 'country',
        self::REQUISITE_TYPE_PERSON => 'person',
        self::REQUISITE_TYPE_BIN => 'bin',
        self::REQUISITE_TYPE_HOST => 'host',
        self::REQUISITE_TYPE_PAYER => 'payer',
        self::REQUISITE_TYPE_UNKNOWN => 'unknown',
    ];

    public const array REQUISITE_TYPES_FOR_BLOCKS = [
        self::REQUISITE_TYPE_CARD ,
        self::REQUISITE_TYPE_PHONE,
        self::REQUISITE_TYPE_WALLET,
        self::REQUISITE_TYPE_EMAIL,
    ];

    #[IntValidator]
    public int $id;
    #[IntValidator]
    public int $action;
    #[IntValidator]
    public int $requisite_type = self::REQUISITE_TYPE_UNKNOWN;
    #[StringValidator(0, 100)]
    public ?string $requisite;
    #[IntValidator]
    public ?int $site_id;
    #[BigIdValidator]
    public ?int $user_id;
    #[DateTimeImmutableValidator]
    public \DateTimeImmutable $created_at;
    #[StringLimitedValidator(100)]
    public ?string $block_reason;

    public static function getActionNameById(int $typeId): string
    {
        return self::ACTIONS[$typeId];
    }

    public static function getRequisiteTypeIdByName(string $typeName): int|string
    {
        $map = array_flip(self::REQUISITE_TYPES);

        return $map[$typeName] ?? self::REQUISITE_TYPE_UNKNOWN;
    }

    public static function getRequisiteTypeNameById(int $typeId): string
    {
        return self::REQUISITE_TYPES[$typeId] ?? self::REQUISITE_TYPES[self::REQUISITE_TYPE_UNKNOWN];
    }
}
