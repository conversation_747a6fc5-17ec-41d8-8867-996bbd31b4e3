<?php

declare(strict_types=1);

namespace app\back\entities\jsonTypes;

use app\back\components\Form;
use app\back\components\validators\StringValidator;

/** DTO used for credentials which all keys are directly and ONLY directly accessed in request, all others keys are omitted */
class AdCredentialValue
{
    use Form;

    /** Unity */
    #[StringValidator(1, 30)]
    public ?string $organization = null;
    #[StringValidator(36, 36)]
    public ?string $key = null;
    #[StringValidator(10, 50)]
    public ?string $secret = null;

    /** Vungle */
    #[StringValidator(1, 50)]
    public ?string $bearerToken = null;

    /** Facebook API */
    #[StringValidator(1, 20)]
    public ?string $accountId = null;
    #[StringValidator(1, 300)]
    public ?string $accessToken = null;

    /** Facebook Renta */
    #[StringValidator(1, 20)]
    public ?string $type = null;
    #[StringValidator(1, 300)]
    public ?string $auth_uri = null;
    #[StringValidator(1, 30)]
    public ?string $client_id = null;
    #[StringValidator(1, 300)]
    public ?string $token_uri = null;
    #[StringValidator(1, 20)]
    public ?string $project_id = null;
    #[StringValidator(1, 10000)]
    public ?string $private_key = null;
    #[StringValidator]
    public ?string $client_email = null;
    #[StringValidator(1, 20)]
    public ?string $private_key_id = null;
    #[StringValidator(1, 300)]
    public ?string $client_x509_cert_url = null;
    #[StringValidator(1, 300)]
    public ?string $auth_provider_x509_cert_url = null;
}
