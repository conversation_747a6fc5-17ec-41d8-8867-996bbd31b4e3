<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\MoneyValidator;
use app\back\components\validators\StringValidator;

class UserTicket extends BaseEntity
{
    public const int TYPE_LOST_DEPOSIT = 1;

    public const array TYPES = [
        self::TYPE_LOST_DEPOSIT => 'Lost deposit',
    ];

    public const int STATUS_NEED_APPROVE = 1;
    public const int STATUS_OPEN = 2;
    public const int STATUS_DECLINED = 3;
    public const int STATUS_DESCRIPTION_NEEDED = 4;
    public const int STATUS_IN_PROGRESS = 5;
    public const int STATUS_PROVIDER_CHECKING = 6;
    public const int STATUS_WAITING_CLIENT = 7;
    public const int STATUS_INCORRECT_REQUEST = 8;
    public const int STATUS_RESOLVED = 9;
    public const int STATUS_CLOSED = 10;
    public const int STATUS_REOPENED = 11;
    public const int STATUS_PAYMENT_RECEIVED = 12;
    public const int STATUS_PAYMENT_LOST = 13;
    public const int STATUS_PAYMENT_FAILED = 14;

    public const array STATUSES = [
        self::STATUS_NEED_APPROVE => 'Need approve',
        self::STATUS_OPEN => 'Open',
        self::STATUS_DECLINED => 'Declined',
        self::STATUS_DESCRIPTION_NEEDED => 'Description needed',
        self::STATUS_IN_PROGRESS => 'In progress',
        self::STATUS_WAITING_CLIENT => 'Waiting for client',
        self::STATUS_INCORRECT_REQUEST => 'Not correct request',
        self::STATUS_PROVIDER_CHECKING => 'Provider checking',
        self::STATUS_RESOLVED => 'Resolved',
        self::STATUS_CLOSED => 'Closed',
        self::STATUS_REOPENED => 'Reopened',
        self::STATUS_PAYMENT_RECEIVED => 'Payment received',
        self::STATUS_PAYMENT_LOST => 'Lost payment',
        self::STATUS_PAYMENT_FAILED => 'Failed payment',
    ];

    public const int SOURCE_ANALYTIC = 1;
    public const int SOURCE_PRODUCT = 2;
    public const int SOURCE_JIRA = 3;

    public const array SOURCES = [
        UserTicket::SOURCE_ANALYTIC => 'Analytics',
        UserTicket::SOURCE_PRODUCT => 'Product',
        UserTicket::SOURCE_JIRA => 'Jira',
    ];

    public int $id;
    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[IntInArrayValidator(self::TYPES)]
    public int $type;
    #[IntInArrayValidator(self::STATUSES)]
    public int $status;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
    #[IntInArrayValidator(self::SOURCES)]
    public ?int $source;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $upserted_at;
    #[StringValidator]
    public ?string $jira_key = null;
    #[IdValidator]
    public ?int $product_ticket_id = null;
    #[StringValidator(1, 36)]
    public ?string $invoice_id = null;
    #[MoneyValidator]
    public ?string $amount_from_user;
    #[StringValidator(1, 100)]
    public ?string $performer;


    public static function jiraUrl(string $jiraKey): string
    {
        return "https://jira.syneforge.com/browse/$jiraKey";
    }

    public static function isApproveDeclineAllowed(int $status): bool
    {
        return $status === self::STATUS_NEED_APPROVE;
    }
}
