{"name": "analytics", "type": "module", "version": "1.0.0", "private": true, "description": "Evoplay analytics", "author": "Sartor <<EMAIL>>", "scripts": {"build": "vite build", "watch": "vite"}, "dependencies": {"@popperjs/core": "^2.11.8", "bootstrap": "^5.3.6", "flatpickr": "4.6.13", "highcharts": "^12.2.0", "lodash-es": "^4.17.21", "luminous-lightbox": "^2.4.0", "marked": "^14.1.4", "ngraph.coarsen": "^1.5.0", "ngraph.forcelayout": "^3.3.1", "ngraph.graph": "^19.1.0", "ngraph.louvain": "^2.0.0", "ngraph.svg": "^0.0.17", "notyf": "^3.10.0", "sortablejs": "^1.15.6", "uuid": "^11.1.0", "vivagraphjs": "git+ssh://*******************:777/analytics/core/vivagraph.git", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^10.0.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.29", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-typescript": "^14.5.0", "@vue/language-server": "^2.2.10", "@vue/tsconfig": "^0.7.0", "eslint": "^9.28.0", "eslint-plugin-vue": "^9.33.0", "jiti": "^2.4.2", "sass": "1.77.6", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0", "vite": "^6.3.5", "vite-plugin-eslint": "^1.8.1", "vite-plugin-vue-devtools": "^7.7.2", "vite-plugin-checker": "^0.9.3", "vue-tsc": "^2.2.10"}, "contributors": ["Becalm <<EMAIL>>", "Kidman <<EMAIL>>", "Magmus <<EMAIL>>", "Maxx <<EMAIL>>", "Purplelephant <<EMAIL>>", "Sartor <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "Vlsirko <<EMAIL>>"]}