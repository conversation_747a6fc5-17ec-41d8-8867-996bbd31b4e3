<?php

declare(strict_types=1);

namespace app\tests\unit\modules\task;

use app\back\modules\task\actions\TaskWithDefaultGetData;
use app\back\modules\task\ImportTask;
use app\back\repositories\BaseRepository;
use app\back\repositories\Users;

/** @property MockRequest $request */
class MockTask extends ImportTask
{
    use TaskWithDefaultGetData;

    public const string BEFORE_FIND_ERROR = 'before-find-error';

    public function __construct(private readonly Users $repository)
    {
    }

    protected function beforeFind(array &$row): bool
    {
        if (array_key_exists('email', $row) && $row['email'] === self::BEFORE_FIND_ERROR) {
            $this->log->error('Triggered ' . self::BEFORE_FIND_ERROR);
        }

        return parent::beforeFind($row);
    }

    protected function repository(): BaseRepository
    {
        return $this->repository;
    }
}
