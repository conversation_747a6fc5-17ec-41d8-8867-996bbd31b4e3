<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\import;

use app\back\components\helpers\DateHelper;
use app\back\config\tasks\Res;
use app\back\entities\Employee;
use app\back\entities\UserTicket;
use app\back\modules\task\actions\import\UsersTicketsJiraTask;
use app\back\modules\task\components\JiraParseHelper;
use app\back\repositories\UserTickets;
use app\back\repositories\UserTicketLogs;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;

#[CoversClass(UsersTicketsJiraTask::class)]
class JiraImportTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string JIRA_KEY = 'TEST-1';

    public function testImport(): void
    {
        $this->haveSystemUser();
        $invoiceId = self::uniqRuntimeUuid();

        $issue = [
            'key' => self::JIRA_KEY,
            'fields' => [
                'issuetype' => ['id' => JiraParseHelper::JIRA_TYPE_LOST_DEPOSIT_ID],
                'creator' => ['emailAddress' => Employee::SYSTEM_USER_EMAIL],
                'created' => '2025-01-01T16:00:00.000+0200',
                'updated' => '2025-01-01T16:00:00.000+0200',
                'customfield_17200' => ['value' => 'CV'], // site_id
                'customfield_17201' => self::uniqRuntimeId(), // user_id
            ],
            'changelog' => [],
        ];

        $this->initStatusAndInvoice($issue, 'INVALID STATUS', 'VALID_INVOICE');
        $this->runImportTask($issue);
        $this->dontSeeRecord(UserTickets::class, ['jira_key' => self::JIRA_KEY]);

        $this->initStatusAndInvoice($issue, 'Need Approve', 'INVALID_BAD_TERRIBLE_AWFUL_OVERCOMPLETE_AND_LONG_LONG_LONG_INVOICE_ID_THAT_WILL_NEVER_PASS');
        $this->runImportTask($issue);
        $this->dontSeeRecord(UserTickets::class, ['jira_key' => self::JIRA_KEY]);

        $this->initStatusAndInvoice($issue, 'Need Approve', null);
        $this->runImportTask($issue);
        $this->dontSeeRecord(UserTickets::class, ['jira_key' => self::JIRA_KEY]);

        $this->addInvoiceId($issue, $invoiceId, '2025-02-02T16:00:00.000+0200');
        $this->runImportTask($issue);
        /** @var UserTicket $initialTicket */
        $initialTicket = $this->seeRecord(UserTickets::class, [
            'jira_key' => self::JIRA_KEY,
            'status' => UserTicket::STATUS_NEED_APPROVE,
            'invoice_id' => $invoiceId,
            'created_at' => '2025-01-01 14:00:00', // utc
            'updated_at' => '2025-02-02 14:00:00', // utc
            'source' => UserTicket::SOURCE_JIRA,
        ]);
        $this->dontSeeRecord(UserTicketLogs::class, [
            'ticket_id' => $initialTicket->id,
            'created_at' => ($initialTicket->updated_at)->format(DateHelper::DATETIME_FORMAT_PHP),
        ]);

        $this->changeStatusToOpen($issue, '2025-03-03T16:00:00.000+0200');
        $this->runTask('users-tickets', Res::JIRA, $this->json(['issues' => [$issue]]));
        /** @var UserTicket $updatedTicket */
        $updatedTicket = $this->seeRecord(UserTickets::class, [
            'jira_key' => self::JIRA_KEY,
            'status' => UserTicket::STATUS_OPEN,
            'updated_at' => '2025-03-03 14:00:00', // utc
        ]);

        $this->seeRecords(UserTicketLogs::class, [
            [
                'ticket_id' => $initialTicket->id,
                'status' => UserTicket::STATUS_NEED_APPROVE,
                'source' => UserTicket::SOURCE_JIRA,
                'created_by' => Employee::SYSTEM_USER_EMAIL,
                'created_at' => $initialTicket->created_at,
            ],
            [
                'ticket_id' => $initialTicket->id,
                'status' => UserTicket::STATUS_OPEN,
                'source' => UserTicket::SOURCE_JIRA,
                'created_by' => Employee::SYSTEM_USER_EMAIL,
                'created_at' => $updatedTicket->updated_at,
            ],
        ]);
    }

    private function runImportTask(array $issue): void
    {
        $this->runTask('users-tickets', Res::JIRA, $this->json(['issues' => [$issue]]));
    }

    private function initStatusAndInvoice(array &$issue, string $status, ?string $invoiceId): void
    {
        $issue['fields']['status']['name'] = $status;
        $issue['fields']['customfield_12902'] = $invoiceId;
    }

    private function addInvoiceId(&$issue, string $invoiceId, string $date): void
    {
        $this->addChangelog($issue, $date, [[
            'field' => 'Invoice ID',
            'fromString' => null,
            'toString' => $invoiceId,
        ]]);

        $issue['fields']['customfield_12902'] = $invoiceId;
        $issue['fields']['updated'] = $date;
    }

    private function changeStatusToOpen(array &$issue, string $date): void
    {
        $this->addChangelog($issue, $date, [[
            'field' => 'status',
            'fromString' => $issue['fields']['status']['name'],
            'toString' => 'Open',
        ]]);

        $issue['fields']['status']['name'] = 'Open';
        $issue['fields']['updated'] = $date;
    }

    private function addChangelog(array &$issue, string $date, array $items): void
    {
        $issue['changelog']['histories'][] = [
            'author' => ['emailAddress' => Employee::SYSTEM_USER_EMAIL],
            'created' => $date,
            'items' => $items,
        ];
    }
}
