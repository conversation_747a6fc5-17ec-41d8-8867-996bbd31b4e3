<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\components\RuntimeCache;
use app\back\entities\Site;
use app\back\entities\UserBlock;
use app\back\modules\task\actions\UsersBlocksTask;
use app\back\repositories\CheckAlerts;
use app\back\repositories\UserBlockComments;
use app\back\repositories\UserBlocks;
use app\back\repositories\views\UserGamePaymentActivities;
use app\back\repositories\Users;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbCleanUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use Psr\SimpleCache\CacheInterface;
use Yiisoft\Db\Query\Query;

#[CoversClass(UsersBlocksTask::class)]
class UsersBlocksTaskTest extends BaseActionTestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    private const string DATA_GI = <<<DATA
player_id,lock_type,is_active,updated_at,reason,comment
154959,deposit_blocked,1,2024-01-24T17:12:21+00:00,Bonus abuse,
154959,login_blocked,0,2024-01-24T16:42:40+00:00,Fraudulent activities,test comment 777
154959,slot_blocked,1,2024-01-24T16:40:32+00:00,,just a comment
227445,login_blocked,1,2024-01-24T13:30:46+00:00,,
756669,login_blocked,1,2024-01-24T13:30:46+00:00,,

DATA;

    private const string DATA_SMEN = <<<DATA
id,userId,operatorId,operatorEmail,reason,createdAt,initBy,operation
136392,76996527,75000892,<EMAIL>,"по запросу игрока ","2023-02-11 11:48:26",project,lock
136395,64910437,75000892,<EMAIL>,"по запросу игрока ","2023-02-11 11:48:53",project,lock
136398,66380089,75000892,<EMAIL>,"по запросу игрока ","2023-02-11 11:49:32",project,lock
136401,77127141,75000892,<EMAIL>,"по запросу игрока ","2023-02-11 11:50:18",project,lock
136404,52252807,75000892,<EMAIL>,"По запросу клиента, фри аккаунт ","2023-02-11 11:50:57",project,lock
136405,26664608,32770052,<EMAIL>,"Issue resolved. Unlock","2023-02-11 11:51:25",project,unlock

DATA;

    private const string DATA_YS = <<<DATA
id,user_id,employee_id,status,reason,created_at
1812693,3492761,,Active,,2025-01-07 00:10:05
1812694,2982179,,Blocked,"Self-exclusion period was activated. If you have any questions please contact customer care representative.",2025-01-07 00:33:42
1812695,5309803,,Blocked,"Self-exclusion period was activated. If you have any questions please contact customer care representative.",2025-01-07 00:39:54
1812696,17038391,,Blocked,"Self-exclusion period was activated. If you have any questions please contact customer care representative.",2025-01-07 00:45:14

DATA;

    public function testAddDataAndApiOutputSmen(): void
    {
        $users = [76996527, 64910437, 26664608, 66380089, 77127141, 52252807];

        foreach ($users as $userId) {
            $this->haveRecord(Users::class, ['site_id' => Site::CV, 'user_id' => $userId]);
        }
        $usersBlocked = [
            [$users[0], true],
            [$users[1], false],
            [$users[2], true],
        ];
        foreach ($usersBlocked as [$userId, $active]) {
            $this->haveRecord(UserBlocks::class, ['site_id' => Site::CV, 'user_id' => $userId, 'type' => UserBlock::BLOCK_TYPE_LOGIN, 'reason' => UserBlock::REASON_DEFAULT, 'active' => $active, 'created_at' => new \DateTimeImmutable(), 'updated_at' => new \DateTimeImmutable(), 'source' => UserBlock::SOURCE_PRODUCT]);
        }

        $this->runTask('users-blocks', 'CV', $this->debugFile(static::DATA_SMEN));

        $comments = $this->container()->get(UserBlockComments::class);
        $usersBlockComment = [
            [$users[0], true, $comments->getIdByName('по запросу игрока ')],
            [$users[1], true, $comments->getIdByName('по запросу игрока ')],
            [$users[2], false, $comments->getIdByName('Issue resolved. Unlock')],
            [$users[3], true, $comments->getIdByName('по запросу игрока ')],
            [$users[4], true, $comments->getIdByName('по запросу игрока ')],
            [$users[5], true, $comments->getIdByName('По запросу клиента, фри аккаунт ')],
        ];
        foreach ($usersBlockComment as [$userId, $active, $comment]) {
            $this->seeRecord(UserBlocks::class, ['site_id' => Site::CV, 'user_id' => $userId, 'type' => UserBlock::BLOCK_TYPE_LOGIN, 'reason' => UserBlock::REASON_DEFAULT, 'active' => $active, 'comment_id' => $comment]);
            $this->seeRecord(Users::class, ['site_id' => Site::CV, 'user_id' => $userId, 'is_blocked' => $active]);
        }

        $this->sendAPI('crm', 'get/user-blocks', [
            'site_id' => ['=' => Site::CV],
            'active' => ['=' => true],
            'type' => ['=' => UserBlock::BLOCK_TYPE_LOGIN],
            'reason' => ['=' => UserBlock::REASON_DEFAULT],
            'source' => ['=' => UserBlock::SOURCE_PRODUCT],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $users[5]],
            ['user_id' => $users[1]],
            ['user_id' => $users[3]],
            ['user_id' => $users[0]],
            ['user_id' => $users[4]],
        ]);
    }

    public function testAddDataAndApiOutputGi(): void
    {
        $users = [154959, 227445, 756669];
        foreach ($users as $userId) {
            $this->haveRecord(Users::class, ['site_id' => Site::VV, 'user_id' => $userId]);
        }

        $userBlocksRows = [
            [$users[0], UserBlock::BLOCK_TYPE_LOGIN, UserBlock::REASON_FRAUDULENT_ACTIVITIES, true],
            [$users[0], UserBlock::BLOCK_TYPE_DEPOSIT, UserBlock::REASON_BONUS_ABUSE, false],
        ];
        foreach ($userBlocksRows as [$userId, $type, $reason, $active]) {
            $this->haveRecord(UserBlocks::class, ['site_id' => Site::VV, 'user_id' => $userId, 'type' => $type, 'reason' => $reason, 'active' => $active, 'created_at' => new \DateTimeImmutable(), 'updated_at' => new \DateTimeImmutable(), 'source' => UserBlock::SOURCE_PRODUCT]);
        }
        $this->haveRecord(Users::class, ['site_id' => Site::VV, 'user_id' => $users[0], 'is_blocked' => true]);

        $this->runTask('users-blocks', 'VV', $this->debugFile(static::DATA_GI));

        $userBlocksRowsAfterTask = [
            [$users[0], UserBlock::BLOCK_TYPE_LOGIN, UserBlock::REASON_FRAUDULENT_ACTIVITIES, false],
            [$users[0], UserBlock::BLOCK_TYPE_DEPOSIT, UserBlock::REASON_BONUS_ABUSE, true],
            [$users[0], UserBlock::BLOCK_TYPE_SLOT, UserBlock::REASON_DEFAULT, true],
            [$users[1], UserBlock::BLOCK_TYPE_LOGIN, UserBlock::REASON_DEFAULT, true],
            [$users[2], UserBlock::BLOCK_TYPE_LOGIN, UserBlock::REASON_DEFAULT, true],
        ];
        foreach ($userBlocksRowsAfterTask as [$userId, $type, $reason, $active]) {
            $this->seeRecord(UserBlocks::class, ['site_id' => Site::VV, 'user_id' => $userId, 'type' => $type, 'reason' => $reason, 'active' => $active]);
        }

        $usersAfterTask = [
            [$users[0], false],
            [$users[1], true],
            [$users[2], true],
        ];
        foreach ($usersAfterTask as [$userId, $active]) {
            $this->seeRecord(Users::class, ['site_id' => Site::VV, 'user_id' => $userId, 'is_blocked' => $active]);
        }

        $this->sendAPI('crm', 'get/user-blocks', [
            'site_id' => ['=' => Site::VV],
            'active' => ['=' => true],
            'type' => ['=' => UserBlock::BLOCK_TYPE_LOGIN],
            'reason' => ['=' => UserBlock::REASON_DEFAULT],
            'source' => ['=' => UserBlock::SOURCE_PRODUCT],
        ]);

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => $users[1]],
            ['user_id' => $users[2]],
        ]);
    }

    public function testAddDataAndApiOutputYs(): void
    {
        $users = [3492761, 2982179, 5309803, 17038391];
        foreach ($users as $userId) {
            $this->haveRecord(Users::class, ['site_id' => Site::MRB, 'user_id' => $userId]);
        }

        $this->runTask('users-blocks', 'MRB', $this->debugFile(static::DATA_YS));

        $this->dontSeeRecord(UserBlocks::class, ['site_id' => Site::MRB, 'user_id' => 3492761]);
        $this->seeRecord(Users::class, ['site_id' => Site::MRB, 'user_id' => 3492761, 'is_blocked' => false]);

        foreach ([2982179, 5309803, 17038391] as $blockedUserId) {
            $this->seeRecord(
                UserBlocks::class,
                [
                    'site_id' => Site::MRB,
                    'user_id' => $blockedUserId,
                    'type' => UserBlock::BLOCK_TYPE_LOGIN,
                    'active' => true,
                    'source' => UserBlock::SOURCE_PRODUCT,
                ]
            );

            $this->seeRecord(Users::class, ['site_id' => Site::MRB, 'user_id' => $blockedUserId, 'is_blocked' => true]);
        }

        $this->sendAPI(
            'crm',
            'get/user-blocks',
            [
                'site_id' => ['=' => Site::MRB],
                'active' => ['=' => true],
                'type' => ['=' => UserBlock::BLOCK_TYPE_LOGIN],
                'source' => ['=' => UserBlock::SOURCE_PRODUCT],
            ]
        );

        $this->seeResponseIsCsv();

        $this->seeResponseEqualsCsvRows([
            ['user_id' => 2982179],
            ['user_id' => 5309803],
            ['user_id' => 17038391],
        ]);
    }
}
