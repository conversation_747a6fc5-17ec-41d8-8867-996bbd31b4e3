<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\components\helpers\DateHelper;
use app\back\components\helpers\UuidHelper;
use app\back\components\kyc\KycStatusModel;
use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\entities\User;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\modules\task\actions\UsersDocumentsSmenTask;
use app\back\repositories\views\UserDocumentsActive;
use app\tests\libs\KycUpdateTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use Yiisoft\Db\Query\Query;

#[CoversClass(UsersDocumentsSmenTask::class)]
class UsersDocumentsSmenTaskTest extends BaseActionTestCase
{
    use KycUpdateTrait;

    public function testWithKyc(): void
    {
        $user1 = $this->haveUserRecord(['site_id' => Site::S7]);
        $user2 = $this->haveUserRecord(['site_id' => Site::S7]);
        $docsRowsUser1 = $this->generateUsersDocsRows($user1, $user1);
        $docsRowsUser2 = $this->generateUsersDocsRows($user2, $user2);
        $kycStatusModel = $this->container()->get(KycStatusModel::class);
        $kycStatusModel->source = UserDocumentProgress::SOURCE_ANALYTICS;
        $kycStatusModel->update($user1->site_id, $user1->user_id, UserKyc::KYC_REQUEST);

        $this->runTask('users-documents', Res::S7, '--mode=noDownload', '--skipEvents', $this->json(array_merge($docsRowsUser1, $docsRowsUser2)));
        $this->assertKycStatus($user1, UserKyc::KYC_WAIT, UserKyc::KYC_REQUEST, curDocs: $this->docIdsByDocRows($docsRowsUser1));
        $this->assertKycStatus($user2, UserKyc::KYC_WAIT_WITHOUT_REQUEST, curDocs: $this->docIdsByDocRows($docsRowsUser2));
    }

    private function generateUsersDocsRows(User ...$users): array
    {
        $now = date(DateHelper::DATETIME_FORMAT_PHP);
        return array_map(fn($u) => [
            "id" => UuidHelper::cast($this->uniqRuntimeId()),
            "userId" => $u->user_id,
            "createdAt" => $now,
            "updatedAt" => $now,
            "type" => UserDocumentProgress::TYPES_API_NAMES[UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD],
            "status" => UsersDocumentsSmenTask::STATUS_AI_VALID,
            "validationMessages" => null,
            "adminMessage" => null,
            "aiScheduledAt" => $now,
            "aiCompletedAt" => $now,
            "url" => "https://fake.host/file.jpg",
        ], $users);
    }

    private function docIdsByDocRows(array $docRows): array
    {
        return (new Query($this->db()))
            ->select('id')
            ->from(UserDocumentsActive::TABLE_NAME)
            ->where(['external_id' => array_column($docRows, 'id')])
            ->column();
    }
}
