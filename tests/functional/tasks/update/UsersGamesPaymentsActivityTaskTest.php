<?php

declare(strict_types=1);

namespace app\tests\functional\tasks\update;

use app\back\config\tasks\Res;
use app\back\entities\UserTransaction;
use app\back\modules\task\actions\update\UsersGamesPaymentsActivityTask;
use app\back\repositories\views\UserGamePaymentActivities;
use app\tests\functional\tasks\BaseActionTestCase;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use Yiisoft\Db\Query\Query;

#[CoversClass(UsersGamesPaymentsActivityTask::class)]
class UsersGamesPaymentsActivityTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const int USER_AGE = 14;

    private int $siteId;
    private int $userId;

    public function setUp(): void
    {
        parent::setUp();

        $this->siteId ??= self::uniqSiteId();
        $this->userId ??= self::uniqRuntimeId();
    }

    public function testBasic(): void
    {
        $this->haveRates();

        $dateOk = $this->fromNow('- 10 day');

        //eligible user with without games && deps has score 0
        $this->haveUserRecord($this->testData(['date' => $dateOk]));
        $this->runTaskAndAssertScore(0);

        // 1 dep = score 1
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $dateOk]));
        $this->runTaskAndAssertScore(1);

        // 1 dep + 1 game = score 2
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $dateOk]));
        $this->runTaskAndAssertScore(2);
    }

    public function testOldUser(): void
    {
        $this->haveRates();
        $oldDate = $this->fromNow('- 15 day');
        $actualDate = $this->fromNow('- 10 day');

        $this->haveUserRecord($this->testData(['date' => $oldDate]));
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $oldDate]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $oldDate]));
        $this->runTaskAndAssertCount(0);

        $this->localHaveUserStatRecord($this->testData(['updated_at' => $actualDate]));
        $this->runTaskAndAssertCount(0);

        $this->haveUserGameTokenRecord($this->testData(['created_at' => $actualDate]));
        $this->runTaskAndAssertCount(0);
    }

    public function testEdgeDates(): void
    {
        $this->haveRates();

        $edgeDate = new \DateTimeImmutable(date('Y-m-d 00:00:01', strtotime('- 13 days')));
        $this->haveUserRecord($this->testData(['date' => $edgeDate]));
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $edgeDate]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $edgeDate]));
        $this->runTaskAndAssertScore(2);

        $edgeDate = new \DateTimeImmutable('-1 day');
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $edgeDate]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $edgeDate]));
        $this->runTaskAndAssertScore(4);
    }

    public function testScoreOnDifferentDateDepsAndGames(): void
    {
        $this->haveRates();
        $date1 = $this->fromNow('-10 day');
        $date2 = $this->fromNow('-9 day');
        $date3 = $this->fromNow('-5 day');
        $this->haveUserRecord($this->testData(['date' => $date1]));

        // 3 deps + 3 games in one day = score 2
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $date1]));
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $date1]));
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $date1]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $date1]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $date1]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $date1]));
        $this->runTaskAndAssertScore(2);

        // added 3 deps another day = score 3
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $date2]));
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $date2]));
        $this->localHaveUserStatRecord($this->testData(['updated_at' => $date2]));
        $this->runTaskAndAssertScore(3);

        //3 games played in third day = score 4
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $date3]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $date3]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $date3]));
        $this->runTaskAndAssertScore(4);

        //today games && payments don't affect score
        $today = new \DateTimeImmutable(date('Y-m-d H:00:00'));

        $this->localHaveUserStatRecord($this->testData(['updated_at' => $today]));
        $this->haveUserGameTokenRecord($this->testData(['created_at' => $today]));
        $this->runTaskAndAssertScore(4);
    }

    private function fromNow(string $interval): \DateTimeImmutable
    {
        return new \DateTimeImmutable(date('Y-m-d H:i:00', strtotime($interval)));
    }

    private function testData(array $data): array
    {
        return array_merge(['site_id' => $this->siteId, 'user_id' => $this->userId], $data);
    }

    private function localHaveUserStatRecord(array $props): UserTransaction
    {
        return $this->haveUserTransactionRecord(array_merge(['op_id' => UserTransaction::OP_IN, 'status' => UserTransaction::STATUS_SUCCESS], $props));
    }

    private function runTaskAndAssertCount(int $expected): void
    {
        $this->runTask('update-users-games-payments-activity', Res::DEFAULT, self::LAST_MINUTE_PERIOD);
        $count = (new Query($this->db()))->from(UserGamePaymentActivities::TABLE_NAME)->count();
        $this->assertSame($expected, $count);
    }

    private function runTaskAndAssertScore(int $expected): void
    {
        $this->runTask('update-users-games-payments-activity', Res::DEFAULT, self::LAST_MINUTE_PERIOD);
        $actual = (new Query($this->db()))
            ->select(['score'])
            ->from(UserGamePaymentActivities::TABLE_NAME)
            ->where(['site_id' => $this->siteId, 'user_id' => $this->userId])
            ->scalar();
        $this->assertSame($expected, $actual);
    }
}
