<?php

declare(strict_types=1);

namespace app\tests\functional\api\crm;

use app\back\entities\UserKyc;
use app\back\modules\api\clients\crm\filter\UsersMethod;
use app\tests\libs\ApiUnitTrait;
use app\tests\libs\DbTransactionalUnitTrait;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

#[CoversClass(UsersMethod::class)]
class CrmGetUsersActionTest extends TestCase
{
    use ApiUnitTrait;
    use DbTransactionalUnitTrait;

    public function testInvalid(): void
    {
        $this->sendAPI('crm', 'get/users', [
            'age' => ['=' => 10],
        ], 422);
    }

    public static function ageDataProvider(): array
    {
        /** [age check operator => years, [relative age => is match, ...]] */
        return [
            [['>=' => 20], [
                '-40 years' => true,
                '-20 years -1 day' => true,
                '-20 years' => true,
                '-19 years' => false,
            ]],
            [['<' => 21], [
                '-10 years' => true,
                '-20 years' => true,
                '-20 years -1 day' => true,
                '-21 years +1 day' => true,
                '-21 years' => false,
                '-21 years -1 day' => false,
            ]],
            [['=' => 10], [
                '-10 years -1 month' => true,
                '-10 years -1 day' => true,
                '-10 years' => true,
                '-10 years + 1 day' => false,
                '-9 years' => false,
                '+1 day' => false,
                'today' => false,
                '-11 years' => false,
                '-19 years' => false,
            ]],
            [['BETWEEN' => [10, 12]], [
                '-10 years' => true,
                '-11 years' => true,
                '-12 years' => true,
                '-9 years' => false,
                '-13 years' => false,
            ]],
        ];
    }

    #[DataProvider('ageDataProvider')]
    public function testUserAgeFilter(array $ageCheckExpression, array $ages): void
    {
        $siteId = self::uniqSiteId();

        $isMatched = [];
        $isNotMatched = [];
        foreach ($ages as $age => $isMatch) {
            $user = $this->haveUserRecord([
                'site_id' => $siteId,
                'birthday' => date('Y-m-d', strtotime($age)),
            ]);
            $uRow = ['user_id' => (string)$user->user_id];
            if ($isMatch) {
                $isMatched[] = $uRow;
            } else {
                $isNotMatched[] = $uRow;
            }
        }

        $this->sendAPI('crm', 'get/users', [
            'site_id' => ['=' => $siteId],
            'age' => $ageCheckExpression,
        ]);

        $this->seeResponseIsCsv();
        $this->seeRowsInCsv($isMatched);
        $this->seeRowsNotInCsv($isNotMatched);
    }

    public static function birthdayAgeDataProvider(): array
    {
        /** [check params, [age => is_match, ...]] */
        return [
            [['today', 'today'], [
                '-10 years' => true,
                '-20 years' => true,
                '-1 year' => true,
                'today' => true,
                '+1 day' => false,
                '-1 day' => false,
                '-1 year +1 day' => false,
                '-1 year -1 day' => false,
                '+1 year' => false,
            ]],
            [['-1 day', '+1 day'], [
                '-10 years' => true,
                '-11 years' => true,
                '-10 years -1 day' => true,
                '-10 years +1 day' => true,
                '-5 years +2 days' => false,
                '-5 years -2 days' => false,
                '-10 years -1 month' => false,
            ]],
        ];
    }

    #[DataProvider('birthdayAgeDataProvider')]
    public function testUserBirthdayFilters(array $birthdayBetween, array $ages): void
    {
        $siteId = self::uniqSiteId();

        $isMatched = [];
        $isNotMatched = [];
        foreach ($ages as $age => $isMatch) {
            $user = $this->haveUserRecord([
                'site_id' => $siteId,
                'birthday' => date('Y-m-d', strtotime($age)),
            ]);
            $uRow = ['user_id' => (string)$user->user_id];
            if ($isMatch) {
                $isMatched[] = $uRow;
            } else {
                $isNotMatched[] = $uRow;
            }
        }

        $this->sendAPI('crm', 'get/users', [
            'site_id' => ['=' => $siteId],
            'birthday' => ['BETWEEN' => array_map(static fn ($d) => date('Y-m-d', strtotime($d)), $birthdayBetween)],
        ]);

        $this->seeResponseIsCsv();
        $this->seeRowsInCsv($isMatched);
        $this->seeRowsNotInCsv($isNotMatched);
    }

    public static function updatedAtDataProvider(): array
    {
        return [[['>' => '-2 days'], [
                    ['today', true],
                    ['-1 day', true],
                    ['-2 days', false],
                    ['-3 days', false]
                ]],
                [['<=' => 'today'], [
                    ['today', true],
                    ['-1 day', true],
                    ['-1 week', true],
                    ['+1 day', false],
                ]],
            ];
    }

    #[DataProvider('updatedAtDataProvider')]
    public function testStatusUpdatedAt(array $expression, array $datetimeMatches): void
    {
        $siteId = self::uniqSiteId();
        foreach ($expression as $oper => $exp) {
            $inCsv = [];
            $notInCsv = [];
            foreach ($datetimeMatches as [$datetime, $matches]) {
                $user = $this->haveUserRecord(['site_id' => $siteId, 'status_updated_at' => date('Y-m-d', strtotime($datetime))]);

                $uRow = ['user_id' => (string)$user->user_id];
                if ($matches) {
                    $inCsv[] = $uRow;
                } else {
                    $notInCsv[] = $uRow;
                }
            }
            $this->sendAPI('crm', 'get/users', [
                'site_id' => ['=' => $siteId],
                'status_updated_at' => [$oper => date('Y-m-d', strtotime($exp))],
            ]);

            $this->seeResponseIsCsv();
            $this->seeRowsInCsv($inCsv);
            $this->seeRowsNotInCsv($notInCsv);
        }
    }


    public function testUserMetricKyc(): void
    {
        $siteId = self::uniqSiteId();
        $user1 = $this->haveUserRecord(['site_id' => $siteId]);
        $user2 = $this->haveUserRecord(['site_id' => $siteId]);
        $user3 = $this->haveUserRecord(['site_id' => $siteId]);

        $this->haveUserKycRecord([
            'site_id' => $siteId,
            'user_id' => $user1->user_id,
            'kyc_status' => UserKyc::KYC_VERIFIED
        ]);
        $this->haveUserKycRecord([
            'site_id' => $siteId,
            'user_id' => $user2->user_id,
            'kyc_status' => UserKyc::KYC_NOT_VERIFIED
        ]);

        $users = [$user1->user_id, $user2->user_id, $user3->user_id];
        $this->sendAndCheck(['=' => 1], [['user_id' => $user1->user_id]], $users, $siteId);
        $this->sendAndCheck(['!=' => 1], [['user_id' => $user2->user_id], ['user_id' => $user3->user_id]], $users, $siteId);
        $this->sendAndCheck(['=' => 0], [['user_id' => $user2->user_id], ['user_id' => $user3->user_id]], $users, $siteId);
        $this->sendAndCheck(['!=' => 0], [['user_id' => $user1->user_id]], $users, $siteId);

        $this->sendAndCheck(['=' => 1], [['user_id' => $user2->user_id], ['user_id' => $user3->user_id]], $users, $siteId, true);
        $this->sendAndCheck(['!=' => 1], [['user_id' => $user1->user_id]], $users, $siteId, true);
        $this->sendAndCheck(['=' => 0], [['user_id' => $user1->user_id]], $users, $siteId, true);
        $this->sendAndCheck(['!=' => 0], [['user_id' => $user2->user_id], ['user_id' => $user3->user_id]], $users, $siteId, true);
    }

    private function sendAndCheck(array $kycConditional, array $result, array $users, int $siteId, bool $exclude = false): void
    {
        $params = [
            'site_id' => ['=' => $siteId],
            'user_id' => ['IN' => $users],
            'is_kyc_confirmed' => $kycConditional,
        ];
        if ($exclude) {
            $params['exclude'] = ['=' => 1];
        }
        $this->sendAPI('crm', 'get/users', $params);

        $this->seeResponseIsCsv();
        $this->seeResponseEqualsCsvRows($result);
    }
}
