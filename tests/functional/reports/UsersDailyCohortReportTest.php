<?php

declare(strict_types=1);

namespace app\tests\functional\reports;

use app\back\components\helpers\DateHelper;
use app\back\entities\Rate;
use app\back\entities\Site;
use app\back\entities\UserGameToken;
use app\back\entities\UserTransaction;
use app\back\modules\reports\reports\Cohort\CohortConfig;
use app\back\modules\reports\reports\Cohort\CohortEntryTypeColumn;
use app\back\repositories\Games;
use app\back\repositories\UserGameTokens;
use app\back\repositories\UserLogins;
use app\back\repositories\Users;
use app\back\repositories\UserTransactions;

class UsersDailyCohortReportTest extends BaseReportTestCase
{
    public function testBettingMode(): void
    {
        $gamesRepo = $this->container()->get(Games::class);

        $this->usersRecords();

        $gameBetting = $gamesRepo->getBettingGameId();
        $this->haveRecords(UserGameTokens::class, [
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d791', 'site_id' => Site::GGB, 'user_id' => 1, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-01-30 01:01:01'), 'created_at' => new \DatetimeImmutable('2020-01-30 01:01:01'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d792', 'site_id' => Site::GGB, 'user_id' => 1, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:02'), 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:02'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d793', 'site_id' => Site::GGB, 'user_id' => 1, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:03'), 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:03'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d791', 'site_id' => Site::GGB, 'user_id' => 2, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:04'), 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:04'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d792', 'site_id' => Site::GGB, 'user_id' => 2, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-02-01 01:01:05'), 'created_at' => new \DatetimeImmutable('2020-02-01 01:01:05'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],

            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d791', 'site_id' => Site::GGB, 'user_id' => 3, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d792', 'site_id' => Site::GGB, 'user_id' => 3, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:02'), 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:02'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d791', 'site_id' => Site::GGB, 'user_id' => 4, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-02-01 01:01:03'), 'created_at' => new \DatetimeImmutable('2020-02-01 01:01:03'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],

            ['token_id' => '060026f3-c353-4fd5-b9cc-3ae607b2d791', 'site_id' => Site::GGB, 'user_id' => 5, 'game_id' => $gameBetting, 'updated_at' => new \DatetimeImmutable('2020-02-01 01:01:01'), 'created_at' => new \DatetimeImmutable('2020-02-01 01:01:01'), 'currency' => Rate::EUR, 'bet_amount_eur' => '1', 'session_type' => UserGameToken::SESSION_TYPE_PAIDSPINS, 'balance_type' => UserGameToken::BALANCE_TYPE_REAL],
        ]);

        $data = $this->cohortConfig(CohortConfig::MODE_BET_SUM)->getData();

        self::assertSame([
            [
                'day' => '2020-01-30',
                'entry_cnt' => '2',
                'day_0' => '1.00',
                'day_1' => '4.00',
                'day_2' => '5.00',
            ],
            [
                'day' => '2020-01-31',
                'entry_cnt' => '2',
                'day_0' => '2.00',
                'day_1' => '3.00',
                'day_2' => null,
            ],
            [
                'day' => '2020-02-01',
                'entry_cnt' => '2',
                'day_0' => '1.00',
                'day_1' => null,
                'day_2' => null,
            ]
        ], $data);
    }

    public function testStatsMode(): void
    {
        $this->usersRecords();

        $this->haveRecords(UserTransactions::class, [
            ['transaction_id' => '1', 'site_id' => Site::GGB, 'user_id' => 1, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-30 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-01-30 01:01:01')],
            ['transaction_id' => '2', 'site_id' => Site::GGB, 'user_id' => 1, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-30 01:01:02'), 'updated_at' => new \DatetimeImmutable('2020-01-30 01:01:02')],
            ['transaction_id' => '3', 'site_id' => Site::GGB, 'user_id' => 1, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_OUT, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_OUT, 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:03'), 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:03')],
            ['transaction_id' => '4', 'site_id' => Site::GGB, 'user_id' => 1, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:02'), 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:02')],
            ['transaction_id' => '5', 'site_id' => Site::GGB, 'user_id' => 1, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-02-01 01:01:03'), 'updated_at' => new \DatetimeImmutable('2020-02-01 01:01:03')],

            ['transaction_id' => '6', 'site_id' => Site::GGB, 'user_id' => 2, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:01')],
            ['transaction_id' => '7', 'site_id' => Site::GGB, 'user_id' => 2, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-02-01 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-02-01 01:01:01')],

            ['transaction_id' => '8', 'site_id' => Site::GGB, 'user_id' => 3, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:01')],
            ['transaction_id' => '9', 'site_id' => Site::GGB, 'user_id' => 3, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-01-31 01:01:01')],
            ['transaction_id' => '10', 'site_id' => Site::GGB, 'user_id' => 4, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-01-02 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-01-02 01:01:01')],

            ['transaction_id' => '11', 'site_id' => Site::GGB, 'user_id' => 5, 'ext_type' => UserTransaction::EXT_TYPE_NORMAL, 'status' => UserTransaction::STATUS_SUCCESS, 'dir' => UserTransaction::DIR_IN, 'amount_eur' => '1', 'op_id' => UserTransaction::OP_IN, 'created_at' => new \DatetimeImmutable('2020-02-01 01:01:01'), 'updated_at' => new \DatetimeImmutable('2020-02-01 01:01:01')],
        ]);

        $data = $this->cohortConfig(CohortConfig::MODE_DEP_SUM)->getdata();

        self::assertSame([
            [
                'day' => '2020-01-30',
                'entry_cnt' => '2',
                'day_0' => '2.00',
                'day_1' => '4.00',
                'day_2' => '6.00',
            ],
            [
                'day' => '2020-01-31',
                'entry_cnt' => '2',
                'day_0' => '2.00',
                'day_1' => '2.00',
                'day_2' => null,
            ],
            [
                'day' => '2020-02-01',
                'entry_cnt' => '2',
                'day_0' => '1.00',
                'day_1' => null,
                'day_2' => null,
            ]
        ], $data);
    }

    public function testLoginMode(): void
    {
        $this->usersRecords();

        $this->haveRecords(UserLogins::class, [
            ['site_id' => Site::GGB, 'user_id' => 1, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-30 01:01:01'), 'login_id' => '1'],
            ['site_id' => Site::GGB, 'user_id' => 1, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-30 01:01:02'), 'login_id' => '2'],
            ['site_id' => Site::GGB, 'user_id' => 1, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-31 01:01:03'), 'login_id' => '3'],
            ['site_id' => Site::GGB, 'user_id' => 1, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-31 01:01:04'), 'login_id' => '4'],
            ['site_id' => Site::GGB, 'user_id' => 1, 'success' => true, 'date' => new \DatetimeImmutable('2020-02-01 01:01:05'), 'login_id' => '5'],
            ['site_id' => Site::GGB, 'user_id' => 2, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'login_id' => '6'],
            ['site_id' => Site::GGB, 'user_id' => 2, 'success' => true, 'date' => new \DatetimeImmutable('2020-02-01 01:01:01'), 'login_id' => '7'],
            ['site_id' => Site::GGB, 'user_id' => 3, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'login_id' => '8'],
            ['site_id' => Site::GGB, 'user_id' => 3, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-31 01:01:01'), 'login_id' => '9'],
            ['site_id' => Site::GGB, 'user_id' => 4, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-02 01:01:01'), 'login_id' => '10'],
            ['site_id' => Site::GGB, 'user_id' => 5, 'success' => true, 'date' => new \DatetimeImmutable('2020-01-01 01:01:01'), 'login_id' => '11'],
        ]);

        $data = $this->cohortConfig(CohortConfig::MODE_LOGIN_RETENTION_PERCENT)->getdata();

        self::assertSame([
            [
                'day' => '2020-01-30',
                'entry_cnt' => '2',
                'day_0' => '50.0',
                'day_1' => '100.0',
                'day_2' => '100.0',
            ],
            [
                'day' => '2020-01-31',
                'entry_cnt' => '2',
                'day_0' => '50.0',
                'day_1' => '0.0',
                'day_2' => null,
            ],
            [
                'day' => '2020-02-01',
                'entry_cnt' => '2',
                'day_0' => '0.0',
                'day_1' => null,
                'day_2' => null,
            ],
        ], $data);
    }

    private function cohortConfig(string $metric): CohortConfig
    {
        $config = $this->reportConfig(CohortConfig::class);
        self::assertInstanceOf(CohortConfig::class, $config);

        $config->loadAndValidateOrException([
            'metrics' => [$metric],
            'isTotals' => true,
            'isHtmlVersion' => true,
            'groups' => ['day'],
            'split' => 'day',
            'filters' => [
                ['date', DateHelper::range('2020-01-30', '2020-02-01')],
                ['filter_type', CohortEntryTypeColumn::ENTRY_TYPE_REG],
                ['site_id', [Site::GGB]],
                ['currency', Rate::EUR],
            ],
        ]);

        return $config;
    }

    private function usersRecords(): void
    {
        $this->haveRecords(Users::class, [
            ['site_id' => Site::GGB, 'user_id' => 1, 'date' => new \DatetimeImmutable('2020-01-30 00:00:00')],
            ['site_id' => Site::GGB, 'user_id' => 2, 'date' => new \DatetimeImmutable('2020-01-30 00:00:00')],
            ['site_id' => Site::GGB, 'user_id' => 3, 'date' => new \DatetimeImmutable('2020-01-31 00:00:00')],
            ['site_id' => Site::GGB, 'user_id' => 4, 'date' => new \DatetimeImmutable('2020-01-31 00:00:00')],
            ['site_id' => Site::GGB, 'user_id' => 5, 'date' => new \DatetimeImmutable('2020-02-01 00:00:00')],
            ['site_id' => Site::GGB, 'user_id' => 6, 'date' => new \DatetimeImmutable('2020-02-01 00:00:00')],
        ]);
    }
}
